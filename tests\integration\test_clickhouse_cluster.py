"""Integration tests for ClickHouse cluster functionality."""

import pytest
from fastapi.testclient import TestClient
import uuid
from unittest.mock import patch, MagicMock

from magic_gateway.main import app
from magic_gateway.db.connection_manager import clickhouse_registry


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest.fixture
def mock_auth_token():
    """Mock authentication token for API requests."""
    return "Bearer test_token"


@pytest.fixture
def mock_clickhouse_handler():
    """Mock the ClickHouseHandler to avoid actual database calls."""
    with patch("magic_gateway.api.v1.endpoints.clickhouse.ClickHouseHandler") as mock:
        # Configure the mock to return test data
        mock.execute_query.return_value = (
            [{"id": 1, "name": "Test"}],  # rows
            ["id", "name"],  # columns
        )
        mock.execute_command.return_value = None
        yield mock


@pytest.fixture
def mock_get_current_active_user():
    """Mock the get_current_active_user dependency."""
    with patch(
        "magic_gateway.api.v1.endpoints.clickhouse.get_current_active_user"
    ) as mock:
        mock.return_value = {"username": "test_user", "is_admin": True}
        yield mock


@pytest.fixture
def mock_registry():
    """Mock the ClickHouse connection registry."""
    with patch("magic_gateway.db.clickhouse_handler.clickhouse_registry") as mock:
        # Configure the mock registry
        mock.get_manager.return_value = MagicMock()
        mock.get_available_clusters.return_value = ["primary", "default_cluster"]
        yield mock


def test_execute_query_primary_cluster(
    client,
    mock_auth_token,
    mock_clickhouse_handler,
    mock_get_current_active_user,
    mock_registry,
):
    """Test executing a query against the primary ClickHouse server."""
    # Set up the request
    headers = {"Authorization": mock_auth_token}
    payload = {"query": "SELECT * FROM test_table", "params": {"param1": "value1"}}

    # Execute the request
    response = client.post("/api/v1/clickhouse/query", json=payload, headers=headers)

    # Verify the response
    assert response.status_code == 200
    data = response.json()
    assert data["rows"] == [{"id": 1, "name": "Test"}]
    assert data["columns"] == ["id", "name"]
    assert data["row_count"] == 1
    assert data["database_type"] == "clickhouse"
    assert data["status"] == "completed"
    assert data["cluster"] is None  # No cluster specified, should be None

    # Verify that the ClickHouseHandler was called correctly
    mock_clickhouse_handler.execute_query.assert_called_once_with(
        query="SELECT * FROM test_table",
        params={"param1": "value1"},
        query_id=str(uuid.UUID(data["request_id"])),
        allow_write=False,
        cluster=None,  # No cluster specified
    )

    # Verify that the registry was used to get the primary manager
    mock_registry.get_manager.assert_called_once_with(None)


def test_execute_query_specific_cluster(
    client,
    mock_auth_token,
    mock_clickhouse_handler,
    mock_get_current_active_user,
    mock_registry,
):
    """Test executing a query against a specific ClickHouse cluster."""
    # Set up the request
    headers = {"Authorization": mock_auth_token}
    payload = {
        "query": "SELECT * FROM test_table",
        "params": {"param1": "value1"},
        "cluster": "default_cluster",
    }

    # Execute the request
    response = client.post("/api/v1/clickhouse/query", json=payload, headers=headers)

    # Verify the response
    assert response.status_code == 200
    data = response.json()
    assert data["rows"] == [{"id": 1, "name": "Test"}]
    assert data["columns"] == ["id", "name"]
    assert data["row_count"] == 1
    assert data["database_type"] == "clickhouse"
    assert data["status"] == "completed"
    assert data["cluster"] == "default_cluster"  # Cluster should be specified

    # Verify that the ClickHouseHandler was called correctly
    mock_clickhouse_handler.execute_query.assert_called_once_with(
        query="SELECT * FROM test_table",
        params={"param1": "value1"},
        query_id=str(uuid.UUID(data["request_id"])),
        allow_write=False,
        cluster="default_cluster",  # Cluster specified
    )

    # Verify that the registry was used to get the cluster manager
    mock_registry.get_manager.assert_called_once_with("default_cluster")


def test_execute_query_invalid_cluster(
    client,
    mock_auth_token,
    mock_clickhouse_handler,
    mock_get_current_active_user,
    mock_registry,
):
    """Test executing a query against an invalid ClickHouse cluster."""
    # Configure the mock registry to raise an exception for invalid cluster
    mock_registry.get_manager.side_effect = lambda cluster: (
        MagicMock()
        if cluster is None or cluster == "default_cluster"
        else Exception(f"Unknown ClickHouse cluster: {cluster}")
    )

    # Set up the request
    headers = {"Authorization": mock_auth_token}
    payload = {
        "query": "SELECT * FROM test_table",
        "params": {"param1": "value1"},
        "cluster": "invalid_cluster",
    }

    # Execute the request
    response = client.post("/api/v1/clickhouse/query", json=payload, headers=headers)

    # Verify the response
    assert response.status_code == 400
    data = response.json()
    assert "Unknown ClickHouse cluster" in data["detail"]

    # Verify that the registry was used to get the cluster manager
    mock_registry.get_manager.assert_called_once_with("invalid_cluster")


def test_execute_command_with_cluster(
    client,
    mock_auth_token,
    mock_clickhouse_handler,
    mock_get_current_active_user,
    mock_registry,
):
    """Test executing a command against a specific ClickHouse cluster."""
    # Set up the request
    headers = {"Authorization": mock_auth_token}
    payload = {
        "command": "CREATE TABLE test_table (id Int32, name String) ENGINE = MergeTree() ORDER BY id",
        "params": {"param1": "value1"},
        "cluster": "default_cluster",
    }

    # Execute the request
    response = client.post("/api/v1/clickhouse/command", json=payload, headers=headers)

    # Verify the response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "Command executed successfully"
    assert data["cluster"] == "default_cluster"

    # Verify that the ClickHouseHandler was called correctly
    mock_clickhouse_handler.execute_command.assert_called_once_with(
        command=payload["command"],
        params=payload["params"],
        query_id=str(uuid.UUID(data["request_id"])),
        cluster="default_cluster",
    )

    # Verify that the registry was used to get the cluster manager
    mock_registry.get_manager.assert_called_once_with("default_cluster")
