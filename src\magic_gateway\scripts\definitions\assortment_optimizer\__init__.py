"""Assortment Optimization script for the MagicGateway application."""

import os
import uuid
import tempfile
from typing import Dict, Any, Optional
from pathlib import Path

# Script metadata
METADATA = {
    "name": "Assortment Optimization",
    "description": "Performs assortment optimization analysis with configurable parameters",
    "author": "MagicGateway Team",
    "version": "1.0.0",
    "requires_admin": False,
}


def run(
    start_date: str,
    end_date: str,
    json_axis_id: int,
    json_flt_hh_id: Optional[int],
    total_position_number: int,
    rest_position_number: int,
    id_panel: int,
    clickhouse_manager=None,
    clickhouse_cluster_manager=None,
    postgres_manager=None,
    logs_manager=None,
    **kwargs,
) -> Dict[str, Any]:
    """
    Run the assortment optimization script.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        json_axis_id: JSON axis ID (integer)
        json_flt_hh_id: JSON filter household ID (optional integer)
        total_position_number: Total position number (required integer)
        rest_position_number: REST position number (-1 if no REST in axis)
        id_panel: Panel ID (integer)
        clickhouse_manager: ClickHouse connection manager (injected by script runner)
        clickhouse_cluster_manager: ClickHouse cluster connection manager (injected by script runner)
        postgres_manager: PostgreSQL connection manager (injected by script runner)
        logs_manager: Logs PostgreSQL connection manager (injected by script runner)
        **kwargs: Additional keyword arguments

    Returns:
        Dictionary with results including file path for download
    """
    try:
        # Import the modified ao module
        from .ao_modified import calc_assortment_optimizer_with_params

        # Generate unique filename for results
        file_id = str(uuid.uuid4())
        downloads_dir = Path("downloads")
        downloads_dir.mkdir(exist_ok=True)
        result_file_path = downloads_dir / f"assortment_optimizer_{file_id}.xlsx"

        # Execute the optimization with parameters
        calc_assortment_optimizer_with_params(
            start_date=start_date,
            end_date=end_date,
            json_axis_id=json_axis_id,
            json_flt_hh_id=json_flt_hh_id,
            total_position_number=total_position_number,
            rest_position_number=rest_position_number,
            id_panel=id_panel,
            output_file_path=str(result_file_path),
        )

        return {
            "status": "success",
            "message": "Assortment optimization completed successfully",
            "result_file_id": file_id,
            "download_url": f"/api/v1/scripts/download/{file_id}",
            "parameters": {
                "start_date": start_date,
                "end_date": end_date,
                "json_axis_id": json_axis_id,
                "json_flt_hh_id": json_flt_hh_id,
                "total_position_number": total_position_number,
                "rest_position_number": rest_position_number,
                "id_panel": id_panel,
            },
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Assortment optimization failed: {str(e)}",
            "error_type": type(e).__name__,
        }
