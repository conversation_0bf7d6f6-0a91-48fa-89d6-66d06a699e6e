# magic_gateway/core/lifespan.py

from contextlib import asynccontextmanager
from typing import As<PERSON><PERSON><PERSON>ator
import asyncio  # Import asyncio

from fastapi import FastAPI

from magic_gateway.core.config import settings
from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import (
    clickhouse_registry,
    postgres_connection_manager,
    logs_connection_manager,
)

# Removed PostgresHandler import as it's no longer needed
from magic_gateway.tracking.service import RequestTrackingService  # Import service
from magic_gateway.api.middleware.request_tracking import set_request_tracker_instance  # Import setter
from magic_gateway.monitoring.service import (
    connection_pool_monitoring_service,
)  # Import monitoring service
from magic_gateway.utils.temp_file_manager import (
    temp_file_manager,
)  # Import temp file manager


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan context manager.
    """
    log.info("Application starting up...")

    # Initialize Request Tracking Service
    # You could potentially pass dependencies like the DB handler if needed,
    # but here it uses the static methods of <PERSON>g<PERSON><PERSON><PERSON><PERSON> directly.
    request_tracker = RequestTrackingService()
    app.state.request_tracker_service = request_tracker  # Store in app state
    set_request_tracker_instance(request_tracker)  # Set for middleware global access
    log.info("Request Tracking Service initialized.")

    # Initialize Database Connections
    postgres_initialized = False
    clickhouse_initialized = False
    logs_initialized = False
    postgres_init_task = None
    clickhouse_init_task = None
    logs_init_task = None

    # Start initializations concurrently
    try:
        log.info("Initializing database connection pools concurrently...")
        postgres_init_task = asyncio.create_task(
            postgres_connection_manager.initialize()
        )
        clickhouse_init_task = asyncio.create_task(clickhouse_registry.initialize())
        logs_init_task = asyncio.create_task(logs_connection_manager.initialize())

        # Wait for PostgreSQL initialization
        try:
            await postgres_init_task
            postgres_initialized = True
            log.info("PostgreSQL connection pool initialization successful.")
            # Removed automatic table creation for PostgreSQL metadata tables
        except Exception as pg_e:
            log.error(f"Failed to initialize PostgreSQL pool during startup: {pg_e}")
            postgres_initialized = False  # Handled by the exception catch

        # Wait for ClickHouse initialization
        try:
            await clickhouse_init_task
            clickhouse_initialized = True

            # Verify registry initialization
            if not clickhouse_registry.initialized:
                log.warning(
                    "ClickHouse registry reports as not initialized after initialization task completed"
                )
            else:
                log.info(
                    f"ClickHouse registry initialized successfully with managers: {clickhouse_registry.get_available_clusters()}"
                )

            # Log information about initialized clusters
            if settings.has_clickhouse_cluster:
                cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
                log.info(
                    f"ClickHouse connection registry initialized with primary and '{cluster_name}' cluster."
                )
                log.info(
                    f"ClickHouse cluster '{cluster_name}' configured at {settings.CLICKHOUSE_CLUSTER_HOST}:{settings.CLICKHOUSE_CLUSTER_PORT}/{settings.CLICKHOUSE_CLUSTER_DATABASE}"
                )
            else:
                log.info(
                    "ClickHouse connection registry initialized with primary connection only."
                )
                log.info("ClickHouse cluster configuration not found or incomplete.")

            # Log primary connection details
            log.info(
                f"Primary ClickHouse connection configured at {settings.CLICKHOUSE_HOST}:{settings.CLICKHOUSE_PORT}/{settings.CLICKHOUSE_DATABASE}"
            )

            # Log available clusters
            available_clusters = clickhouse_registry.get_available_clusters()
            log.info(f"Available ClickHouse clusters: {', '.join(available_clusters)}")
        except Exception as ch_e:
            log.error(
                f"Failed to initialize ClickHouse registry during startup: {ch_e}"
            )
            clickhouse_initialized = False

        # Wait for Logs database initialization
        try:
            await logs_init_task
            logs_initialized = True
            log.info("Logs database connection initialization successful.")
        except Exception as logs_e:
            log.critical(
                f"Failed to initialize Logs database pool during startup: {logs_e}. Request tracking will be unavailable."
            )
            logs_initialized = False  # Handled by the exception catch

    except Exception as init_e:
        # Catch potential errors during task creation itself
        log.critical(f"Error during concurrent DB initialization setup: {init_e}")

    # Report status
    if not postgres_initialized and not clickhouse_initialized and not logs_initialized:
        log.critical("CRITICAL: All database connections failed to initialize.")
    else:
        if not postgres_initialized:
            log.warning(
                "WARNING: PostgreSQL failed to initialize. Some functionality may be unavailable."
            )
        if not clickhouse_initialized:
            log.warning(
                "WARNING: ClickHouse connection registry failed to initialize. ClickHouse queries will be unavailable."
            )
        if not logs_initialized:
            log.warning(
                "WARNING: Logs database failed to initialize. Request tracking and history will be unavailable."
            )
        if postgres_initialized and clickhouse_initialized and logs_initialized:
            log.info("All configured database connections initialized successfully.")

    # Store availability state if needed
    app.state.postgres_available = postgres_initialized
    app.state.clickhouse_available = clickhouse_initialized
    app.state.clickhouse_cluster_available = (
        clickhouse_initialized and settings.has_clickhouse_cluster
    )

    # Store available clusters in app state for health checks and monitoring
    if clickhouse_initialized:
        app.state.clickhouse_clusters = clickhouse_registry.get_available_clusters()
    else:
        app.state.clickhouse_clusters = []

    app.state.logs_available = logs_initialized

    # Initialize connection pool monitoring service if enabled
    if settings.ENABLE_CONNECTION_POOL_MONITORING:
        try:
            log.info("Initializing connection pool monitoring service...")
            # Start the monitoring service with the configured collection interval
            await connection_pool_monitoring_service.start(
                collection_interval=settings.CONNECTION_POOL_MONITORING_INTERVAL
            )
            log.info(
                f"Connection pool monitoring service started successfully with {settings.CONNECTION_POOL_MONITORING_INTERVAL}s interval."
            )
            app.state.monitoring_service_available = True
        except Exception as mon_e:
            log.error(
                f"Failed to initialize connection pool monitoring service: {mon_e}"
            )
            app.state.monitoring_service_available = False
    else:
        log.info("Connection pool monitoring is disabled in settings.")
        app.state.monitoring_service_available = False

    # Initialize temporary file cleanup task
    try:
        # Get configuration settings for file cleanup
        cleanup_interval_minutes = settings.TEMP_FILE_CLEANUP_INTERVAL_MINUTES
        retention_hours = settings.TEMP_FILE_RETENTION_HOURS

        log.info(
            f"Initializing temporary file cleanup task with settings: "
            f"interval={cleanup_interval_minutes} minutes, retention={retention_hours} hours, "
            f"storage_path='{settings.TEMP_FILE_STORAGE_PATH}', max_size={settings.TEMP_FILE_MAX_SIZE_MB}MB"
        )

        async def periodic_temp_file_cleanup():
            while True:
                try:
                    log.info(
                        f"Running scheduled cleanup of temporary files older than {retention_hours} hours"
                    )
                    cleaned_count = temp_file_manager.cleanup_old_files(retention_hours)
                    log.info(
                        f"Temporary file cleanup completed: {cleaned_count} files removed"
                    )
                except Exception as e:
                    log.error(f"Error during temporary file cleanup: {e}")

                # Wait for the next cleanup interval
                await asyncio.sleep(cleanup_interval_minutes * 60)

        # Start the cleanup task
        app.state.temp_file_cleanup_task = asyncio.create_task(
            periodic_temp_file_cleanup()
        )
        log.info(
            f"Temporary file cleanup task started with {cleanup_interval_minutes} minute interval"
        )
    except Exception as e:
        log.error(f"Failed to initialize temporary file cleanup task: {e}")
        app.state.temp_file_cleanup_task = None

    # Mark startup as complete to enable debug logging for connection managers
    try:
        postgres_connection_manager.mark_startup_complete()
        logs_connection_manager.mark_startup_complete()
        if clickhouse_registry.initialized:
            # Mark startup complete for all ClickHouse managers in the registry
            for cluster_name in clickhouse_registry.get_available_clusters():
                manager = clickhouse_registry.get_manager(cluster_name)
                manager.mark_startup_complete()
        log.debug("Connection managers marked as startup complete - debug logging enabled")
    except Exception as e:
        log.warning(f"Failed to mark startup complete for connection managers: {e}")

    log.info("Application startup sequence complete.")
    yield
    log.info("Application shutting down...")

    # Close pools concurrently
    close_tasks = []

    # Close the ClickHouse registry which manages all connection managers
    if clickhouse_registry.initialized:
        close_tasks.append(asyncio.create_task(clickhouse_registry.close()))
        log.info(
            "Closing ClickHouse connection registry with all managed connections..."
        )

    if postgres_connection_manager.initialized:
        close_tasks.append(asyncio.create_task(postgres_connection_manager.close()))
    else:
        log.info("PostgreSQL pool was not initialized, skipping close.")

    if logs_connection_manager.initialized:
        close_tasks.append(asyncio.create_task(logs_connection_manager.close()))
    else:
        log.info("Logs database pool was not initialized, skipping close.")

    if close_tasks:
        try:
            await asyncio.gather(*close_tasks)
            log.info("Database connection pools closed.")
        except Exception as close_e:
            log.error(
                f"Error closing database pools during shutdown: {close_e}",
                exc_info=True,
            )

    # Stop the monitoring service
    if (
        hasattr(app.state, "monitoring_service_available")
        and app.state.monitoring_service_available
    ):
        try:
            log.info("Stopping connection pool monitoring service...")
            await connection_pool_monitoring_service.stop()
            log.info("Connection pool monitoring service stopped.")
        except Exception as mon_e:
            log.error(f"Error stopping monitoring service: {mon_e}", exc_info=True)

    # Stop the temporary file cleanup task
    if (
        hasattr(app.state, "temp_file_cleanup_task")
        and app.state.temp_file_cleanup_task
    ):
        try:
            log.info("Stopping temporary file cleanup task...")
            app.state.temp_file_cleanup_task.cancel()
            try:
                await app.state.temp_file_cleanup_task
            except asyncio.CancelledError:
                pass
            log.info("Temporary file cleanup task stopped.")
        except Exception as e:
            log.error(f"Error stopping temporary file cleanup task: {e}", exc_info=True)

    log.info("Application shutdown sequence complete.")
