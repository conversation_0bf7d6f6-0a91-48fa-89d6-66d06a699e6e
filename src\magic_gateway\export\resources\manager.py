"""
Export Resource Manager for guaranteed cleanup of temporary resources.

This module provides the ExportResourceManager class that handles lifecycle
management of temporary files, streams, and other resources used during
export operations with guaranteed cleanup via async context managers.
"""

import asyncio
import uuid
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
from contextlib import asynccontextmanager

from magic_gateway.core.logging_config import log
from magic_gateway.utils.temp_file_manager import TempFileManager


class ExportResourceManager:
    """
    Manages temporary resources during export operations with guaranteed cleanup.
    
    This class provides centralized resource lifecycle management for export operations,
    ensuring that all temporary files, streams, and other resources are properly
    cleaned up even in error scenarios.
    
    Features:
    - Async context manager support for guaranteed cleanup
    - Registration of temporary files and streams
    - Integration with existing TempFileManager
    - Resource tracking with unique identifiers
    - Comprehensive error handling and logging
    """

    def __init__(self, temp_file_manager: Optional[TempFileManager] = None):
        """
        Initialize the ExportResourceManager.
        
        Args:
            temp_file_manager: Optional TempFileManager instance. If None,
                             uses the global temp_file_manager instance.
        """
        if temp_file_manager is None:
            from magic_gateway.utils.temp_file_manager import temp_file_manager as global_tfm
            self.temp_file_manager = global_tfm
        else:
            self.temp_file_manager = temp_file_manager
        self.request_id = str(uuid.uuid4())
        
        # Resource tracking
        self._temp_files: Dict[str, str] = {}  # resource_id -> file_id
        self._temp_file_paths: Dict[str, Path] = {}  # resource_id -> file_path
        self._streams: Dict[str, Any] = {}  # resource_id -> stream object
        self._cleanup_callbacks: Dict[str, callable] = {}  # resource_id -> cleanup function
        self._is_cleaned_up = False
        
        log.debug(f"Initialized ExportResourceManager with request_id: {self.request_id}")

    async def __aenter__(self):
        """Async context manager entry."""
        log.debug(f"Entering ExportResourceManager context: {self.request_id}")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with guaranteed cleanup."""
        if exc_type:
            log.warning(
                f"ExportResourceManager exiting with exception: {exc_type.__name__}: {exc_val}"
            )
        else:
            log.debug(f"ExportResourceManager exiting normally: {self.request_id}")
        
        await self.cleanup_all()
        
        # Don't suppress exceptions
        return False

    def register_temp_file(
        self, 
        file_path: Union[str, Path], 
        resource_id: Optional[str] = None,
        file_id: Optional[str] = None
    ) -> str:
        """
        Register a temporary file for cleanup.
        
        Args:
            file_path: Path to the temporary file
            resource_id: Optional unique identifier for this resource
            file_id: Optional file ID from TempFileManager
            
        Returns:
            Resource ID for tracking this file
            
        Raises:
            ValueError: If resource is already registered or file doesn't exist
        """
        if self._is_cleaned_up:
            raise ValueError("Cannot register resources after cleanup")
            
        resource_id = resource_id or str(uuid.uuid4())
        file_path = Path(file_path)
        
        if resource_id in self._temp_file_paths:
            raise ValueError(f"Resource ID {resource_id} already registered")
            
        if not file_path.exists():
            raise ValueError(f"File does not exist: {file_path}")
        
        self._temp_file_paths[resource_id] = file_path
        if file_id:
            self._temp_files[resource_id] = file_id
            
        log.debug(f"Registered temp file: {file_path} with resource_id: {resource_id}")
        return resource_id

    def register_stream(
        self, 
        stream: Any, 
        resource_id: Optional[str] = None,
        cleanup_callback: Optional[callable] = None
    ) -> str:
        """
        Register a stream or other resource for cleanup.
        
        Args:
            stream: Stream or resource object to register
            resource_id: Optional unique identifier for this resource
            cleanup_callback: Optional custom cleanup function
            
        Returns:
            Resource ID for tracking this stream
            
        Raises:
            ValueError: If resource is already registered
        """
        if self._is_cleaned_up:
            raise ValueError("Cannot register resources after cleanup")
            
        resource_id = resource_id or str(uuid.uuid4())
        
        if resource_id in self._streams:
            raise ValueError(f"Resource ID {resource_id} already registered")
            
        self._streams[resource_id] = stream
        if cleanup_callback:
            self._cleanup_callbacks[resource_id] = cleanup_callback
            
        log.debug(f"Registered stream with resource_id: {resource_id}")
        return resource_id

    def register_temp_file_from_manager(
        self,
        content: Any,
        extension: str = None,
        prefix: str = None,
        resource_id: Optional[str] = None
    ) -> str:
        """
        Create and register a temporary file using TempFileManager.
        
        Args:
            content: Content to save to the temporary file
            extension: File extension without the dot
            prefix: File prefix
            resource_id: Optional unique identifier for this resource
            
        Returns:
            Resource ID for tracking this file
            
        Raises:
            OSError: If file cannot be created
            ValueError: If resource is already registered
        """
        if self._is_cleaned_up:
            raise ValueError("Cannot register resources after cleanup")
            
        resource_id = resource_id or str(uuid.uuid4())
        
        if resource_id in self._temp_files or resource_id in self._temp_file_paths:
            raise ValueError(f"Resource ID {resource_id} already registered")
        
        try:
            # Determine if this is an export operation and use larger size limit
            from magic_gateway.core.config import settings
            
            # Use larger size limit for export operations (identified by prefix)
            is_export_operation = (
                prefix and any(export_prefix in prefix.lower() for export_prefix in [
                    "export_", "parquet_", "large_", "job_", "result_"
                ])
            )
            
            max_size_mb = (
                settings.TEMP_FILE_EXPORT_MAX_SIZE_MB if is_export_operation 
                else None  # Use default
            )
            
            if is_export_operation:
                log.debug(f"Using export size limit ({max_size_mb} MB) for prefix: {prefix}")
            
            # Create file using TempFileManager with appropriate size limit
            file_id = self.temp_file_manager.save_result_file(
                content=content,
                extension=extension,
                prefix=prefix,
                max_size_mb=max_size_mb
            )
            
            # Get the file path
            file_path = self.temp_file_manager.get_file_path(file_id)
            if not file_path:
                raise OSError(f"Failed to get file path for file_id: {file_id}")
            
            # Register the file_id (primary) - path will be retrieved via TempFileManager
            self._temp_files[resource_id] = file_id
            
            log.debug(
                f"Created and registered temp file: {file_path.name} "
                f"with resource_id: {resource_id}, file_id: {file_id}"
            )
            return resource_id
            
        except Exception as e:
            log.error(f"Failed to create temp file with resource_id {resource_id}: {e}")
            raise

    def get_temp_file_path(self, resource_id: str) -> Optional[Path]:
        """
        Get the file path for a registered temporary file.
        
        Args:
            resource_id: Resource ID of the temporary file
            
        Returns:
            Path object if file exists, None otherwise
        """
        if resource_id in self._temp_file_paths:
            file_path = self._temp_file_paths[resource_id]
            if file_path.exists():
                return file_path
            else:
                log.warning(f"Registered file no longer exists: {file_path}")
                return None
        
        # Try to get from TempFileManager if we have a file_id
        if resource_id in self._temp_files:
            file_id = self._temp_files[resource_id]
            return self.temp_file_manager.get_file_path(file_id)
            
        return None

    def get_stream(self, resource_id: str) -> Optional[Any]:
        """
        Get a registered stream.
        
        Args:
            resource_id: Resource ID of the stream
            
        Returns:
            Stream object if registered, None otherwise
        """
        return self._streams.get(resource_id)

    def list_resources(self) -> Dict[str, Dict[str, Any]]:
        """
        List all registered resources.
        
        Returns:
            Dictionary mapping resource IDs to resource information
        """
        resources = {}
        
        # Add temporary files from direct registration
        for resource_id, file_path in self._temp_file_paths.items():
            file_id = self._temp_files.get(resource_id)
            resources[resource_id] = {
                "type": "temp_file",
                "file_path": str(file_path),
                "file_id": file_id,
                "exists": file_path.exists() if file_path else False
            }
        
        # Add temporary files from TempFileManager (not already in _temp_file_paths)
        for resource_id, file_id in self._temp_files.items():
            if resource_id not in resources:  # Not already added above
                file_path = self.temp_file_manager.get_file_path(file_id)
                resources[resource_id] = {
                    "type": "temp_file",
                    "file_path": str(file_path) if file_path else None,
                    "file_id": file_id,
                    "exists": file_path.exists() if file_path else False
                }
        
        # Add streams
        for resource_id, stream in self._streams.items():
            resources[resource_id] = {
                "type": "stream",
                "stream_type": type(stream).__name__,
                "has_cleanup_callback": resource_id in self._cleanup_callbacks
            }
            
        return resources

    async def cleanup_resource(self, resource_id: str) -> bool:
        """
        Clean up a specific resource.
        
        Args:
            resource_id: Resource ID to clean up
            
        Returns:
            True if resource was successfully cleaned up, False otherwise
        """
        success = True
        
        # Clean up temporary file
        if resource_id in self._temp_files:
            file_id = self._temp_files[resource_id]
            try:
                cleanup_success = self.temp_file_manager.cleanup_file(file_id)
                if cleanup_success:
                    log.debug(f"Cleaned up temp file with file_id: {file_id}")
                else:
                    log.warning(f"Failed to cleanup temp file with file_id: {file_id}")
                    success = False
            except Exception as e:
                log.error(f"Error cleaning up temp file {file_id}: {e}")
                success = False
            finally:
                del self._temp_files[resource_id]
        
        # Clean up file path reference
        if resource_id in self._temp_file_paths:
            file_path = self._temp_file_paths[resource_id]
            try:
                if file_path.exists():
                    file_path.unlink()
                    log.debug(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                log.error(f"Error cleaning up file {file_path}: {e}")
                success = False
            finally:
                del self._temp_file_paths[resource_id]
        
        # Clean up stream
        if resource_id in self._streams:
            stream = self._streams[resource_id]
            try:
                # Use custom cleanup callback if available
                if resource_id in self._cleanup_callbacks:
                    cleanup_callback = self._cleanup_callbacks[resource_id]
                    if asyncio.iscoroutinefunction(cleanup_callback):
                        await cleanup_callback(stream)
                    else:
                        cleanup_callback(stream)
                    del self._cleanup_callbacks[resource_id]
                else:
                    # Try common cleanup methods
                    if hasattr(stream, 'aclose') and callable(getattr(stream, 'aclose')):
                        await stream.aclose()
                    elif hasattr(stream, 'close') and callable(getattr(stream, 'close')):
                        stream.close()
                        
                log.debug(f"Cleaned up stream with resource_id: {resource_id}")
            except Exception as e:
                log.error(f"Error cleaning up stream {resource_id}: {e}")
                success = False
            finally:
                del self._streams[resource_id]
        
        return success

    async def cleanup_all(self) -> None:
        """
        Clean up all registered resources.
        
        This method is called automatically when exiting the async context manager
        and provides guaranteed cleanup even in error scenarios.
        """
        if self._is_cleaned_up:
            log.debug(f"Resources already cleaned up for request: {self.request_id}")
            return
        
        log.debug(f"Starting cleanup of all resources for request: {self.request_id}")
        
        # Get all resource IDs to avoid modifying dict during iteration
        all_resource_ids = set()
        all_resource_ids.update(self._temp_files.keys())
        all_resource_ids.update(self._temp_file_paths.keys())
        all_resource_ids.update(self._streams.keys())
        
        cleanup_results = []
        for resource_id in all_resource_ids:
            try:
                success = await self.cleanup_resource(resource_id)
                cleanup_results.append(success)
            except Exception as e:
                log.error(f"Error during cleanup of resource {resource_id}: {e}")
                cleanup_results.append(False)
        
        # Mark as cleaned up
        self._is_cleaned_up = True
        
        # Log summary
        total_resources = len(cleanup_results)
        successful_cleanups = sum(cleanup_results)
        failed_cleanups = total_resources - successful_cleanups
        
        if failed_cleanups > 0:
            log.warning(
                f"Cleanup completed for request {self.request_id}: "
                f"{successful_cleanups}/{total_resources} successful, "
                f"{failed_cleanups} failed"
            )
        else:
            log.debug(
                f"Cleanup completed successfully for request {self.request_id}: "
                f"{successful_cleanups} resources cleaned up"
            )

    @property
    def is_cleaned_up(self) -> bool:
        """Check if resources have been cleaned up."""
        return self._is_cleaned_up

    @property
    def resource_count(self) -> int:
        """Get the total number of registered resources."""
        # Count unique resource IDs across all tracking dictionaries
        all_resource_ids = set()
        all_resource_ids.update(self._temp_files.keys())
        all_resource_ids.update(self._temp_file_paths.keys())
        all_resource_ids.update(self._streams.keys())
        return len(all_resource_ids)


@asynccontextmanager
async def export_resource_context(temp_file_manager: Optional[TempFileManager] = None):
    """
    Async context manager factory for ExportResourceManager.
    
    Args:
        temp_file_manager: Optional TempFileManager instance
        
    Yields:
        ExportResourceManager instance with guaranteed cleanup
        
    Example:
        async with export_resource_context() as resource_manager:
            resource_id = resource_manager.register_temp_file_from_manager(
                content="test data",
                extension="csv"
            )
            # Resources are automatically cleaned up on exit
    """
    async with ExportResourceManager(temp_file_manager) as manager:
        yield manager