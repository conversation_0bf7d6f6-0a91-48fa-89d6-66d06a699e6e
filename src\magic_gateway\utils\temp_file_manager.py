"""Universal temporary file management utilities for MagicGateway."""

import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Optional, List
import json
import pickle
from enum import Enum

from magic_gateway.core.logging_config import log
from magic_gateway.core.config import settings


class FileCategory(Enum):
    """Categories of temporary files for different purposes."""

    SCRIPT_RESULT = "script_result"
    EXPORT = "export"
    UPLOAD = "upload"
    PROCESSING = "processing"
    CACHE = "cache"
    REPORT = "report"


class TempFileManager:
    """Universal manager for temporary files with automatic cleanup and flexible naming."""

    def __init__(self, base_directory: Optional[str] = None):
        """
        Initialize the temporary file manager.

        Args:
            base_directory: Base directory for storing temporary files. If None, uses settings.TEMP_FILE_STORAGE_PATH
        """
        # Use the configured path from settings if not explicitly provided
        self.base_directory = Path(base_directory or settings.TEMP_FILE_STORAGE_PATH)
        self.base_directory.mkdir(exist_ok=True)

        # Get settings from configuration
        self.retention_hours = getattr(settings, "TEMP_FILE_RETENTION_HOURS", 24)
        self.cleanup_interval_minutes = getattr(
            settings, "TEMP_FILE_CLEANUP_INTERVAL_MINUTES", 60
        )
        self.max_file_size_mb = getattr(settings, "TEMP_FILE_MAX_SIZE_MB", 100)

        # Get allowed extensions, ensuring it's a list
        allowed_extensions = getattr(
            settings,
            "TEMP_FILE_ALLOWED_EXTENSIONS",
            [
                "xlsx",
                "csv",
                "json",
                "txt",
                "parquet",
                "zip",
                "pdf",
                "png",
                "jpg",
                "jpeg",
                "gif",
                "xml",
                "html",
            ],
        )
        if isinstance(allowed_extensions, str):
            self.allowed_extensions = [
                ext.strip() for ext in allowed_extensions.split(",")
            ]
        else:
            self.allowed_extensions = allowed_extensions

        # Default naming patterns for different categories
        self.default_patterns = {
            FileCategory.SCRIPT_RESULT: {
                "prefix": "script_result_",
                "extension": "xlsx",
                "include_timestamp": True,
                "include_uuid": True,
            },
            FileCategory.EXPORT: {
                "prefix": "export_",
                "extension": "xlsx",
                "include_timestamp": True,
                "include_uuid": True,
            },
            FileCategory.UPLOAD: {
                "prefix": "upload_",
                "extension": "tmp",
                "include_timestamp": True,
                "include_uuid": True,
            },
            FileCategory.PROCESSING: {
                "prefix": "processing_",
                "extension": "tmp",
                "include_timestamp": True,
                "include_uuid": True,
            },
            FileCategory.CACHE: {
                "prefix": "cache_",
                "extension": "json",
                "include_timestamp": False,
                "include_uuid": True,
            },
            FileCategory.REPORT: {
                "prefix": "report_",
                "extension": "pdf",
                "include_timestamp": True,
                "include_uuid": True,
            },
        }

        # Get cleanup behavior setting
        self.cleanup_downloaded_only = getattr(
            settings, "TEMP_FILE_CLEANUP_DOWNLOADED_ONLY", True
        )

    def generate_unique_filename(
        self,
        name: Optional[str] = None,
        extension: Optional[str] = None,
        prefix: Optional[str] = None,
        category: Optional[FileCategory] = None,
        include_timestamp: Optional[bool] = None,
        include_uuid: Optional[bool] = None,
        custom_pattern: Optional[str] = None,
    ) -> str:
        """
        Generate a unique filename with flexible naming patterns.

        Args:
            name: Base name for the file (optional)
            extension: File extension without the dot
            prefix: File prefix
            category: File category for default patterns
            include_timestamp: Whether to include timestamp
            include_uuid: Whether to include UUID
            custom_pattern: Custom pattern string with placeholders: {name}, {prefix}, {timestamp}, {uuid}, {extension}

        Returns:
            Unique filename string
        """
        # Use custom pattern if provided
        if custom_pattern:
            return self._generate_from_pattern(custom_pattern, name, extension, prefix)

        # Determine defaults based on category
        if category and category in self.default_patterns:
            pattern = self.default_patterns[category]
            extension = extension or pattern["extension"]
            prefix = prefix or pattern["prefix"]
            include_timestamp = (
                include_timestamp
                if include_timestamp is not None
                else pattern["include_timestamp"]
            )
            include_uuid = (
                include_uuid if include_uuid is not None else pattern["include_uuid"]
            )
        else:
            # Use generic defaults if no category specified
            extension = extension or "tmp"
            prefix = prefix or "temp_"
            include_timestamp = (
                include_timestamp if include_timestamp is not None else True
            )
            include_uuid = include_uuid if include_uuid is not None else True

        # Validate extension is allowed
        if extension not in self.allowed_extensions:
            log.warning(
                f"Extension '{extension}' not in allowed list {self.allowed_extensions}, using 'tmp'"
            )
            extension = "tmp"

        # Build filename components
        components = []

        if prefix:
            components.append(prefix.rstrip("_"))

        if name:
            # Sanitize the name
            sanitized_name = "".join(
                c if c.isalnum() or c in "_-" else "_" for c in name
            )
            components.append(sanitized_name)

        if include_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            components.append(timestamp)

        if include_uuid:
            unique_id = str(uuid.uuid4())[:8]
            components.append(unique_id)

        # Join components and add extension
        base_name = "_".join(components) if components else "temp_file"
        return f"{base_name}.{extension}"

    def _generate_from_pattern(
        self,
        pattern: str,
        name: Optional[str] = None,
        extension: Optional[str] = None,
        prefix: Optional[str] = None,
    ) -> str:
        """
        Generate filename from a custom pattern.

        Args:
            pattern: Pattern string with placeholders
            name: Base name
            extension: File extension
            prefix: File prefix

        Returns:
            Generated filename
        """
        # Prepare replacement values
        replacements = {
            "name": name or "file",
            "prefix": prefix or "",
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "uuid": str(uuid.uuid4())[:8],
            "extension": extension or "tmp",
        }

        # Sanitize name if provided
        if name:
            replacements["name"] = "".join(
                c if c.isalnum() or c in "_-" else "_" for c in name
            )

        try:
            filename = pattern.format(**replacements)
            # Ensure extension is valid
            ext = replacements["extension"]
            if ext not in self.allowed_extensions:
                log.warning(f"Extension '{ext}' not allowed, using 'tmp'")
                filename = filename.replace(f".{ext}", ".tmp")
            return filename
        except KeyError as e:
            log.error(f"Invalid pattern placeholder: {e}")
            # Fallback to simple generation
            return self.generate_unique_filename(
                name=name, extension=extension, prefix=prefix
            )

    def save_result_file(
        self,
        content: Any,
        file_id: Optional[str] = None,
        name: Optional[str] = None,
        extension: Optional[str] = None,
        prefix: Optional[str] = None,
        category: Optional[FileCategory] = None,
        custom_pattern: Optional[str] = None,
    ) -> str:
        """
        Save result content to a temporary file with flexible naming.

        Args:
            content: Content to save (can be string, dict, list, etc.)
            file_id: Optional specific file ID to use
            name: Base name for the file
            extension: File extension without the dot
            prefix: File prefix
            category: File category for default patterns
            custom_pattern: Custom naming pattern

        Returns:
            File ID for later retrieval

        Raises:
            OSError: If file cannot be written
            ValueError: If content exceeds maximum file size
        """
        if file_id is None:
            file_id = str(uuid.uuid4())

        filename = self.generate_unique_filename(
            name=name,
            extension=extension,
            prefix=prefix,
            category=category,
            custom_pattern=custom_pattern,
        )
        file_path = self.base_directory / filename

        try:
            # Check content size for string and bytes
            if isinstance(content, str):
                # Estimate string size in MB
                estimated_size_mb = len(content.encode("utf-8")) / (1024 * 1024)
                if estimated_size_mb > self.max_file_size_mb:
                    raise ValueError(
                        f"Content size ({estimated_size_mb:.2f} MB) exceeds maximum allowed size ({self.max_file_size_mb} MB)"
                    )

                # String content - write as text
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)

            elif isinstance(content, (dict, list)):
                # JSON serializable content
                json_content = json.dumps(content, indent=2, default=str)
                estimated_size_mb = len(json_content.encode("utf-8")) / (1024 * 1024)
                if estimated_size_mb > self.max_file_size_mb:
                    raise ValueError(
                        f"Content size ({estimated_size_mb:.2f} MB) exceeds maximum allowed size ({self.max_file_size_mb} MB)"
                    )

                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(json_content)

            elif isinstance(content, bytes):
                # Binary content
                estimated_size_mb = len(content) / (1024 * 1024)
                if estimated_size_mb > self.max_file_size_mb:
                    raise ValueError(
                        f"Content size ({estimated_size_mb:.2f} MB) exceeds maximum allowed size ({self.max_file_size_mb} MB)"
                    )

                with open(file_path, "wb") as f:
                    f.write(content)

            else:
                # Other objects - use pickle
                with open(file_path, "wb") as f:
                    pickle.dump(content, f)

                # Check file size after writing
                file_size_mb = file_path.stat().st_size / (1024 * 1024)
                if file_size_mb > self.max_file_size_mb:
                    # Remove the file if it's too large
                    file_path.unlink()
                    raise ValueError(
                        f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size ({self.max_file_size_mb} MB)"
                    )

            # Store mapping of file_id to actual filename
            self._store_file_mapping(file_id, filename)

            log.info(f"Saved temporary file: {filename} with ID: {file_id}")
            return file_id

        except ValueError as ve:
            log.error(f"File size validation failed for {filename}: {ve}")
            raise
        except Exception as e:
            log.error(f"Failed to save temporary file {filename}: {e}")
            raise OSError(f"Failed to save temporary file: {e}")

    def get_file_path(self, file_id: str) -> Optional[Path]:
        """
        Get the file path for a given file ID.

        Args:
            file_id: File ID to look up

        Returns:
            Path object if file exists, None otherwise
        """
        filename = self._get_filename_from_mapping(file_id)
        if not filename:
            return None

        file_path = self.base_directory / filename
        if file_path.exists():
            return file_path
        else:
            # Clean up stale mapping
            self._remove_file_mapping(file_id)
            return None

    def get_temp_path(
        self,
        file_id: str,
        name: Optional[str] = None,
        extension: Optional[str] = None,
        prefix: Optional[str] = None,
        category: Optional[FileCategory] = None,
        custom_pattern: Optional[str] = None,
    ) -> Path:
        """
        Get a temporary file path for a given file ID with flexible naming.

        This method generates a path for a temporary file that may not exist yet.
        Unlike get_file_path(), this doesn't check if the file exists.

        Args:
            file_id: File ID to generate path for
            name: Base name for the file
            extension: File extension without the dot
            prefix: File prefix
            category: File category for default patterns
            custom_pattern: Custom naming pattern

        Returns:
            Path object for the temporary file
        """
        # Generate a filename for this file_id if it doesn't exist
        filename = self._get_filename_from_mapping(file_id)
        if not filename:
            # Generate a new filename for this file_id with flexible naming
            filename = self.generate_unique_filename(
                name=name,
                extension=extension,
                prefix=prefix,
                category=category,
                custom_pattern=custom_pattern,
            )
            self._store_file_mapping(file_id, filename)

        return self.base_directory / filename

    def cleanup_file(self, file_id: str) -> bool:
        """
        Clean up a specific temporary file.

        Args:
            file_id: File ID to clean up

        Returns:
            True if file was successfully deleted, False otherwise
        """
        # Debug logging to understand the mapping issue
        log.debug(f"Attempting to cleanup file with ID: {file_id}")

        # Check if mapping exists
        filename = self._get_filename_from_mapping(file_id)
        log.debug(f"Filename from mapping for {file_id}: {filename}")

        file_path = self.get_file_path(file_id)
        if not file_path:
            log.warning(f"File not found for cleanup: {file_id}")
            # Additional debug info
            mapping_file = self.base_directory / ".file_mappings.json"
            if mapping_file.exists():
                try:
                    with open(mapping_file, "r", encoding="utf-8") as f:
                        mappings = json.load(f)
                    log.debug(f"Available file mappings: {list(mappings.keys())}")
                    if file_id in mappings:
                        log.debug(
                            f"Mapping exists but file not found: {mappings[file_id]}"
                        )
                except Exception as e:
                    log.debug(f"Failed to read mappings for debug: {e}")
            return False

        try:
            file_path.unlink()
            self._mark_file_as_downloaded(
                file_id
            )  # Mark as downloaded instead of removing mapping
            log.info(f"Cleaned up temporary file: {file_path.name}")
            return True
        except Exception as e:
            log.error(f"Failed to cleanup file {file_path}: {e}")
            return False

    def cleanup_old_files(self, max_age_hours: Optional[int] = None) -> int:
        """
        Clean up temporary files older than the specified age.
        Only cleans up files that have been downloaded or are very old (2x retention period).

        Args:
            max_age_hours: Maximum age in hours before files are deleted.
                          If None, uses self.retention_hours from settings.

        Returns:
            Number of files cleaned up
        """
        # Use the configured retention period if not explicitly provided
        max_age_hours = max_age_hours or self.retention_hours
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        force_cleanup_time = datetime.now() - timedelta(
            hours=max_age_hours * 2
        )  # Force cleanup after 2x retention
        cleaned_count = 0

        try:
            # Load file mappings to check download status
            mapping_file = self.base_directory / ".file_mappings.json"
            mappings = {}
            if mapping_file.exists():
                try:
                    with open(mapping_file, "r", encoding="utf-8") as f:
                        mappings = json.load(f)
                except Exception as e:
                    log.warning(f"Failed to load file mappings for cleanup: {e}")

            # Get all files in the directory (excluding hidden files like .file_mappings.json)
            for file_path in self.base_directory.iterdir():
                if file_path.is_file() and not file_path.name.startswith("."):
                    # Check file modification time
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)

                    # Find corresponding mapping
                    file_id = None
                    is_downloaded = False
                    for fid, mapping in mappings.items():
                        if mapping.get("filename") == file_path.name:
                            file_id = fid
                            is_downloaded = mapping.get("downloaded", False)
                            break

                    should_cleanup = False
                    cleanup_reason = ""

                    if file_mtime < force_cleanup_time:
                        # Force cleanup very old files regardless of download status
                        should_cleanup = True
                        cleanup_reason = f"very old (>{max_age_hours * 2}h)"
                    elif file_mtime < cutoff_time and file_id is None:
                        # Cleanup files without mapping (orphaned files)
                        should_cleanup = True
                        cleanup_reason = "orphaned file without mapping"
                    elif file_mtime < cutoff_time:
                        if self.cleanup_downloaded_only:
                            # Only cleanup downloaded files when this setting is enabled
                            if is_downloaded:
                                should_cleanup = True
                                cleanup_reason = (
                                    f"downloaded and old (>{max_age_hours}h)"
                                )
                        else:
                            # Cleanup all old files regardless of download status (legacy behavior)
                            should_cleanup = True
                            cleanup_reason = f"old (>{max_age_hours}h)"

                    if should_cleanup:
                        try:
                            file_path.unlink()
                            if file_id:
                                self._remove_file_mapping(file_id)
                            cleaned_count += 1
                            log.info(
                                f"Cleaned up temporary file: {file_path.name} (reason: {cleanup_reason})"
                            )
                        except Exception as e:
                            log.error(f"Failed to cleanup file {file_path}: {e}")
                    else:
                        log.debug(
                            f"Keeping file: {file_path.name} (not downloaded, age: {datetime.now() - file_mtime})"
                        )

            # Also clean up old mappings for files that no longer exist
            self._cleanup_old_mappings(max_age_hours)

            if cleaned_count > 0:
                log.info(f"Cleaned up {cleaned_count} temporary files")

            return cleaned_count

        except Exception as e:
            log.error(f"Error during old file cleanup: {e}")
            return cleaned_count

    def get_file_info(self, file_id: str) -> Optional[dict]:
        """
        Get information about a temporary file.

        Args:
            file_id: File ID to get info for

        Returns:
            Dictionary with file information or None if not found
        """
        file_path = self.get_file_path(file_id)
        if not file_path:
            return None

        try:
            stat = file_path.stat()
            return {
                "file_id": file_id,
                "filename": file_path.name,
                "size_bytes": stat.st_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "exists": True,
            }
        except Exception as e:
            log.error(f"Failed to get file info for {file_id}: {e}")
            return None

    def list_temp_files(self) -> List[dict]:
        """
        List all temporary files managed by this instance with download status.

        Returns:
            List of file information dictionaries
        """
        files = []

        # Load file mappings to get download status
        mapping_file = self.base_directory / ".file_mappings.json"
        mappings = {}
        if mapping_file.exists():
            try:
                with open(mapping_file, "r", encoding="utf-8") as f:
                    mappings = json.load(f)
            except Exception as e:
                log.warning(f"Failed to load file mappings: {e}")

        try:
            for file_path in self.base_directory.iterdir():
                if file_path.is_file() and not file_path.name.startswith("."):
                    try:
                        stat = file_path.stat()

                        # Find corresponding mapping
                        file_id = None
                        is_downloaded = False
                        downloaded_time = None
                        for fid, mapping in mappings.items():
                            if mapping.get("filename") == file_path.name:
                                file_id = fid
                                is_downloaded = mapping.get("downloaded", False)
                                downloaded_time = mapping.get("downloaded_time")
                                break

                        files.append(
                            {
                                "file_id": file_id,
                                "filename": file_path.name,
                                "size_bytes": stat.st_size,
                                "created_time": datetime.fromtimestamp(stat.st_ctime),
                                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                                "downloaded": is_downloaded,
                                "downloaded_time": downloaded_time,
                            }
                        )
                    except Exception as e:
                        log.error(f"Failed to get info for file {file_path}: {e}")
        except Exception as e:
            log.error(f"Failed to list temporary files: {e}")

        return files

    # Convenience methods for common use cases
    def create_script_result_file(
        self, content: Any, script_name: str, file_id: Optional[str] = None
    ) -> str:
        """
        Create a temporary file for script results.

        Args:
            content: Content to save
            script_name: Name of the script
            file_id: Optional file ID

        Returns:
            File ID for later retrieval
        """
        return self.save_result_file(
            content=content,
            file_id=file_id,
            name=script_name,
            category=FileCategory.SCRIPT_RESULT,
        )

    def create_export_file(
        self,
        content: Any,
        export_name: str,
        format_ext: str,
        file_id: Optional[str] = None,
    ) -> str:
        """
        Create a temporary file for export results.

        Args:
            content: Content to save
            export_name: Name of the export
            format_ext: File format extension (xlsx, csv, etc.)
            file_id: Optional file ID

        Returns:
            File ID for later retrieval
        """
        return self.save_result_file(
            content=content,
            file_id=file_id,
            name=export_name,
            extension=format_ext,
            category=FileCategory.EXPORT,
        )

    def get_export_path(self, file_id: str, export_name: str, format_ext: str) -> Path:
        """
        Get a temporary path for export files.

        Args:
            file_id: File ID
            export_name: Name of the export
            format_ext: File format extension

        Returns:
            Path object for the temporary file
        """
        return self.get_temp_path(
            file_id=file_id,
            name=export_name,
            extension=format_ext,
            category=FileCategory.EXPORT,
        )

    def get_processing_path(
        self, file_id: str, process_name: str, format_ext: str = "tmp"
    ) -> Path:
        """
        Get a temporary path for processing files.

        Args:
            file_id: File ID
            process_name: Name of the process
            format_ext: File format extension

        Returns:
            Path object for the temporary file
        """
        return self.get_temp_path(
            file_id=file_id,
            name=process_name,
            extension=format_ext,
            category=FileCategory.PROCESSING,
        )

    def create_upload_file(
        self, content: Any, original_filename: str, file_id: Optional[str] = None
    ) -> str:
        """
        Create a temporary file for uploaded content.

        Args:
            content: Content to save
            original_filename: Original filename (extension will be extracted)
            file_id: Optional file ID

        Returns:
            File ID for later retrieval
        """
        # Extract extension from original filename
        extension = Path(original_filename).suffix.lstrip(".")
        name = Path(original_filename).stem

        return self.save_result_file(
            content=content,
            file_id=file_id,
            name=name,
            extension=extension or "tmp",
            category=FileCategory.UPLOAD,
        )

    def create_cache_file(
        self, content: Any, cache_key: str, file_id: Optional[str] = None
    ) -> str:
        """
        Create a temporary cache file.

        Args:
            content: Content to cache
            cache_key: Cache key identifier
            file_id: Optional file ID

        Returns:
            File ID for later retrieval
        """
        return self.save_result_file(
            content=content,
            file_id=file_id,
            name=cache_key,
            category=FileCategory.CACHE,
        )

    def _store_file_mapping(self, file_id: str, filename: str) -> None:
        """Store mapping between file ID and filename."""
        mapping_file = self.base_directory / ".file_mappings.json"
        mappings = {}

        log.info(f"Storing file mapping: {file_id} -> {filename}")
        log.info(f"Mapping file path: {mapping_file}")
        log.info(f"Base directory: {self.base_directory}")
        log.info(f"Base directory exists: {self.base_directory.exists()}")

        # Ensure base directory exists
        try:
            self.base_directory.mkdir(parents=True, exist_ok=True)
            log.info(f"Base directory created/verified: {self.base_directory}")
        except Exception as e:
            log.error(f"Failed to create base directory {self.base_directory}: {e}")
            raise

        # Load existing mappings
        if mapping_file.exists():
            try:
                with open(mapping_file, "r", encoding="utf-8") as f:
                    mappings = json.load(f)
                log.info(f"Loaded {len(mappings)} existing mappings")
            except Exception as e:
                log.warning(f"Failed to load file mappings: {e}")
                mappings = {}

        # Add new mapping with timestamp and download status
        mappings[file_id] = {
            "filename": filename,
            "created_time": datetime.now().isoformat(),
            "downloaded": False,  # Track download status
        }
        log.info(f"Added mapping, total mappings: {len(mappings)}")

        # Save mappings with more robust error handling
        try:
            # Create a temporary file first to avoid corruption
            temp_mapping_file = mapping_file.with_suffix(".json.tmp")
            with open(temp_mapping_file, "w", encoding="utf-8") as f:
                json.dump(mappings, f, indent=2)

            # Atomic rename to replace the original file
            temp_mapping_file.replace(mapping_file)
            log.info(f"Successfully saved file mappings to {mapping_file}")

            # Verify the file was written correctly
            if mapping_file.exists():
                with open(mapping_file, "r", encoding="utf-8") as f:
                    verification_mappings = json.load(f)
                if file_id in verification_mappings:
                    log.info(f"Mapping verification successful for {file_id}")
                else:
                    log.error(
                        f"Mapping verification failed: {file_id} not found after save"
                    )
            else:
                log.error(f"Mapping file does not exist after save: {mapping_file}")

        except Exception as e:
            log.error(f"Failed to save file mappings: {e}")
            import traceback

            log.error(f"Traceback: {traceback.format_exc()}")
            raise

    def _get_filename_from_mapping(self, file_id: str) -> Optional[str]:
        """Get filename from file ID mapping."""
        mapping_file = self.base_directory / ".file_mappings.json"

        if not mapping_file.exists():
            return None

        try:
            with open(mapping_file, "r", encoding="utf-8") as f:
                mappings = json.load(f)
                mapping = mappings.get(file_id)
                return mapping["filename"] if mapping else None
        except Exception as e:
            log.warning(f"Failed to read file mappings: {e}")
            return None

    def _mark_file_as_downloaded(self, file_id: str) -> None:
        """Mark file as downloaded in the mapping."""
        mapping_file = self.base_directory / ".file_mappings.json"

        if not mapping_file.exists():
            return

        try:
            with open(mapping_file, "r", encoding="utf-8") as f:
                mappings = json.load(f)

            if file_id in mappings:
                mappings[file_id]["downloaded"] = True
                mappings[file_id]["downloaded_time"] = datetime.now().isoformat()

                with open(mapping_file, "w", encoding="utf-8") as f:
                    json.dump(mappings, f, indent=2)
                log.info(f"Marked file {file_id} as downloaded")
        except Exception as e:
            log.warning(f"Failed to mark file as downloaded: {e}")

    def _remove_file_mapping(self, file_id: str) -> None:
        """Remove file ID mapping."""
        mapping_file = self.base_directory / ".file_mappings.json"

        if not mapping_file.exists():
            return

        try:
            with open(mapping_file, "r", encoding="utf-8") as f:
                mappings = json.load(f)

            if file_id in mappings:
                del mappings[file_id]

                with open(mapping_file, "w", encoding="utf-8") as f:
                    json.dump(mappings, f, indent=2)
        except Exception as e:
            log.warning(f"Failed to remove file mapping: {e}")

    def _cleanup_old_mappings(self, max_age_hours: int) -> None:
        """Clean up old file mappings based on download status and age."""
        mapping_file = self.base_directory / ".file_mappings.json"

        if not mapping_file.exists():
            return

        try:
            with open(mapping_file, "r", encoding="utf-8") as f:
                mappings = json.load(f)

            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            force_cleanup_time = datetime.now() - timedelta(hours=max_age_hours * 2)
            cleaned_mappings = {}

            for file_id, mapping in mappings.items():
                try:
                    created_time = datetime.fromisoformat(mapping["created_time"])
                    is_downloaded = mapping.get("downloaded", False)

                    # Check if corresponding file still exists
                    file_path = self.base_directory / mapping["filename"]
                    file_exists = file_path.exists()

                    should_keep = False

                    if not file_exists:
                        # Remove mappings for files that no longer exist
                        should_keep = False
                    elif created_time < force_cleanup_time:
                        # Remove very old mappings regardless of download status
                        should_keep = False
                    elif created_time < cutoff_time and is_downloaded:
                        # Remove mappings for downloaded files after retention period
                        should_keep = False
                    elif created_time >= cutoff_time:
                        # Keep recent mappings
                        should_keep = True
                    elif not is_downloaded:
                        # Keep undownloaded files even if old (but not very old)
                        should_keep = True

                    if should_keep:
                        cleaned_mappings[file_id] = mapping

                except Exception as e:
                    log.warning(f"Invalid mapping entry for {file_id}: {e}")

            # Save cleaned mappings
            with open(mapping_file, "w", encoding="utf-8") as f:
                json.dump(cleaned_mappings, f, indent=2)

            removed_count = len(mappings) - len(cleaned_mappings)
            if removed_count > 0:
                log.info(f"Cleaned up {removed_count} old file mappings")

        except Exception as e:
            log.warning(f"Failed to cleanup old mappings: {e}")


# Global instance for use throughout the application
temp_file_manager = TempFileManager()
