# magic_gateway/tracking/models.py

"""Models for request tracking in the MagicGateway application."""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field


class DatabaseType(str, Enum):
    """Type of database."""

    CLICKHOUSE = "clickhouse"
    POSTGRES = "postgres"


class RequestStatus(str, Enum):
    """Status of an API request."""

    RECEIVED = "received"  # Request received by middleware
    AUTHENTICATING = "authenticating"  # Optional: If auth takes time
    PROCESSING = "processing"  # Request being handled by endpoint logic
    # Add more specific states if needed, e.g., DB_QUERYING, EXTERNAL_CALL
    COMPLETED = "completed"  # Request finished successfully (2xx)
    FAILED = "failed"  # Request failed (4xx, 5xx, or uncaught exception)
    CANCELED = "canceled"  # Associated long-running task was canceled


class ApiRequestLog(BaseModel):
    """Model for an API request log entry in the database."""

    # Core Fields
    request_id: uuid.UUID = Field(default_factory=uuid.uuid4)
    endpoint_path: str
    http_method: str
    status: RequestStatus = RequestStatus.RECEIVED
    start_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    end_time: Optional[datetime] = None
    duration_ms: Optional[int] = None  # Duration in milliseconds

    # Request Context
    client_ip: Optional[str] = None
    username: Optional[str] = None  # Updated after authentication

    # Response Context
    status_code: Optional[int] = None
    error_message: Optional[str] = None  # Store brief error summary

    # Optional Detailed Context (Consider JSONB in Postgres)
    # request_headers: Optional[Dict[str, str]] = None # Careful with sensitive data
    # request_body_preview: Optional[str] = None # Truncated, careful with sensitive data
    # response_body_preview: Optional[str] = None # Truncated, careful with sensitive data
    task_details: Optional[Dict[str, Any]] = (
        None  # For linking DB query IDs, script IDs etc. for cancellation
    )

    model_config = {
        "from_attributes": True,  # Renamed from orm_mode in Pydantic V2
        "arbitrary_types_allowed": True,  # Allow UUID
    }


# Models related to QueryTracker (TrackedQuery, QueryResult etc.) can now be removed
# Keep QueryRequest/CommandRequest if they are still useful API input models
class QueryRequest(BaseModel):
    """Model for a query request."""

    query: str
    params: Optional[Dict[str, Any]] = None
    # query_id is now implicitly the request_id from the tracker
    allow_write: bool = False
    cluster: Optional[str] = Field(
        None,
        description="Optional cluster name to specify target ClickHouse server. If not provided, the primary server is used.",
    )


class CommandRequest(BaseModel):
    """Model for a command request."""

    command: str
    params: Optional[Dict[str, Any]] = None
    # query_id is now implicitly the request_id from the tracker
    cluster: Optional[str] = Field(
        None,
        description="Optional cluster name to specify target ClickHouse server. If not provided, the primary server is used.",
    )


# Model for status response (simplified)
class RequestStatusResponse(BaseModel):
    request_id: uuid.UUID
    status: RequestStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[int] = None
    endpoint_path: str
    http_method: str
    username: Optional[str] = None
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    task_details: Optional[Dict[str, Any]] = None
