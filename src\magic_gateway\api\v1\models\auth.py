"""Authentication models for the MagicGateway application."""

from pydantic import BaseModel, Field


class LoginRequest(BaseModel):
    """Model for a login request."""

    username: str = Field(
        ...,
        description="LDAP username (UPN format for users, DN format for service accounts)",
    )
    password: str = Field(..., description="LDAP password")


class Token(BaseModel):
    """Model for an authentication token."""

    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class RefreshTokenRequest(BaseModel):
    """Model for a refresh token request."""

    refresh_token: str = Field(
        ..., description="The refresh token to use for generating a new access token"
    )


class TokenPayload(BaseModel):
    """Model for a token payload."""

    sub: str = None
    exp: int = None
    iat: int = None
    type: str = None
    user_id: int = None
    is_admin: bool = None
    auth_source: str = None


class User(BaseModel):
    """Model for a user."""

    username: str
    is_admin: bool = False
    auth_source: str = "ldap"
