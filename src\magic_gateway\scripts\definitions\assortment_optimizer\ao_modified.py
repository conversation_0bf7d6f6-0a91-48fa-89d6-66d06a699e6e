"""Modified assortment optimization script that accepts parameters instead of hardcoded values."""

from datetime import datetime
import pandas as pd
import logging
from typing import Optional
import os
import sys
from contextlib import redirect_stderr, redirect_stdout
from io import StringIO

# Import functions from ao.py
from .ao import (
    initialize_cpapi_client,
    initialize_click_connection,
    get_axis_code,
    get_sql_code,
    create_pre_axis,
    create_axis_by_id_trip,
    create_buyers_final,
    create_rp_data,
    create_buyers_uplift,
    create_final_kpi,
    get_panel_name,
    get_axis_info_df,
    get_filter_info_df,
)
from .cpaapi_client.client import DBEngineEnum
from .exception import AxisTranslationError
from .util import get_version_from_toml

# Set up logging
LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(logging.INFO)


def initialize_connections_with_port_fix():
    """
    Initialize connections with automatic port correction for ClickHouse.
    This handles the common issue where CLICKHOUSE_PORT is set to 9000 (native)
    but clickhouse_connect needs 8123 (HTTP).
    """
    # Store original port
    original_port = os.environ.get("CLICKHOUSE_PORT")

    try:
        # Try with original port first
        click_client = safe_call_ao_function(initialize_click_connection)
        return click_client
    except Exception as e:
        # If it fails and we're using port 9000, try with 8123
        if original_port == "9000":
            LOGGER.info(
                "ClickHouse connection failed with port 9000, trying port 8123 (HTTP)"
            )
            try:
                os.environ["CLICKHOUSE_PORT"] = "8123"
                click_client = safe_call_ao_function(initialize_click_connection)
                LOGGER.info("Successfully connected to ClickHouse using HTTP port 8123")
                return click_client
            except Exception as e2:
                # Try with CLICKHOUSE_HTTP_PORT if available
                http_port = os.environ.get("CLICKHOUSE_HTTP_PORT", "8123")
                if http_port != "8123":
                    LOGGER.info(f"Trying with CLICKHOUSE_HTTP_PORT: {http_port}")
                    try:
                        os.environ["CLICKHOUSE_PORT"] = http_port
                        click_client = safe_call_ao_function(
                            initialize_click_connection
                        )
                        LOGGER.info(
                            f"Successfully connected to ClickHouse using port {http_port}"
                        )
                        return click_client
                    except Exception as e3:
                        pass

                # Restore original port and re-raise the last error
                if original_port:
                    os.environ["CLICKHOUSE_PORT"] = original_port
                raise e2
        else:
            raise e
    finally:
        # Ensure we restore the original port if we changed it
        if original_port and os.environ.get("CLICKHOUSE_PORT") != original_port:
            os.environ["CLICKHOUSE_PORT"] = original_port


class SystemExitException(Exception):
    """Custom exception to handle sys.exit() calls from ao.py functions."""

    pass


def safe_call_ao_function(func, *args, **kwargs):
    """
    Safely call a function from ao.py that might call sys.exit().
    Converts sys.exit() calls to exceptions.
    """
    # Monkey patch sys.exit temporarily
    original_exit = sys.exit

    def mock_exit(code=0):
        raise SystemExitException(f"Function {func.__name__} called sys.exit({code})")

    sys.exit = mock_exit

    try:
        # Capture stdout/stderr to prevent unwanted output
        stdout_capture = StringIO()
        stderr_capture = StringIO()

        with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
            result = func(*args, **kwargs)

        return result
    except SystemExitException as e:
        # Get captured output for better error context
        stdout_content = stdout_capture.getvalue()
        stderr_content = stderr_capture.getvalue()

        error_context = ""
        if stdout_content:
            error_context += f" stdout: {stdout_content.strip()}"
        if stderr_content:
            error_context += f" stderr: {stderr_content.strip()}"

        # Convert sys.exit() to a proper exception with context
        raise RuntimeError(f"Function {func.__name__} failed{error_context}") from e
    except Exception as e:
        # Handle other exceptions that might occur
        if "clickhouse_connect.driver.exceptions" in str(type(e)):
            # Handle ClickHouse connection errors specifically
            raise ConnectionError(
                f"ClickHouse connection error in {func.__name__}: {str(e)}"
            ) from e
        else:
            # Re-raise other exceptions as-is
            raise e
    finally:
        # Restore original sys.exit
        sys.exit = original_exit


def calc_assortment_optimizer_with_params(
    start_date: str,
    end_date: str,
    json_axis_id: int,
    json_flt_hh_id: Optional[int],
    total_position_number: int,
    rest_position_number: int,
    id_panel: int,
    output_file_path: str,
):
    """
    Calculate assortment optimization with provided parameters.

    This function replicates the workflow from ao.py's calc_assortment_optimizer
    but with enhanced error handling and parameterization.
    """
    click_client = None
    cpapi_client = None

    try:
        # Initialize ClickHouse connection with automatic port correction
        LOGGER.info("Initializing ClickHouse connection...")
        try:
            click_client = initialize_connections_with_port_fix()
            LOGGER.info("ClickHouse connection established successfully")
        except Exception as e:
            LOGGER.error(f"Failed to initialize ClickHouse connection: {e}")
            raise ConnectionError(f"Failed to connect to ClickHouse: {str(e)}") from e

        LOGGER.info("Initializing CP-API client...")
        try:
            cpapi_client = safe_call_ao_function(initialize_cpapi_client)
            LOGGER.info("CP-API client initialized successfully")
        except Exception as e:
            LOGGER.error(f"Failed to initialize CP-API client: {e}")
            raise ConnectionError(f"Failed to connect to CP-API: {str(e)}") from e

        # Follow the same workflow as calc_assortment_optimizer from ao.py
        general_sql_parameters = {
            "start_date": start_date,
            "end_date": end_date,
            "id_panel": id_panel,
        }

        # Validate axis
        LOGGER.info(f"Validating axis with ID: {json_axis_id}")
        try:
            axis_description = cpapi_client.get_object_description(id=json_axis_id)

            if not axis_description or "object_type" not in axis_description:
                raise ValueError(
                    f"Invalid axis description received for axis ID {json_axis_id}"
                )

            if axis_description["object_type"] != "Axis":
                raise ValueError(
                    f"Object with id={json_axis_id} is not an Axis. Object type is {axis_description['object_type']}"
                )

            axis_type = axis_description.get("axis_type")
            if not axis_type:
                raise ValueError(f"Axis type not specified for axis ID {json_axis_id}")

            if axis_type not in ("axsm", "axsa"):
                raise ValueError(
                    f"Object with id={json_axis_id} is not an Article or Movement Axis. Object type is {axis_type}"
                )

            LOGGER.info(
                f"Axis validation successful: ID={json_axis_id}, type={axis_type}"
            )
        except Exception as e:
            if isinstance(e, ValueError):
                raise e
            LOGGER.error(f"Error validating axis {json_axis_id}: {e}")
            raise ValueError(f"Failed to validate axis {json_axis_id}: {str(e)}") from e

        # Create axis table
        axis_table_name = "ao_axis_temp_table"
        LOGGER.info(f"Getting SQL code for axis={json_axis_id}")
        try:
            axis_code = get_axis_code(
                temp_table_name=axis_table_name,
                cpapi_client=cpapi_client,
                json_id_axis=json_axis_id,
                engine_type=DBEngineEnum.clickhouse,
                id_panel=id_panel,
            )
            LOGGER.info(f"Successfully generated axis SQL code for axis {json_axis_id}")
        except AxisTranslationError as e:
            LOGGER.error(f"Axis translation error for axis {json_axis_id}: {e}")
            raise AxisTranslationError(
                f"Failed to translate axis {json_axis_id}: {str(e)}"
            ) from e
        except Exception as e:
            LOGGER.error(
                f"Unexpected error generating axis code for axis {json_axis_id}: {e}"
            )
            raise ValueError(
                f"Failed to generate axis SQL code for axis {json_axis_id}: {str(e)}"
            ) from e

        try:
            LOGGER.info(f"Executing axis creation SQL for axis {json_axis_id}")
            click_client.command(axis_code)
            LOGGER.info(f"Successfully created axis table: {axis_table_name}")
        except Exception as e:
            LOGGER.error(f"Failed to execute axis creation SQL: {e}")
            raise ConnectionError(
                f"Failed to create axis table in ClickHouse: {str(e)}"
            ) from e

        # Handle household filter if provided
        hh_flt_description = None
        if json_flt_hh_id is not None:
            LOGGER.info(f"Processing household filter with ID: {json_flt_hh_id}")
            try:
                hh_flt_description = cpapi_client.get_object_description(
                    id=json_flt_hh_id
                )

                translation_result = cpapi_client.get_filter_translation(
                    id=json_flt_hh_id, engine_type=DBEngineEnum.clickhouse
                )

                if not translation_result or "query" not in translation_result:
                    raise ValueError(
                        f"Invalid filter translation result for filter ID {json_flt_hh_id}"
                    )

                flth_code = translation_result["query"]
                create_hh_filter_code = get_sql_code(
                    template_filename="00_create_hh_filter.sql",
                    params={"hh_filter_query": flth_code},
                )

                LOGGER.info(f"Executing household filter creation SQL")
                click_client.command(
                    cmd=create_hh_filter_code, parameters=general_sql_parameters
                )
                LOGGER.info(f"Successfully created household filter table")

            except AxisTranslationError as e:
                LOGGER.error(
                    f"Filter translation error for filter {json_flt_hh_id}: {e}"
                )
                raise AxisTranslationError(
                    f"Failed to translate household filter {json_flt_hh_id}: {str(e)}"
                ) from e
            except Exception as e:
                LOGGER.error(f"Error processing household filter {json_flt_hh_id}: {e}")
                raise ValueError(
                    f"Failed to process household filter {json_flt_hh_id}: {str(e)}"
                ) from e

        # Get list of unique positions
        LOGGER.info("Getting list of unique positions within the axis")
        try:
            axis_position_df = click_client.query_df(
                f"SELECT DISTINCT position_number FROM {axis_table_name} ORDER BY position_number"
            )

            if axis_position_df.empty:
                raise ValueError(
                    f"No positions found in axis {json_axis_id} for the specified date range"
                )

            axis_position_list = axis_position_df["position_number"].tolist()
            LOGGER.info(
                f"Found {len(axis_position_list)} positions: {axis_position_list}"
            )

            # Validate that required positions exist
            if total_position_number not in axis_position_list:
                raise ValueError(
                    f"Total position number {total_position_number} not found in axis {json_axis_id}"
                )

            if (
                rest_position_number != -1
                and rest_position_number not in axis_position_list
            ):
                raise ValueError(
                    f"REST position number {rest_position_number} not found in axis {json_axis_id}"
                )

        except Exception as e:
            if isinstance(e, ValueError):
                raise e
            LOGGER.error(f"Failed to retrieve position list from axis table: {e}")
            raise ConnectionError(
                f"Failed to query axis positions from ClickHouse: {str(e)}"
            ) from e

        # Process position lists (following ao.py logic)
        try:
            axis_position_no_total_no_rest_list = axis_position_list.copy()
            axis_position_no_total_no_rest_list.remove(total_position_number)

            if rest_position_number in axis_position_no_total_no_rest_list:
                axis_position_no_total_no_rest_list.remove(rest_position_number)

            axis_position_no_total_list = axis_position_list.copy()
            axis_position_no_total_list.remove(total_position_number)

            LOGGER.info(
                f"Positions for optimization (excluding total): {axis_position_no_total_list}"
            )
            LOGGER.info(
                f"Positions for optimization (excluding total and REST): {axis_position_no_total_no_rest_list}"
            )
        except ValueError as e:
            LOGGER.error(f"Error processing position lists: {e}")
            raise ValueError(f"Failed to process position lists: {str(e)}") from e

        # Table names
        buyers_total_table_name = "buyers_final_total"
        buyers_flth_table_name = "buyers_final_flth"

        # Calculate standard penetration (following ao.py workflow)
        LOGGER.info("Calculating standard penetration metrics")
        try:
            # Use safe_call_ao_function for functions that might call sys.exit()
            safe_call_ao_function(
                create_pre_axis,
                click_client,
                axis_table_name,
                axis_type,
                axis_position_list,
                id_panel,
                start_date,
                end_date,
                "position_number",
            )

            safe_call_ao_function(create_axis_by_id_trip, click_client)

            safe_call_ao_function(
                create_buyers_final,
                click_client,
                buyers_total_table_name,
                False,
                False,
            )

            safe_call_ao_function(
                create_rp_data,
                click_client,
                buyers_total_table_name,
                general_sql_parameters,
            )

            safe_call_ao_function(
                create_buyers_uplift, click_client, general_sql_parameters
            )

            if json_flt_hh_id is not None:
                safe_call_ao_function(
                    create_buyers_final,
                    click_client,
                    buyers_flth_table_name,
                    True,
                    False,
                )

            safe_call_ao_function(
                create_final_kpi,
                click_client,
                buyers_flth_table_name
                if json_flt_hh_id is not None
                else buyers_total_table_name,
                True if json_flt_hh_id is not None else False,
                general_sql_parameters,
            )

        except Exception as e:
            LOGGER.error(f"Error in standard penetration calculation: {e}")
            raise RuntimeError(
                f"Failed to calculate standard penetration: {str(e)}"
            ) from e

        # Get standard KPI data
        try:
            LOGGER.info("Retrieving standard KPI data")
            standard_kpi_df = click_client.query_df(
                query="SELECT * FROM final_KPI_rwbasis"
            )

            if standard_kpi_df.empty:
                raise ValueError(
                    f"The axis {json_axis_id} provided an empty dataset for the date range {start_date} to {end_date}"
                )

            LOGGER.info(f"Retrieved {len(standard_kpi_df)} rows of standard KPI data")
        except Exception as e:
            if isinstance(e, ValueError):
                raise e
            LOGGER.error(f"Failed to retrieve standard KPI data: {e}")
            raise ConnectionError(
                f"Failed to query standard KPI data from ClickHouse: {str(e)}"
            ) from e

        # Calculate standard penetration metrics (following ao.py logic)
        try:
            LOGGER.info("Calculating standard penetration metrics")
            standard_pen_df = (
                standard_kpi_df.groupby("position_number")
                .agg({"population": "first", "buyers": "sum", "projectc": "first"})
                .reset_index()
            )

            if standard_pen_df.empty:
                raise ValueError(
                    "Failed to calculate standard penetration - no data after grouping"
                )

            # Calculate metrics
            standard_pen_df["buyers_rp"] = (
                standard_pen_df["buyers"] * standard_pen_df["projectc"] / 1000
            )
            standard_pen_df["population"] = (
                standard_pen_df["population"] * standard_pen_df["projectc"] / 1000
            )

            # Check for division by zero
            if (standard_pen_df["population"] == 0).any():
                raise ValueError(
                    "Population is zero for some positions - cannot calculate penetration"
                )

            standard_pen_df["penetration"] = (
                standard_pen_df["buyers_rp"] / standard_pen_df["population"]
            )

            # Get total penetration
            total_pen_rows = standard_pen_df[
                standard_pen_df["position_number"] == total_position_number
            ]
            if total_pen_rows.empty:
                raise ValueError(
                    f"Total position {total_position_number} not found in penetration data"
                )

            total_penetration = total_pen_rows.iloc[
                0, standard_pen_df.columns.get_loc("penetration")
            ]

            if total_penetration == 0:
                raise ValueError(
                    f"Total penetration is zero for position {total_position_number}"
                )

            LOGGER.info(f"Total penetration: {total_penetration:.4f}")

            standard_pen_df["single relative penetration"] = (
                standard_pen_df["penetration"] / total_penetration
            )

            LOGGER.info("Standard penetration calculations completed successfully")
        except Exception as e:
            if isinstance(e, ValueError):
                raise e
            LOGGER.error(f"Error calculating standard penetration metrics: {e}")
            raise ValueError(
                f"Failed to calculate penetration metrics: {str(e)}"
            ) from e

        # Main optimization loop (following ao.py logic)
        template_columns = [
            "position_number",
            "buyers_rp",
            "population",
            "projectc",
            "penetration",
        ]
        top_df = pd.DataFrame(columns=template_columns)

        LOGGER.info(
            f"Starting main optimization loop with {len(axis_position_no_total_list)} positions to process"
        )

        for outer_idx, position_number1 in enumerate(axis_position_no_total_list):
            try:
                LOGGER.info(
                    f"Optimization iteration {outer_idx + 1}/{len(axis_position_no_total_list)}"
                )
                combination_df = pd.DataFrame(columns=template_columns)

                position_for_loop = axis_position_no_total_no_rest_list
                if outer_idx == len(axis_position_no_total_list) - 1:
                    position_for_loop = axis_position_no_total_list

                for inner_idx, position_number in enumerate(position_for_loop):
                    # Skip if position already selected
                    if position_number in top_df["position_number"].to_list():
                        continue

                    check_positions = top_df["position_number"].tolist()
                    check_positions.append(position_number)

                    # Execute optimization step using ao.py functions
                    safe_call_ao_function(
                        create_pre_axis,
                        click_client,
                        axis_table_name,
                        axis_type,
                        check_positions,
                        id_panel,
                        start_date,
                        end_date,
                        1,
                    )

                    safe_call_ao_function(create_axis_by_id_trip, click_client)

                    safe_call_ao_function(
                        create_buyers_final,
                        click_client,
                        buyers_total_table_name,
                        False,
                        False,
                    )

                    safe_call_ao_function(
                        create_rp_data,
                        click_client,
                        buyers_total_table_name,
                        general_sql_parameters,
                    )

                    safe_call_ao_function(
                        create_buyers_uplift, click_client, general_sql_parameters
                    )

                    if json_flt_hh_id is not None:
                        safe_call_ao_function(
                            create_buyers_final,
                            click_client,
                            buyers_flth_table_name,
                            True,
                            False,
                        )

                    safe_call_ao_function(
                        create_final_kpi,
                        click_client,
                        buyers_flth_table_name
                        if json_flt_hh_id is not None
                        else buyers_total_table_name,
                        True if json_flt_hh_id is not None else False,
                        general_sql_parameters,
                    )

                    filtered_kpi_df = click_client.query_df(
                        query="SELECT * FROM final_KPI_rwbasis"
                    )

                    if not filtered_kpi_df.empty:
                        total_buyers = filtered_kpi_df["buyers"].sum()
                        first_row = filtered_kpi_df.iloc[0]
                        population = first_row["population"]
                        projectc = first_row["projectc"]

                        # Calculate metrics
                        buyers_rp = total_buyers * projectc / 1000
                        population_adj = population * projectc / 1000
                        penetration = buyers_rp / population_adj

                        # Create result DataFrame
                        result = pd.DataFrame(
                            {
                                "position_number": [position_number],
                                "buyers_rp": [buyers_rp],
                                "population": [population_adj],
                                "projectc": [projectc],
                                "penetration": [penetration],
                            }
                        )

                        if combination_df.empty:
                            combination_df = result.copy()
                        else:
                            combination_df = pd.concat(
                                [combination_df, result], ignore_index=True
                            )

                # Find the best position
                if not combination_df.empty:
                    combination_df = combination_df.set_index("position_number")
                    combination_df["position_number"] = combination_df.index
                    max_df = combination_df[
                        combination_df.index == combination_df["penetration"].idxmax()
                    ]

                    # Calculate exclusive penetration
                    max_position = int(max_df["position_number"].iloc[0])
                    rest_but_max_position = axis_position_list.copy()
                    rest_but_max_position.remove(max_position)
                    rest_but_max_position.remove(total_position_number)

                    # Calculate exclusive penetration using ao.py functions
                    safe_call_ao_function(
                        create_pre_axis,
                        click_client,
                        axis_table_name,
                        axis_type,
                        rest_but_max_position,
                        id_panel,
                        start_date,
                        end_date,
                        1,
                    )

                    safe_call_ao_function(create_axis_by_id_trip, click_client)

                    safe_call_ao_function(
                        create_buyers_final,
                        click_client,
                        buyers_total_table_name,
                        False,
                        False,
                    )

                    safe_call_ao_function(
                        create_rp_data,
                        click_client,
                        buyers_total_table_name,
                        general_sql_parameters,
                    )

                    safe_call_ao_function(
                        create_buyers_uplift, click_client, general_sql_parameters
                    )

                    if json_flt_hh_id is not None:
                        safe_call_ao_function(
                            create_buyers_final,
                            click_client,
                            buyers_flth_table_name,
                            True,
                            False,
                        )

                    safe_call_ao_function(
                        create_final_kpi,
                        click_client,
                        buyers_flth_table_name
                        if json_flt_hh_id is not None
                        else buyers_total_table_name,
                        True if json_flt_hh_id is not None else False,
                        general_sql_parameters,
                    )

                    filtered_kpi_df = click_client.query_df(
                        query="SELECT * FROM final_KPI_rwbasis"
                    )

                    total_buyers = filtered_kpi_df["buyers"].sum()
                    first_row = filtered_kpi_df.iloc[0]
                    population = first_row["population"]
                    projectc = first_row["projectc"]

                    # Calculate exclusive metrics
                    buyers_rp = total_buyers * projectc / 1000
                    population_adj = population * projectc / 1000
                    penetration = buyers_rp / population_adj

                    max_df = max_df.copy()
                    max_df["exclusive penetration"] = total_penetration - penetration
                    max_df["exclusive relative penetration"] = (
                        total_penetration - penetration
                    ) / total_penetration

                    if top_df.empty:
                        top_df = max_df.copy()
                    else:
                        top_df = pd.concat([top_df, max_df], ignore_index=True)
                    LOGGER.info(
                        f"Completed optimization iteration {outer_idx + 1}/{len(axis_position_no_total_list)}"
                    )

            except Exception as e:
                LOGGER.error(f"Error in optimization iteration {outer_idx + 1}: {e}")
                raise ValueError(
                    f"Optimization failed at iteration {outer_idx + 1}: {str(e)}"
                ) from e

        LOGGER.info("Main optimization loop completed successfully")

        # Final calculations (following ao.py logic)
        top_df = top_df.rename(columns={"penetration": "cumulative penetration"})
        top_df["cumulative relative penetration"] = (
            top_df["cumulative penetration"] / total_penetration
        )
        top_df["incremental penetration"] = top_df["cumulative penetration"].diff()

        inc_pen_col = top_df.columns.get_loc("incremental penetration")
        top_df.iloc[0, int(inc_pen_col)] = top_df.iloc[
            0, top_df.columns.get_loc("cumulative penetration")
        ]
        top_df["incremental relative penetration"] = (
            top_df["incremental penetration"] / total_penetration
        )

        # Add usual penetration
        top_df = top_df.merge(
            standard_pen_df[
                ["position_number", "penetration", "single relative penetration"]
            ],
            on="position_number",
            how="left",
        )
        top_df["cannibilized relative penetration"] = (
            top_df["single relative penetration"]
            - top_df["incremental relative penetration"]
        )

        # Technical column for building a chart
        top_df["chart column"] = (
            top_df["cumulative relative penetration"]
            - top_df["incremental relative penetration"]
            - top_df["cannibilized relative penetration"]
        )

        # Calculate duplicators
        top_df["duplicators"] = 0.0
        for i in range(1, len(top_df)):
            top_df.loc[i, "duplicators"] = (
                top_df.loc[i - 1, "single relative penetration"]
                + top_df.loc[i - 1, "duplicators"]
                - top_df.loc[i - 1, "cannibilized relative penetration"]
            )

        # Get axis data
        try:
            LOGGER.info(f"Retrieving axis data for axis {json_axis_id}")
            axis_data = cpapi_client.get_axis_data(json_axis_id)

            if not axis_data:
                LOGGER.warning(f"No axis data returned for axis {json_axis_id}")
                top_df["value_group_name"] = "Unknown"
            else:
                axis_df = pd.DataFrame(axis_data)
                if (
                    "position_number" in axis_df.columns
                    and "value_group_name" in axis_df.columns
                ):
                    top_df = top_df.merge(
                        axis_df[["position_number", "value_group_name"]],
                        on="position_number",
                        how="left",
                    )
                    LOGGER.info(
                        f"Successfully merged axis data with {len(axis_df)} rows"
                    )
                else:
                    LOGGER.warning("axis_data does not contain expected columns")
                    top_df["value_group_name"] = "Unknown"
        except Exception as e:
            LOGGER.error(f"Error retrieving axis data for axis {json_axis_id}: {e}")
            top_df["value_group_name"] = "Unknown"
            LOGGER.info(
                "Continuing with default value_group_name due to axis data retrieval error"
            )

        top_df = top_df.drop(["population", "projectc"], axis=1)

        output_column_order = [
            "position_number",
            "value_group_name",
            "buyers_rp",
            "penetration",
            "cumulative penetration",
            "cumulative relative penetration",
            "incremental penetration",
            "incremental relative penetration",
            "single relative penetration",
            "cannibilized relative penetration",
            "duplicators",
            "exclusive penetration",
            "exclusive relative penetration",
            "chart column",
        ]

        top_df = top_df[output_column_order]

        # Save results to Excel (following ao.py logic)
        try:
            LOGGER.info(f"Final optimization results shape: {top_df.shape}")
            LOGGER.info("Sample of final results:")
            LOGGER.info(top_df.head().to_string())

            # Validate final results
            if top_df.empty:
                raise ValueError("Optimization completed but resulted in empty dataset")

            required_columns = [
                "position_number",
                "value_group_name",
                "buyers_rp",
                "penetration",
            ]
            missing_columns = [
                col for col in required_columns if col not in top_df.columns
            ]
            if missing_columns:
                raise ValueError(
                    f"Missing required columns in final results: {missing_columns}"
                )

            # Save to Excel file
            LOGGER.info(f"Saving results to Excel file: {output_file_path}")

            # Prepare additional information for Excel report
            panel_name = get_panel_name(id_panel, cpapi_client)

            axis_name = f"{axis_description['client_group']}.{axis_description['axis_name']} ({json_axis_id})"
            filter_name = f"{hh_flt_description['client_group'] if hh_flt_description else ''}.{hh_flt_description['filter_name'] if hh_flt_description else ''} ({json_flt_hh_id})"

            general_info = [
                ["Panel name", f"{panel_name} ({id_panel})"],
                ["Period", f"{start_date} - {end_date}"],
                ["Json Axis", axis_name],
                ["Json HH filter", filter_name],
                ["Total position number", total_position_number],
                ["App version", get_version_from_toml()],
                ["TimeStamp:", datetime.now()],
            ]
            general_info_df = pd.DataFrame(general_info, columns=["Info", "Value"])

            axis_info_df = get_axis_info_df(axis_description)
            hh_flt_info_df = (
                get_filter_info_df(hh_flt_description) if hh_flt_description else None
            )

            with pd.ExcelWriter(output_file_path, engine="xlsxwriter") as writer:
                top_df.to_excel(writer, index=False, sheet_name="Data")

                workbook = writer.book
                data_worksheet = writer.sheets["Data"]

                # Create formats with error handling
                try:
                    dot1_format = workbook.add_format({"num_format": "0.0"})
                    percent_format = workbook.add_format({"num_format": "0.0%"})

                    data_worksheet.set_column("C:C", None, dot1_format)
                    data_worksheet.set_column("D:N", None, percent_format)
                except Exception as format_error:
                    LOGGER.warning(f"Failed to apply Excel formatting: {format_error}")

                # Add Info sheet
                general_info_df.to_excel(
                    writer, index=False, sheet_name="Info", startrow=1
                )

                info_worksheet = writer.sheets["Info"]
                info_worksheet.write(0, 0, "Report info")

                right_align_format = workbook.add_format()
                right_align_format.set_align("right")

                info_worksheet.set_column("A:A", 30)
                info_worksheet.set_column("B:B", 50, right_align_format)

                info_worksheet.write(10, 0, "Axis info")
                axis_info_df.to_excel(
                    writer, index=False, sheet_name="Info", startrow=11
                )

                info_worksheet.write(14, 0, "Filter info")
                if (
                    hh_flt_description is not None
                    and hh_flt_info_df is not None
                    and not hh_flt_info_df.empty
                ):
                    hh_flt_info_df.to_excel(
                        writer, index=False, sheet_name="Info", startrow=15
                    )

            LOGGER.info(f"Results successfully saved to {output_file_path}")

            # Verify file was created and has content
            if not os.path.exists(output_file_path):
                raise FileNotFoundError(
                    f"Output file was not created: {output_file_path}"
                )

            file_size = os.path.getsize(output_file_path)
            if file_size == 0:
                raise ValueError(f"Output file is empty: {output_file_path}")

            LOGGER.info(f"Output file verification successful: {file_size} bytes")

        except Exception as e:
            LOGGER.error(f"Error saving results to Excel file: {e}")
            if isinstance(e, (ValueError, FileNotFoundError)):
                raise e
            raise ValueError(
                f"Failed to save optimization results to Excel file: {str(e)}"
            ) from e

    except Exception as e:
        LOGGER.error(f"Assortment optimization failed: {e}")
        raise e
