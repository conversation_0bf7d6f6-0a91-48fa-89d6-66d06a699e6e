[tool.poetry]
name = "magic_gateway"
version = "0.5.5"
description = "A unified gateway for ClickHouse and PostgreSQL queries"
authors = ["<PERSON>ov <<EMAIL>>"]
readme = "README.md"
packages = [{include = "magic_gateway", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
fastapi = "^0.115.12"
uvicorn = {extras = ["standard"], version = "^0.34.0"}
psycopg = {extras = ["binary", "pool"], version = "^3.2.6"}
ldap3 = "^2.9.1"
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
python-multipart = ">=0.0.6,<0.0.7"
pydantic-settings = "^2.8.1"
pydantic = ">=2.0.0"
pg_parser = {git = "https://gitlab.icmr.ru/dev/pg_parser.git"}
asyncpg = "0.30.0"
loguru = "^0.7.2"
pandas = "^2.2.3"
xlsxwriter = "^3.2.3"
openpyxl = "^3.1.2"
pyexcelerate = "^0.12.0"
pyarrow = "^20.0.0"
psutil = "^6.1.0"
clickhouse-driver = "^0.2.9"
aiohttp = "^3.10.0"
aiofiles = "^24.1.0"
clickhouse-connect = "^0.8.18"
httpx = "^0.28.1"
toml = "^0.10.2"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
black = "^25.1.0"
flake8 = "^7.2.0"
isort = "^6.0.1"
pytest-cov = "^4.1.0"
mypy = "^1.8.0"
pytest-asyncio = "^1.1.0"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --cov=magic_gateway --cov-report=term-missing"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
