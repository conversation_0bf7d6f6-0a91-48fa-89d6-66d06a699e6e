-- для хранения и использования потом в поправках
CREATE TEMPORARY TABLE final_KPI_rwbasis as 
SELECT
    position_number     as position_number,
    rwbasis             as rwbasis,
    sum(buyers_ww * c.BUF) AS buyers,
    any(population)       AS population,
    sum(trips_ww)       as trips_ww,
    sum(trips_fullmass) as trips_fullmass,
    any(projectc)       as projectc
FROM {{buyers_final}}  a
  INNER JOIN (
        WITH population AS (
            SELECT
                hhkey,
                sum(fullmass) / (age('month', toDate({start_date:DateTime}), toDate({end_date:DateTime})) + 1) AS weight_wave
            FROM pet.hh_weights_fullmass
            WHERE id_panel={id_panel:Integer} AND dt_start >= {start_date:DateTime} AND dt_end <= {end_date:DateTime}
            {% if hh_filter %}
                and hhkey in (SELECT hhkey FROM hh_filter_hhkey)
            {% endif%}    
            GROUP BY hhkey
        )
        SELECT
            hhkey,
            sum(weight_wave)
            OVER () AS population
        FROM population
    )  p on p.hhkey = a.hhkey
    INNER JOIN buyers_uplift c USING (position_number, rwbasis)
GROUP BY 
    rwbasis
    , position_number


