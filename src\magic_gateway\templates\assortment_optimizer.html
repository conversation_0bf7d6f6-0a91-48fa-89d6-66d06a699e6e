<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assortment Optimizer</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --success-color: #059669;
            --error-color: #dc2626;
            --warning-color: #d97706;
            --background: #f8fafc;
            --surface: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border: #e2e8f0;
            --border-focus: #3b82f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .header p {
            color: var(--text-secondary);
        }

        .form-container {
            background: var(--surface);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .form-group input {
            padding: 0.75rem;
            border: 2px solid var(--border);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--border-focus);
        }

        .form-group input:invalid {
            border-color: var(--error-color);
        }

        .form-group .help-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .field-error {
            font-size: 0.875rem;
            color: var(--error-color);
            margin-top: 0.25rem;
            display: none;
            font-weight: 500;
        }

        .form-group input.error {
            border-color: var(--error-color);
            background-color: #fef2f2;
        }

        .form-group input.success {
            border-color: var(--success-color);
        }

        .submit-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .submit-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.875rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
            min-width: 200px;
        }

        .submit-btn:hover:not(:disabled) {
            background: var(--primary-hover);
        }

        .submit-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
        }

        .status-message {
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-weight: 500;
            display: none;
        }

        .status-message.success {
            background: #ecfdf5;
            color: var(--success-color);
            border: 1px solid #a7f3d0;
            display: block;
        }

        .status-message.error {
            background: #fef2f2;
            color: var(--error-color);
            border: 1px solid #fecaca;
            display: block;
        }

        .status-message.loading {
            background: #eff6ff;
            color: var(--primary-color);
            border: 1px solid #bfdbfe;
            display: block;
        }

        .status-message.warning {
            background: #fffbeb;
            color: var(--warning-color);
            border: 1px solid #fed7aa;
            display: block;
        }

        .status-message .error-details {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }

        .status-message button {
            margin-left: 0.5rem;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .download-link {
            display: inline-block;
            background: var(--success-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin-top: 1rem;
            transition: background-color 0.2s ease;
        }

        .download-link:hover {
            background: #047857;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-container {
                padding: 1.5rem;
            }

            .status-message {
                font-size: 0.875rem;
            }

            .download-link {
                display: block;
                text-align: center;
                margin-top: 0.5rem;
            }
        }

        /* Focus indicators for accessibility */
        .form-group input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .submit-btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Loading state improvements */
        .submit-btn:disabled {
            opacity: 0.7;
        }

        /* Error state animations */
        .field-error {
            animation: slideDown 0.2s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Download button styling */
        .download-link:hover {
            background: var(--primary-hover) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .download-link:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Assortment Optimizer</h1>
            <p>Configure and run assortment optimization analysis</p>
        </div>

        <!-- Authentication Section -->
        <div id="authSection" class="form-container" style="display: none;">
            <h2>Authentication Required</h2>
            <p>Please log in to access the assortment optimization tool.</p>
            
            <form id="loginForm">
                <div class="form-grid" style="grid-template-columns: 1fr;">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                        <div class="help-text">Enter your LDAP username</div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                        <div class="help-text">Enter your LDAP password</div>
                    </div>
                </div>

                <div class="submit-section">
                    <button type="submit" class="submit-btn" id="loginBtn">
                        Log In
                    </button>
                    
                    <div id="loginStatusMessage" class="status-message"></div>
                </div>
            </form>
        </div>

        <!-- User Info Section -->
        <div id="userSection" class="form-container" style="display: none;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <div>
                    <span>Logged in as: <strong id="currentUser"></strong></span>
                </div>
                <button type="button" class="submit-btn" id="logoutBtn" style="background: var(--error-color); padding: 0.5rem 1rem;">
                    Log Out
                </button>
            </div>
        </div>

        <!-- Main Form Section -->
        <div id="mainSection" class="form-container" style="display: none;">
            <form id="optimizerForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="start_date">Start Date</label>
                        <input type="date" id="start_date" name="start_date" required>
                        <div class="help-text">Analysis start date</div>
                    </div>

                    <div class="form-group">
                        <label for="end_date">End Date</label>
                        <input type="date" id="end_date" name="end_date" required>
                        <div class="help-text">Analysis end date</div>
                    </div>

                    <div class="form-group">
                        <label for="json_axis_id">JSON Axis ID</label>
                        <input type="number" id="json_axis_id" name="json_axis_id" min="1" required>
                        <div class="help-text">Axis identifier for analysis</div>
                    </div>

                    <div class="form-group">
                        <label for="json_flt_hh_id">JSON Filter HH ID</label>
                        <input type="number" id="json_flt_hh_id" name="json_flt_hh_id" min="1">
                        <div class="help-text">Optional household filter ID</div>
                    </div>

                    <div class="form-group">
                        <label for="total_position_number">TOTAL Position Number</label>
                        <input type="number" id="total_position_number" name="total_position_number" min="1" value="1" required>
                        <div class="help-text">Position number for TOTAL</div>
                    </div>

                    <div class="form-group">
                        <label for="rest_position_number">Rest Position Number</label>
                        <input type="number" id="rest_position_number" name="rest_position_number" min="-1" value="-1" required>
                        <div class="help-text">Rest positions (-1 for no REST in axis)</div>
                    </div>

                    <div class="form-group">
                        <label for="id_panel">ID Panel</label>
                        <input type="number" id="id_panel" name="id_panel" min="1" value="1" required>
                        <div class="help-text">Panel identifier</div>
                    </div>
                </div>

                <div class="submit-section">
                    <button type="submit" class="submit-btn" id="submitBtn">
                        Run Optimization
                    </button>
                    
                    <div id="statusMessage" class="status-message"></div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let currentDownloadUrl = null;
        let retryCount = 0;
        const MAX_RETRIES = 3;
        const RETRY_DELAY = 2000; // 2 seconds
        let currentUser = null;
        let authToken = null;

        // Authentication functions
        async function refreshToken() {
            const refreshToken = localStorage.getItem('refresh_token');
            if (!refreshToken) {
                console.log('No refresh token available');
                return false;
            }

            try {
                console.log('Attempting to refresh token...');
                const response = await fetch('/api/v1/auth/refresh', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        refresh_token: refreshToken
                    })
                });

                if (response.ok) {
                    const tokenData = await response.json();
                    localStorage.setItem('jwt_token', tokenData.access_token);
                    if (tokenData.refresh_token) {
                        localStorage.setItem('refresh_token', tokenData.refresh_token);
                    }
                    authToken = tokenData.access_token;
                    console.log('Token refreshed successfully');
                    return true;
                } else {
                    console.log('Token refresh failed:', response.status);
                    // Refresh token is invalid, clear everything
                    localStorage.removeItem('jwt_token');
                    localStorage.removeItem('refresh_token');
                    return false;
                }
            } catch (error) {
                console.error('Token refresh error:', error);
                return false;
            }
        }

        async function checkAuthStatus() {
            const token = localStorage.getItem('jwt_token');
            if (!token) {
                showAuthSection();
                return false;
            }

            try {
                // Verify token by calling /me endpoint
                const refreshTokenValue = localStorage.getItem('refresh_token');
                const headers = {
                    'Authorization': `Bearer ${token}`
                };
                
                // Include refresh token header if available
                if (refreshTokenValue) {
                    headers['X-Refresh-Token'] = refreshTokenValue;
                }
                
                const response = await fetch('/api/v1/auth/me', {
                    headers: headers
                });

                if (response.ok) {
                    const user = await response.json();
                    currentUser = user;
                    authToken = token;
                    showMainSection();
                    return true;
                } else if (response.status === 401) {
                    // Token expired, try to refresh
                    console.log('Token expired, attempting refresh...');
                    const refreshSuccess = await refreshToken();
                    if (refreshSuccess) {
                        // Retry with new token
                        return await checkAuthStatus();
                    } else {
                        // Refresh failed, show auth section
                        showAuthSection();
                        return false;
                    }
                } else {
                    // Other error, remove token
                    localStorage.removeItem('jwt_token');
                    showAuthSection();
                    return false;
                }
            } catch (error) {
                console.error('Auth check failed:', error);
                // Try to refresh token on network error too
                const refreshSuccess = await refreshToken();
                if (refreshSuccess) {
                    return await checkAuthStatus();
                } else {
                    localStorage.removeItem('jwt_token');
                    showAuthSection();
                    return false;
                }
            }
        }

        function showAuthSection() {
            document.getElementById('authSection').style.display = 'block';
            document.getElementById('userSection').style.display = 'none';
            document.getElementById('mainSection').style.display = 'none';
        }

        function showMainSection() {
            document.getElementById('authSection').style.display = 'none';
            document.getElementById('userSection').style.display = 'block';
            document.getElementById('mainSection').style.display = 'block';
            
            // Update user info
            if (currentUser) {
                document.getElementById('currentUser').textContent = currentUser.username || 'Unknown';
            }
            
            // Setup form validation for the main form
            setupFormValidation();
        }

        async function login(username, password) {
            try {
                const response = await fetch('/api/v1/auth/login-json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                if (response.ok) {
                    const tokenData = await response.json();
                    localStorage.setItem('jwt_token', tokenData.access_token);
                    if (tokenData.refresh_token) {
                        localStorage.setItem('refresh_token', tokenData.refresh_token);
                    }
                    
                    // Check auth status to update UI
                    await checkAuthStatus();
                    return { success: true };
                } else {
                    const error = await response.json();
                    return { 
                        success: false, 
                        message: error.detail || 'Authentication failed' 
                    };
                }
            } catch (error) {
                console.error('Login error:', error);
                return { 
                    success: false, 
                    message: 'Network error. Please check your connection and try again.' 
                };
            }
        }

        function logout() {
            localStorage.removeItem('jwt_token');
            localStorage.removeItem('refresh_token');
            currentUser = null;
            authToken = null;
            showAuthSection();
            
            // Clear any status messages
            document.getElementById('statusMessage').innerHTML = '';
            document.getElementById('loginStatusMessage').innerHTML = '';
        }

        // Helper function for making authenticated requests with automatic token refresh
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('jwt_token');
            const refreshTokenValue = localStorage.getItem('refresh_token');
            
            if (!token) {
                throw new Error('No authentication token available');
            }

            // Add authorization and refresh token headers
            const headers = {
                ...options.headers,
                'Authorization': `Bearer ${token}`
            };
            
            // Include refresh token header if available (for server-side proactive refresh)
            if (refreshTokenValue) {
                headers['X-Refresh-Token'] = refreshTokenValue;
            }

            const requestOptions = {
                ...options,
                headers
            };

            try {
                const response = await fetch(url, requestOptions);
                
                if (response.status === 401) {
                    // Token expired, try to refresh
                    console.log('Request failed with 401, attempting token refresh...');
                    const refreshSuccess = await refreshToken();
                    
                    if (refreshSuccess) {
                        // Retry request with new token
                        const newToken = localStorage.getItem('jwt_token');
                        const newRefreshToken = localStorage.getItem('refresh_token');
                        
                        const retryHeaders = {
                            ...options.headers,
                            'Authorization': `Bearer ${newToken}`
                        };
                        
                        // Include refresh token header in retry as well
                        if (newRefreshToken) {
                            retryHeaders['X-Refresh-Token'] = newRefreshToken;
                        }
                        
                        const retryOptions = {
                            ...options,
                            headers: retryHeaders
                        };
                        
                        console.log('Retrying request with refreshed token...');
                        return await fetch(url, retryOptions);
                    } else {
                        // Refresh failed, redirect to login
                        console.log('Token refresh failed, redirecting to login');
                        showAuthSection();
                        throw new Error('Authentication expired. Please log in again.');
                    }
                }
                
                return response;
            } catch (error) {
                console.error('Authenticated request failed:', error);
                throw error;
            }
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const statusMessage = document.getElementById('loginStatusMessage');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<span class="loading-spinner"></span>Logging in...';
            statusMessage.className = 'status-message loading';
            statusMessage.innerHTML = 'Authenticating...';
            statusMessage.style.display = 'block';
            
            try {
                const result = await login(username, password);
                
                if (result.success) {
                    statusMessage.className = 'status-message success';
                    statusMessage.innerHTML = 'Login successful! Loading interface...';
                    
                    // Clear form
                    document.getElementById('username').value = '';
                    document.getElementById('password').value = '';
                } else {
                    statusMessage.className = 'status-message error';
                    statusMessage.innerHTML = `Login failed: ${result.message}`;
                }
            } catch (error) {
                statusMessage.className = 'status-message error';
                statusMessage.innerHTML = `Login error: ${error.message}`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.innerHTML = 'Log In';
            }
        });

        // Logout button handler
        document.getElementById('logoutBtn').addEventListener('click', function() {
            logout();
        });

        // Real-time form validation
        function setupFormValidation() {
            const form = document.getElementById('optimizerForm');
            const inputs = form.querySelectorAll('input[required]');
            
            inputs.forEach(input => {
                // Add error display elements
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.style.cssText = `
                    color: var(--error-color);
                    font-size: 0.875rem;
                    margin-top: 0.25rem;
                    display: none;
                `;
                input.parentNode.appendChild(errorDiv);
                
                // Real-time validation on input
                input.addEventListener('input', function() {
                    validateField(this);
                });
                
                // Validation on blur
                input.addEventListener('blur', function() {
                    validateField(this);
                });
            });
            
            // Date range validation
            const startDate = document.getElementById('start_date');
            const endDate = document.getElementById('end_date');
            
            startDate.addEventListener('change', function() {
                validateDateRange();
            });
            
            endDate.addEventListener('change', function() {
                validateDateRange();
            });
        }
        
        function validateField(field) {
            const errorDiv = field.parentNode.querySelector('.field-error');
            let isValid = true;
            let errorMessage = '';
            
            // Required field validation
            if (field.hasAttribute('required') && !field.value.trim()) {
                isValid = false;
                errorMessage = `${field.labels[0].textContent} is required`;
            }
            
            // Numeric field validation
            if (field.type === 'number' && field.value.trim()) {
                const value = parseInt(field.value);
                const min = parseInt(field.getAttribute('min'));
                
                if (isNaN(value)) {
                    isValid = false;
                    errorMessage = 'Must be a valid number';
                } else if (min !== null && value < min) {
                    isValid = false;
                    errorMessage = `Must be at least ${min}`;
                }
            }
            
            // Date validation
            if (field.type === 'date' && field.value.trim()) {
                const dateValue = new Date(field.value);
                if (isNaN(dateValue.getTime())) {
                    isValid = false;
                    errorMessage = 'Invalid date format';
                }
            }
            
            // Update field appearance and error message (only if errorDiv exists)
            if (errorDiv) {
                if (isValid) {
                    field.style.borderColor = 'var(--border)';
                    errorDiv.style.display = 'none';
                } else {
                    field.style.borderColor = 'var(--error-color)';
                    errorDiv.textContent = errorMessage;
                    errorDiv.style.display = 'block';
                }
            } else {
                // Fallback styling when error div doesn't exist
                if (isValid) {
                    field.style.borderColor = 'var(--border)';
                } else {
                    field.style.borderColor = 'var(--error-color)';
                }
            }
            
            return isValid;
        }
        
        function validateDateRange() {
            const startDate = document.getElementById('start_date');
            const endDate = document.getElementById('end_date');
            const startErrorDiv = startDate.parentNode.querySelector('.field-error');
            const endErrorDiv = endDate.parentNode.querySelector('.field-error');
            
            if (startDate.value && endDate.value) {
                const start = new Date(startDate.value);
                const end = new Date(endDate.value);
                
                if (start >= end) {
                    endDate.style.borderColor = 'var(--error-color)';
                    if (endErrorDiv) {
                        endErrorDiv.textContent = 'End date must be after start date';
                        endErrorDiv.style.display = 'block';
                    }
                    return false;
                } else {
                    endDate.style.borderColor = 'var(--border)';
                    if (endErrorDiv) {
                        endErrorDiv.style.display = 'none';
                    }
                    return true;
                }
            }
            return true;
        }
        
        function validateForm() {
            const form = document.getElementById('optimizerForm');
            const inputs = form.querySelectorAll('input');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });
            
            if (!validateDateRange()) {
                isValid = false;
            }
            
            return isValid;
        }
        
        // Enhanced error handling for network requests with authentication
        async function makeRequestWithRetry(url, options, attempt = 1) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 1500000); // 25 minute timeout (longer than server timeout)
                
                // Use authenticated request helper
                const response = await makeAuthenticatedRequest(url, {
                    ...options,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                return response;
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    throw new Error('Request timed out after 25 minutes. The optimization may be taking longer than expected. Please try again or contact support if the problem persists.');
                }
                
                // Handle authentication errors
                if (error.message.includes('Authentication expired')) {
                    throw error; // Don't retry auth errors, let them bubble up
                }
                
                if (attempt < MAX_RETRIES && isRetryableError(error)) {
                    showStatus('loading', `Connection failed, retrying... (${attempt}/${MAX_RETRIES})`);
                    await sleep(RETRY_DELAY * attempt); // Exponential backoff
                    return makeRequestWithRetry(url, options, attempt + 1);
                }
                
                throw error;
            }
        }
        
        function isRetryableError(error) {
            return error.name === 'TypeError' || // Network error
                   error.message.includes('fetch') ||
                   error.message.includes('network');
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // Enhanced error message formatting
        function formatErrorMessage(error, response = null) {
            if (typeof error === 'string') {
                return error;
            }
            
            if (error.detail) {
                if (typeof error.detail === 'object') {
                    if (error.detail.error) {
                        return error.detail.error;
                    }
                    if (error.detail.details) {
                        return error.detail.details;
                    }
                    return JSON.stringify(error.detail);
                }
                return error.detail;
            }
            
            if (error.message) {
                return error.message;
            }
            
            return 'An unexpected error occurred';
        }
        
        // Download error handling
        async function handleDownload(fileId, token) {
            try {
                showStatus('loading', '⏳ Preparing download...');
                
                const response = await makeRequestWithRetry(`/api/v1/scripts/download/${fileId}`, {
                    method: 'GET'
                });
                

                
                if (response.status === 404) {
                    throw new Error('Download file not found. The file may have expired or been deleted.');
                }
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(formatErrorMessage(errorData) || `Download failed with status ${response.status}`);
                }
                
                // Create download link
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `assortment_optimizer_results_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                // Clear the current download URL since file has been downloaded
                currentDownloadUrl = null;
                
                // Show success message that will auto-hide after 5 seconds
                showStatus('success', '✅ File downloaded successfully! You can now run another optimization if needed.');
                
            } catch (error) {
                console.error('Download error:', error);
                
                // Restore the download link if download failed
                if (currentDownloadUrl) {
                    showStatus('error', 
                        `❌ Download failed: ${error.message}<br>
                        <button onclick="handleDownload('${currentDownloadUrl}', authToken)" class="download-link" 
                            style="background: var(--primary-color); color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px; font-weight: bold;">
                            🔄 Try Download Again
                        </button>`
                    );
                } else {
                    showStatus('error', `❌ Download failed: ${error.message}`);
                }
            }
        }
        
        // Main form submission handler
        document.getElementById('optimizerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const statusMessage = document.getElementById('statusMessage');
            
            // Reset retry count
            retryCount = 0;
            
            // Validate form before submission
            if (!validateForm()) {
                showStatus('error', 'Please fix the validation errors above before submitting.');
                return;
            }
            
            // Get form data
            const formData = new FormData(this);
            const params = {};
            
            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    // Convert numeric fields
                    if (['json_axis_id', 'json_flt_hh_id', 'total_position_number', 'rest_position_number', 'id_panel'].includes(key)) {
                        const numValue = parseInt(value);
                        if (!isNaN(numValue)) {
                            params[key] = numValue;
                        }
                    } else {
                        params[key] = value;
                    }
                }
            }
            
            // Final validation check
            const requiredFields = ['start_date', 'end_date', 'json_axis_id', 'total_position_number', 'rest_position_number', 'id_panel'];
            const missingFields = requiredFields.filter(field => !params[field] && params[field] !== 0);
            
            if (missingFields.length > 0) {
                showStatus('error', `Please fill in required fields: ${missingFields.join(', ')}`);
                return;
            }
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner"></span>Running...';
            showStatus('loading', 'Starting optimization analysis...');
            
            try {
                // Use the stored auth token
                if (!authToken) {
                    throw new Error('Authentication required. Please log in first.');
                }
                
                // Make API request with retry logic
                const response = await makeRequestWithRetry('/api/v1/scripts/run/assortment_optimizer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ parameters: params })
                });
                
                // Handle authentication errors

                
                // Handle permission errors
                if (response.status === 403) {
                    throw new Error('You do not have permission to run this script. Please contact your administrator.');
                }
                
                // Handle server errors
                if (response.status >= 500) {
                    throw new Error('Server error occurred. Please try again later or contact support if the problem persists.');
                }
                
                // Parse response
                let result;
                try {
                    result = await response.json();
                } catch (parseError) {
                    throw new Error('Invalid response from server. Please try again.');
                }
                
                // Debug logging
                console.log('Script execution result:', result);
                console.log('result_file_id present:', !!result.result_file_id);
                console.log('download_url present:', !!result.download_url);
                
                // Handle API errors
                if (!response.ok) {
                    const errorMsg = formatErrorMessage(result);
                    throw new Error(errorMsg);
                }
                
                // Handle script execution errors
                if (result.error) {
                    throw new Error(`Script execution failed: ${result.error}`);
                }
                
                // Success - show download link if available
                if (result.result_file_id) {
                    currentDownloadUrl = result.result_file_id;
                    showStatus('success', 
                        `✅ Optimization completed successfully! Your results are ready.<br>
                        <button onclick="handleDownload('${result.result_file_id}', authToken)" class="download-link" 
                            style="background: var(--primary-color); color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px; font-weight: bold;">
                            📥 Download Results
                        </button>
                        <div style="margin-top: 8px; font-size: 0.9em; color: var(--text-secondary);">
                            💡 This download link will remain available until you download the file.
                        </div>`
                    );
                } else {
                    showStatus('success', 'Optimization completed successfully! However, no result file was generated. Please check the console for details or contact support.');
                }
                
            } catch (error) {
                console.error('Execution error:', error);
                
                // Enhanced error categorization
                let userMessage = error.message;
                
                if (error.message.includes('fetch')) {
                    userMessage = 'Network connection failed. Please check your internet connection and try again.';
                } else if (error.message.includes('timeout')) {
                    userMessage = 'Request timed out. The optimization process may be taking longer than expected. Please try again in a few moments.';
                } else if (error.message.includes('JSON')) {
                    userMessage = 'Invalid server response. Please try again or contact support if the problem persists.';
                }
                
                showStatus('error', `Error: ${userMessage}`);
                
            } finally {
                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Run Optimization';
            }
        });
        
        function showStatus(type, message) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.className = `status-message ${type}`;
            statusMessage.innerHTML = message;
            
            // Always ensure the status message is visible when showing new content
            statusMessage.style.display = 'block';
            
            // Auto-hide success messages after 10 seconds, but NOT if they contain download links
            if (type === 'success' && !message.includes('Download Results')) {
                setTimeout(() => {
                    if (statusMessage.className.includes('success') && !statusMessage.innerHTML.includes('Download Results')) {
                        statusMessage.style.display = 'none';
                    }
                }, 10000);
            }
        }
        
        // Periodic token refresh check (every 5 minutes)
        function startTokenRefreshTimer() {
            setInterval(async () => {
                const token = localStorage.getItem('jwt_token');
                const refreshToken = localStorage.getItem('refresh_token');
                
                if (token && refreshToken && currentUser) {
                    try {
                        // Check if token is still valid
                        const headers = {
                            'Authorization': `Bearer ${token}`
                        };
                        
                        // Include refresh token header if available
                        if (refreshToken) {
                            headers['X-Refresh-Token'] = refreshToken;
                        }
                        
                        const response = await fetch('/api/v1/auth/me', {
                            headers: headers
                        });
                        
                        if (response.status === 401) {
                            // Token expired, refresh it
                            console.log('Periodic check: Token expired, refreshing...');
                            const refreshSuccess = await refreshToken();
                            if (refreshSuccess) {
                                console.log('Periodic token refresh successful');
                                // Update authToken variable
                                authToken = localStorage.getItem('jwt_token');
                            } else {
                                console.log('Periodic token refresh failed, user will need to re-login');
                            }
                        }
                    } catch (error) {
                        console.log('Periodic token check failed:', error);
                    }
                }
            }, 5 * 60 * 1000); // Check every 5 minutes
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', async function() {
            // Check authentication status first
            const isAuthenticated = await checkAuthStatus();
            
            // Only setup form validation if authenticated
            if (isAuthenticated) {
                setupFormValidation();
                // Start periodic token refresh timer
                startTokenRefreshTimer();
            }
        });
    </script>
</body>
</html>