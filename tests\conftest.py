"""Test configuration for the MagicGateway application."""

import asyncio
import os
from typing import Async<PERSON><PERSON>ator, Dict, Any, Generator

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from httpx import AsyncClient

from magic_gateway.core.config import settings
from magic_gateway.main import app


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_app() -> FastAPI:
    """Get the FastAPI application for testing."""
    return app


@pytest.fixture
def client(test_app: FastAPI) -> Generator[TestClient, None, None]:
    """Get a TestClient instance for testing."""
    with TestClient(test_app) as client:
        yield client


@pytest.fixture
async def async_client(test_app: FastAPI) -> AsyncGenerator[AsyncClient, None]:
    """Get an AsyncClient instance for testing."""
    async with Async<PERSON>lient(app=test_app, base_url="http://test") as client:
        yield client


@pytest.fixture
async def admin_token(async_client: AsyncClient) -> str:
    """Get an admin token for testing."""
    # This would normally authenticate with the real system
    # For testing, we'll create a token directly
    from magic_gateway.auth.jwt import create_access_token
    
    token_data = {
        "sub": "admin",
        "is_admin": True,
        "auth_source": "test",
    }
    
    return create_access_token(token_data)


@pytest.fixture
async def user_token(async_client: AsyncClient) -> str:
    """Get a user token for testing."""
    # This would normally authenticate with the real system
    # For testing, we'll create a token directly
    from magic_gateway.auth.jwt import create_access_token
    
    token_data = {
        "sub": "user",
        "is_admin": False,
        "auth_source": "test",
    }
    
    return create_access_token(token_data)
