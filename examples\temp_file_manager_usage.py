#!/usr/bin/env python3
"""
Example usage of the universal TempFileManager.

This example demonstrates the flexible naming capabilities and different
use cases for the updated temp file manager.
"""

from magic_gateway.utils.temp_file_manager import temp_file_manager, FileCategory

def demonstrate_flexible_naming():
    """Demonstrate different naming patterns and categories."""
    
    print("=== Universal TempFileManager Usage Examples ===\n")
    
    # 1. Script result files
    print("1. Script Result Files:")
    script_file_id = temp_file_manager.create_script_result_file(
        content="Script execution results",
        script_name="assortment_optimizer"
    )
    print(f"   Created script result: {script_file_id}")
    
    # 2. Export files with different formats
    print("\n2. Export Files:")
    excel_export_id = temp_file_manager.create_export_file(
        content="Excel export data",
        export_name="job_123_export",
        format_ext="xlsx"
    )
    print(f"   Created Excel export: {excel_export_id}")
    
    csv_export_id = temp_file_manager.create_export_file(
        content="CSV export data",
        export_name="job_456_export", 
        format_ext="csv"
    )
    print(f"   Created CSV export: {csv_export_id}")
    
    # 3. Processing files with custom naming
    print("\n3. Processing Files:")
    processing_path = temp_file_manager.get_processing_path(
        file_id="proc_001",
        process_name="data_transformation",
        format_ext="parquet"
    )
    print(f"   Processing path: {processing_path}")
    
    # 4. Upload files
    print("\n4. Upload Files:")
    upload_id = temp_file_manager.create_upload_file(
        content="Uploaded file content",
        original_filename="user_data.xlsx"
    )
    print(f"   Created upload file: {upload_id}")
    
    # 5. Cache files
    print("\n5. Cache Files:")
    cache_id = temp_file_manager.create_cache_file(
        content={"cached": "data"},
        cache_key="user_123_preferences"
    )
    print(f"   Created cache file: {cache_id}")
    
    # 6. Custom patterns
    print("\n6. Custom Patterns:")
    custom_file_id = temp_file_manager.save_result_file(
        content="Custom pattern content",
        name="report",
        custom_pattern="{prefix}{name}_{timestamp}.{extension}",
        prefix="monthly_",
        extension="pdf"
    )
    print(f"   Created custom pattern file: {custom_file_id}")
    
    # 7. List all files
    print("\n7. All Temporary Files:")
    all_files = temp_file_manager.list_temp_files()
    for file_info in all_files:
        print(f"   - {file_info['filename']} ({file_info['size_bytes']} bytes)")
    
    print(f"\nTotal files created: {len(all_files)}")

def demonstrate_path_generation():
    """Demonstrate different path generation methods."""
    
    print("\n=== Path Generation Examples ===\n")
    
    # Export paths with different extensions
    export_paths = [
        temp_file_manager.get_export_path("exp_001", "sales_report", "xlsx"),
        temp_file_manager.get_export_path("exp_002", "inventory_data", "csv"),
        temp_file_manager.get_export_path("exp_003", "analytics_dump", "parquet")
    ]
    
    print("Export paths:")
    for path in export_paths:
        print(f"   {path}")
    
    # Processing paths
    processing_paths = [
        temp_file_manager.get_processing_path("proc_001", "data_cleaning", "tmp"),
        temp_file_manager.get_processing_path("proc_002", "feature_extraction", "json"),
        temp_file_manager.get_processing_path("proc_003", "model_training", "pkl")
    ]
    
    print("\nProcessing paths:")
    for path in processing_paths:
        print(f"   {path}")

def demonstrate_categories():
    """Demonstrate different file categories."""
    
    print("\n=== File Categories Examples ===\n")
    
    categories = [
        (FileCategory.SCRIPT_RESULT, "optimization_results"),
        (FileCategory.EXPORT, "quarterly_report"),
        (FileCategory.UPLOAD, "user_upload"),
        (FileCategory.PROCESSING, "temp_processing"),
        (FileCategory.CACHE, "session_cache"),
        (FileCategory.REPORT, "monthly_summary")
    ]
    
    for category, name in categories:
        file_id = temp_file_manager.save_result_file(
            content=f"Content for {category.value}",
            name=name,
            category=category
        )
        file_info = temp_file_manager.get_file_info(file_id)
        if file_info:
            print(f"{category.value:15} -> {file_info['filename']}")

if __name__ == "__main__":
    demonstrate_flexible_naming()
    demonstrate_path_generation()
    demonstrate_categories()
    
    print("\n=== Cleanup ===")
    cleaned_count = temp_file_manager.cleanup_old_files(max_age_hours=0)  # Clean all files
    print(f"Cleaned up {cleaned_count} files")