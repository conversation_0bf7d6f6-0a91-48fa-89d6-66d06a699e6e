[project]
name = "assort-optimize"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",email = "Kirill<PERSON><EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<4.0"
dependencies = [
    "python-dotenv (>=1.1.1,<2.0.0)",
    "clickhouse-connect (>=0.8.18,<0.9.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "jinja2 (>=3.1.6,<4.0.0)",
    "pandas (>=2.3.0,<3.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "xlsxwriter (>=3.2.5,<4.0.0)",
    "toml (>=0.10.2,<0.11.0)"
]

[tool.poetry]
packages = [{include = "assort_optimize", from = "src"}]


[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
