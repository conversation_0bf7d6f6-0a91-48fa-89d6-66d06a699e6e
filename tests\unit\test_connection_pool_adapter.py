"""
Unit tests for ConnectionPoolAdapter.

This module tests the connection pool selection logic, cluster detection,
and fallback mechanisms of the ConnectionPoolAdapter class.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

from magic_gateway.export.adapters.connection_pool import Connection<PERSON>ool<PERSON>dapter
from magic_gateway.export.exceptions import ExportError
from magic_gateway.export.models import ConnectionSelection
from magic_gateway.db.connection_manager import ClickHouseConnectionManager


class TestConnectionPoolAdapter:
    """Test cases for ConnectionPoolAdapter class."""

    @pytest.fixture
    def mock_registry(self):
        """Create a mock ClickHouse connection registry."""
        registry = MagicMock()
        registry.initialized = True
        registry.get_available_clusters.return_value = ["primary", "test_cluster"]
        return registry

    @pytest.fixture
    def mock_primary_manager(self):
        """Create a mock primary connection manager."""
        manager = MagicMock(spec=ClickHouseConnectionManager)
        manager.initialized = True
        manager.cluster_name = "primary"
        return manager

    @pytest.fixture
    def mock_cluster_manager(self):
        """Create a mock cluster connection manager."""
        manager = MagicMock(spec=ClickHouseConnectionManager)
        manager.initialized = True
        manager.cluster_name = "test_cluster"
        return manager

    @pytest.fixture
    def adapter(self, mock_registry):
        """Create a ConnectionPoolAdapter instance with mocked registry."""
        return ConnectionPoolAdapter(registry=mock_registry)

    def test_init_with_custom_registry(self, mock_registry):
        """Test adapter initialization with custom registry."""
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        assert adapter.registry is mock_registry

    @patch('magic_gateway.export.adapters.connection_pool.clickhouse_registry')
    def test_init_with_default_registry(self, mock_global_registry):
        """Test adapter initialization with default global registry."""
        adapter = ConnectionPoolAdapter()
        assert adapter.registry is mock_global_registry

    @pytest.mark.asyncio
    async def test_get_optimal_connection_registry_not_initialized(self, adapter):
        """Test error handling when registry is not initialized."""
        adapter.registry.initialized = False
        
        with pytest.raises(ExportError) as exc_info:
            await adapter.get_optimal_connection("test_table")
        
        error = exc_info.value
        assert error.error_type == "registry_not_initialized"
        assert "not initialized" in error.message
        assert "test_table" in error.context["table_name"]

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_get_optimal_connection_cluster_routing(
        self, mock_get_cluster, adapter, mock_cluster_manager
    ):
        """Test successful cluster routing based on table name."""
        # Setup mocks
        mock_get_cluster.return_value = "test_cluster"
        adapter.registry.get_manager.return_value = mock_cluster_manager
        
        # Execute
        result = await adapter.get_optimal_connection("job_result.test_table")
        
        # Verify
        assert isinstance(result, ConnectionSelection)
        assert result.manager is mock_cluster_manager
        assert result.cluster_name == "test_cluster"
        assert result.is_fallback is False
        assert "Table routing" in result.routing_reason
        
        mock_get_cluster.assert_called_once_with("job_result.test_table")
        adapter.registry.get_manager.assert_called_once_with("test_cluster")

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_get_optimal_connection_primary_routing(
        self, mock_get_cluster, adapter, mock_primary_manager
    ):
        """Test routing to primary connection when no cluster is needed."""
        # Setup mocks
        mock_get_cluster.return_value = None
        adapter.registry.get_manager.return_value = mock_primary_manager
        
        # Execute
        result = await adapter.get_optimal_connection("default.test_table")
        
        # Verify
        assert isinstance(result, ConnectionSelection)
        assert result.manager is mock_primary_manager
        assert result.cluster_name is None
        assert result.is_fallback is False
        assert "Primary connection" in result.routing_reason
        
        adapter.registry.get_manager.assert_called_once_with("primary")

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_get_optimal_connection_cluster_fallback(
        self, mock_get_cluster, adapter, mock_primary_manager
    ):
        """Test fallback to primary when cluster connection fails."""
        # Setup mocks
        mock_get_cluster.return_value = "test_cluster"
        adapter.registry.get_manager.side_effect = [
            Exception("Cluster not available"),  # First call for cluster fails
            mock_primary_manager  # Second call for primary succeeds
        ]
        
        # Execute
        result = await adapter.get_optimal_connection("job_result.test_table")
        
        # Verify
        assert isinstance(result, ConnectionSelection)
        assert result.manager is mock_primary_manager
        assert result.cluster_name is None
        assert result.is_fallback is True
        assert "Fallback to primary" in result.routing_reason
        
        # Verify both calls were made
        assert adapter.registry.get_manager.call_count == 2
        adapter.registry.get_manager.assert_any_call("test_cluster")
        adapter.registry.get_manager.assert_any_call("primary")

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_get_optimal_connection_cluster_not_initialized(
        self, mock_get_cluster, adapter, mock_primary_manager
    ):
        """Test fallback when cluster manager is not initialized."""
        # Setup mocks
        mock_get_cluster.return_value = "test_cluster"
        mock_cluster_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_cluster_manager.initialized = False  # Not initialized
        
        adapter.registry.get_manager.side_effect = [
            mock_cluster_manager,  # First call returns uninitialized manager
            mock_primary_manager   # Second call for primary succeeds
        ]
        
        # Execute
        result = await adapter.get_optimal_connection("job_result.test_table")
        
        # Verify fallback occurred
        assert result.manager is mock_primary_manager
        assert result.is_fallback is True

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_get_optimal_connection_primary_not_initialized(
        self, mock_get_cluster, adapter
    ):
        """Test error when primary connection is not initialized."""
        # Setup mocks
        mock_get_cluster.return_value = None
        mock_primary_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_primary_manager.initialized = False
        adapter.registry.get_manager.return_value = mock_primary_manager
        
        # Execute and verify error
        with pytest.raises(ExportError) as exc_info:
            await adapter.get_optimal_connection("default.test_table")
        
        error = exc_info.value
        assert error.error_type == "primary_connection_unavailable"
        assert "not initialized" in error.message

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_get_optimal_connection_no_connection_available(
        self, mock_get_cluster, adapter
    ):
        """Test error when no connection is available."""
        # Setup mocks
        mock_get_cluster.return_value = None
        adapter.registry.get_manager.side_effect = Exception("Connection failed")
        adapter.registry.get_available_clusters.return_value = ["primary"]
        
        # Execute and verify error
        with pytest.raises(ExportError) as exc_info:
            await adapter.get_optimal_connection("default.test_table")
        
        error = exc_info.value
        assert error.error_type == "no_connection_available"
        assert "No ClickHouse connection available" in error.message

    @pytest.mark.asyncio
    async def test_validate_connection_health_success(self, adapter):
        """Test successful connection health validation."""
        # Setup mock connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection context manager
        mock_conn = MagicMock()
        mock_conn.execute.return_value = [(1,)]  # Health check result
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Execute
        result = await adapter.validate_connection_health(mock_manager)
        
        # Verify
        assert result is True
        mock_conn.execute.assert_called_once_with("SELECT 1 as health_check")

    @pytest.mark.asyncio
    async def test_validate_connection_health_not_initialized(self, adapter):
        """Test health validation with uninitialized manager."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = False
        
        result = await adapter.validate_connection_health(mock_manager)
        
        assert result is False

    @pytest.mark.asyncio
    async def test_validate_connection_health_query_fails(self, adapter):
        """Test health validation when query fails."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup connection to raise exception
        mock_manager.connection.return_value.__aenter__.side_effect = Exception("Connection failed")
        
        result = await adapter.validate_connection_health(mock_manager)
        
        assert result is False

    @pytest.mark.asyncio
    async def test_validate_connection_health_unexpected_result(self, adapter):
        """Test health validation with unexpected query result."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection with unexpected result
        mock_conn = MagicMock()
        mock_conn.execute.return_value = [(2,)]  # Wrong result
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        result = await adapter.validate_connection_health(mock_manager)
        
        assert result is False

    def test_get_available_clusters_initialized(self, adapter):
        """Test getting available clusters when registry is initialized."""
        expected_clusters = ["primary", "test_cluster"]
        adapter.registry.get_available_clusters.return_value = expected_clusters
        
        result = adapter.get_available_clusters()
        
        assert result == expected_clusters

    def test_get_available_clusters_not_initialized(self, adapter):
        """Test getting available clusters when registry is not initialized."""
        adapter.registry.initialized = False
        
        result = adapter.get_available_clusters()
        
        assert result == []

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_success(self, adapter):
        """Test successful parquet streaming export."""
        # Setup mock connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection
        mock_conn = MagicMock()
        mock_conn.execute.return_value = b"parquet_data"
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Mock health check
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            # Execute
            result_generator = adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123"
            )
            
            # Collect results
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify
        assert len(results) == 1
        assert results[0] == b"parquet_data"
        mock_conn.execute.assert_called_once_with("SELECT * FROM test_table FORMAT Parquet")

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_manager_not_initialized(self, adapter):
        """Test error when connection manager is not initialized."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = False
        
        with pytest.raises(ExportError) as exc_info:
            async for _ in adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123"
            ):
                pass
        
        error = exc_info.value
        assert error.error_type == "connection_not_initialized"

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_unhealthy_connection(self, adapter):
        """Test error when connection fails health check."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Mock failed health check
        with patch.object(adapter, 'validate_connection_health', return_value=False):
            with pytest.raises(ExportError) as exc_info:
                async for _ in adapter.export_to_parquet_stream(
                    query="SELECT * FROM test_table",
                    connection_manager=mock_manager,
                    query_id="test_query_123"
                ):
                    pass
        
        error = exc_info.value
        assert error.error_type == "connection_unhealthy"

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_query_execution_fails(self, adapter):
        """Test error handling when query execution fails."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection that fails
        mock_conn = MagicMock()
        mock_conn.execute.side_effect = Exception("Query failed")
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Mock successful health check
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            with pytest.raises(ExportError) as exc_info:
                async for _ in adapter.export_to_parquet_stream(
                    query="SELECT * FROM test_table",
                    connection_manager=mock_manager,
                    query_id="test_query_123"
                ):
                    pass
        
        error = exc_info.value
        assert error.error_type == "query_execution_failed"
        assert "Query failed" in error.message

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_string_result(self, adapter):
        """Test handling of string result from query execution."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection with string result
        mock_conn = MagicMock()
        mock_conn.execute.return_value = "string_result"
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Mock health check
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            result_generator = adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123"
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify string was encoded to bytes
        assert len(results) == 1
        assert results[0] == b"string_result"

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_with_request_id(self, adapter):
        """Test parquet streaming with custom request ID."""
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        mock_conn = MagicMock()
        mock_conn.execute.return_value = b"data"
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        request_id = "custom_request_123"
        
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            result_generator = adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123",
                request_id=request_id
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        assert len(results) == 1
        assert results[0] == b"data"


class TestConnectionPoolAdapterIntegration:
    """Integration-style tests for ConnectionPoolAdapter."""

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_full_workflow_cluster_to_primary_fallback(self, mock_get_cluster):
        """Test complete workflow from cluster selection to primary fallback."""
        # Setup registry with both managers
        registry = MagicMock()
        registry.initialized = True
        registry.get_available_clusters.return_value = ["primary", "test_cluster"]
        
        # Setup cluster manager that fails
        cluster_manager = MagicMock(spec=ClickHouseConnectionManager)
        cluster_manager.initialized = False
        
        # Setup primary manager that works
        primary_manager = MagicMock(spec=ClickHouseConnectionManager)
        primary_manager.initialized = True
        primary_manager.cluster_name = "primary"
        
        # Configure registry to return appropriate managers
        def get_manager_side_effect(cluster_name):
            if cluster_name == "test_cluster":
                return cluster_manager
            elif cluster_name == "primary":
                return primary_manager
            else:
                raise Exception(f"Unknown cluster: {cluster_name}")
        
        registry.get_manager.side_effect = get_manager_side_effect
        
        # Setup table routing to prefer cluster
        mock_get_cluster.return_value = "test_cluster"
        
        # Create adapter and test
        adapter = ConnectionPoolAdapter(registry=registry)
        result = await adapter.get_optimal_connection("job_result.test_table")
        
        # Verify fallback occurred
        assert result.manager is primary_manager
        assert result.cluster_name is None
        assert result.is_fallback is True
        assert "not initialized" in result.routing_reason

    @pytest.mark.asyncio
    async def test_request_id_propagation(self):
        """Test that request IDs are properly propagated through error handling."""
        registry = MagicMock()
        registry.initialized = False
        
        adapter = ConnectionPoolAdapter(registry=registry)
        custom_request_id = "test_request_456"
        
        with pytest.raises(ExportError) as exc_info:
            await adapter.get_optimal_connection("test_table", request_id=custom_request_id)
        
        error = exc_info.value
        assert error.request_id == custom_request_id


class TestConnectionPoolAdapterStreaming:
    """Test cases for streaming capabilities and fallback mechanisms."""

    @pytest.fixture
    def adapter_with_mock_registry(self):
        """Create adapter with a fully mocked registry for streaming tests."""
        registry = MagicMock()
        registry.initialized = True
        registry.get_available_clusters.return_value = ["primary", "cluster"]
        return ConnectionPoolAdapter(registry=registry)

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_with_streaming_support(self, adapter_with_mock_registry):
        """Test streaming when connection manager supports execute_stream."""
        adapter = adapter_with_mock_registry
        
        # Setup mock connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection with streaming support
        mock_conn = MagicMock()
        
        async def mock_stream(query):
            yield b"chunk1"
            yield b"chunk2"
            yield b"chunk3"
        
        # Mock execute_stream to return the async generator directly
        mock_conn.execute_stream = mock_stream
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Mock health check
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            result_generator = adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123"
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify streaming was used and all chunks received
        assert len(results) == 3
        assert results == [b"chunk1", b"chunk2", b"chunk3"]

    @pytest.mark.asyncio
    async def test_export_to_parquet_stream_fallback_to_regular_execution(self, adapter_with_mock_registry):
        """Test fallback to regular execution when streaming is not supported."""
        adapter = adapter_with_mock_registry
        
        # Setup mock connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection without streaming support
        mock_conn = MagicMock()
        # Don't add execute_stream method to simulate lack of streaming support
        mock_conn.execute.return_value = b"regular_execution_result"
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Mock health check
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            result_generator = adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123"
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify fallback to regular execution
        assert len(results) == 1
        assert results[0] == b"regular_execution_result"
        mock_conn.execute.assert_called_once_with("SELECT * FROM test_table FORMAT Parquet")

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_export_to_parquet_stream_with_fallback_success_first_attempt(
        self, mock_get_cluster, adapter_with_mock_registry
    ):
        """Test successful streaming on first attempt with fallback method."""
        adapter = adapter_with_mock_registry
        
        # Setup table routing
        mock_get_cluster.return_value = "cluster"
        
        # Setup successful connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        adapter.registry.get_manager.return_value = mock_manager
        
        # Mock the streaming method to succeed
        async def mock_stream():
            yield b"success_data"
        
        with patch.object(adapter, 'export_to_parquet_stream', return_value=mock_stream()):
            result_generator = adapter.export_to_parquet_stream_with_fallback(
                query="SELECT * FROM test_table",
                table_name="job_result.test_table",
                query_id="test_query_123"
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify success on first attempt
        assert len(results) == 1
        assert results[0] == b"success_data"

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_export_to_parquet_stream_with_fallback_retry_on_connection_error(
        self, mock_get_cluster, adapter_with_mock_registry
    ):
        """Test retry mechanism when connection pool is exhausted."""
        adapter = adapter_with_mock_registry
        
        # Setup table routing
        mock_get_cluster.return_value = "cluster"
        
        # Setup connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        adapter.registry.get_manager.return_value = mock_manager
        
        # Mock streaming to fail first time, succeed second time
        call_count = 0
        
        async def mock_stream_with_failure(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ConnectionPoolExhaustedError(
                    request_id="test_request",
                    pool_name="test_pool",
                    active_connections=10,
                    max_connections=10
                )
            else:
                yield b"retry_success_data"
        
        with patch.object(adapter, 'export_to_parquet_stream', side_effect=mock_stream_with_failure):
            result_generator = adapter.export_to_parquet_stream_with_fallback(
                query="SELECT * FROM test_table",
                table_name="job_result.test_table",
                query_id="test_query_123",
                max_retries=2
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify retry succeeded
        assert len(results) == 1
        assert results[0] == b"retry_success_data"
        assert call_count == 2

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_export_to_parquet_stream_with_fallback_max_retries_exceeded(
        self, mock_get_cluster, adapter_with_mock_registry
    ):
        """Test failure when max retries are exceeded."""
        adapter = adapter_with_mock_registry
        
        # Setup table routing
        mock_get_cluster.return_value = "cluster"
        
        # Setup connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        adapter.registry.get_manager.return_value = mock_manager
        
        # Mock streaming to always fail
        async def mock_stream_always_fail(*args, **kwargs):
            raise ConnectionTimeoutError(
                request_id="test_request",
                timeout_seconds=30.0,
                operation="parquet_export"
            )
        
        with patch.object(adapter, 'export_to_parquet_stream', side_effect=mock_stream_always_fail):
            with pytest.raises(ExportError) as exc_info:
                result_generator = adapter.export_to_parquet_stream_with_fallback(
                    query="SELECT * FROM test_table",
                    table_name="job_result.test_table",
                    query_id="test_query_123",
                    max_retries=1
                )
                
                async for _ in result_generator:
                    pass
        
        error = exc_info.value
        assert error.error_type == "streaming_failed_all_connections"
        assert "after 2 attempts" in error.message

    @pytest.mark.asyncio
    @patch('magic_gateway.export.adapters.connection_pool.get_cluster_for_table')
    async def test_export_to_parquet_stream_with_fallback_no_retry_on_export_error(
        self, mock_get_cluster, adapter_with_mock_registry
    ):
        """Test that non-connection ExportErrors are not retried."""
        adapter = adapter_with_mock_registry
        
        # Setup table routing
        mock_get_cluster.return_value = "cluster"
        
        # Setup connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        adapter.registry.get_manager.return_value = mock_manager
        
        # Mock streaming to fail with non-connection error
        async def mock_stream_export_error(*args, **kwargs):
            raise ExportError(
                error_type="query_execution_failed",
                message="SQL syntax error",
                context={},
                request_id="test_request",
                recovery_suggestions=[]
            )
        
        with patch.object(adapter, 'export_to_parquet_stream', side_effect=mock_stream_export_error):
            with pytest.raises(ExportError) as exc_info:
                result_generator = adapter.export_to_parquet_stream_with_fallback(
                    query="SELECT * FROM test_table",
                    table_name="job_result.test_table",
                    query_id="test_query_123",
                    max_retries=2
                )
                
                async for _ in result_generator:
                    pass
        
        # Should get the original error, not a retry failure
        error = exc_info.value
        assert error.error_type == "query_execution_failed"
        assert "SQL syntax error" in error.message

    @pytest.mark.asyncio
    async def test_validate_connection_health_with_different_result_formats(self, adapter_with_mock_registry):
        """Test connection health validation with various result formats."""
        adapter = adapter_with_mock_registry
        
        # Test with tuple result (expected format)
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        mock_conn = MagicMock()
        mock_conn.execute.return_value = [(1,)]
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        result = await adapter.validate_connection_health(mock_manager)
        assert result is True
        
        # Test with list result
        mock_conn.execute.return_value = [[1]]
        result = await adapter.validate_connection_health(mock_manager)
        assert result is True
        
        # Test with empty result
        mock_conn.execute.return_value = []
        result = await adapter.validate_connection_health(mock_manager)
        assert result is False
        
        # Test with None result
        mock_conn.execute.return_value = None
        result = await adapter.validate_connection_health(mock_manager)
        assert result is False

    @pytest.mark.asyncio
    async def test_connection_health_validation_integration(self, adapter_with_mock_registry):
        """Test that health validation is properly integrated into streaming."""
        adapter = adapter_with_mock_registry
        
        # Setup mock connection manager that fails health check
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Mock health check to fail
        with patch.object(adapter, 'validate_connection_health', return_value=False):
            with pytest.raises(ExportError) as exc_info:
                async for _ in adapter.export_to_parquet_stream(
                    query="SELECT * FROM test_table",
                    connection_manager=mock_manager,
                    query_id="test_query_123"
                ):
                    pass
        
        error = exc_info.value
        assert error.error_type == "connection_unhealthy"
        assert "failed health check" in error.message

    @pytest.mark.asyncio
    async def test_streaming_with_mixed_data_types(self, adapter_with_mock_registry):
        """Test streaming with different data types in chunks."""
        adapter = adapter_with_mock_registry
        
        # Setup mock connection manager
        mock_manager = MagicMock(spec=ClickHouseConnectionManager)
        mock_manager.initialized = True
        
        # Setup mock connection with mixed data types
        mock_conn = MagicMock()
        
        async def mock_mixed_stream(query):
            yield b"bytes_chunk"      # bytes
            yield "string_chunk"      # string
            yield 12345              # integer
            yield ["list", "data"]   # list
        
        mock_conn.execute_stream = mock_mixed_stream
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None
        
        # Mock health check
        with patch.object(adapter, 'validate_connection_health', return_value=True):
            result_generator = adapter.export_to_parquet_stream(
                query="SELECT * FROM test_table",
                connection_manager=mock_manager,
                query_id="test_query_123"
            )
            
            results = []
            async for chunk in result_generator:
                results.append(chunk)
        
        # Verify all data types are converted to bytes
        assert len(results) == 4
        assert results[0] == b"bytes_chunk"
        assert results[1] == b"string_chunk"
        assert results[2] == b"12345"
        assert results[3] == b"['list', 'data']"