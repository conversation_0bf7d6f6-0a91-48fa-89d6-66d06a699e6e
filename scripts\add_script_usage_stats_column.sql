-- Migration script to add script_usage_stats column to connection_pool_monitoring table
-- Run this script as a user with appropriate permissions on the logs database

-- Add the script_usage_stats column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'api' 
        AND table_name = 'connection_pool_monitoring' 
        AND column_name = 'script_usage_stats'
    ) THEN
        ALTER TABLE api.connection_pool_monitoring 
        ADD COLUMN script_usage_stats JSONB;
        
        -- Add a comment to describe the column
        COMMENT ON COLUMN api.connection_pool_monitoring.script_usage_stats IS 'Script-specific usage statistics in JSON format';
        
        -- Create an index for better query performance on script stats
        CREATE INDEX IF NOT EXISTS idx_connection_pool_monitoring_script_stats 
        ON api.connection_pool_monitoring USING GIN (script_usage_stats);
        
        RAISE NOTICE 'Added script_usage_stats column to api.connection_pool_monitoring table';
    ELSE
        RAISE NOTICE 'script_usage_stats column already exists in api.connection_pool_monitoring table';
    END IF;
END
$$;

-- Update the daily aggregated view to include script usage stats
CREATE OR REPLACE VIEW api.connection_pool_monitoring_daily AS
SELECT 
    DATE_TRUNC('day', timestamp) AS day,
    pool_type,
    AVG(active_connections) AS avg_active_connections,
    MAX(active_connections) AS max_active_connections,
    AVG(idle_connections) AS avg_idle_connections,
    MAX(idle_connections) AS max_idle_connections,
    MAX(total_connections_created) AS total_connections_created,
    MAX(total_connections_closed) AS total_connections_closed,
    SUM(connection_timeouts) AS connection_timeouts,
    SUM(connection_errors) AS connection_errors,
    AVG(avg_acquisition_time_ms) AS avg_acquisition_time_ms,
    MAX(max_acquisition_time_ms) AS max_acquisition_time_ms,
    AVG(avg_usage_time_ms) AS avg_usage_time_ms,
    MAX(max_usage_time_ms) AS max_usage_time_ms,
    -- Aggregate script usage stats (merge all JSON objects for the day)
    jsonb_object_agg(
        COALESCE(script_usage_stats->>'script_name', 'unknown'), 
        script_usage_stats
    ) FILTER (WHERE script_usage_stats IS NOT NULL) AS daily_script_usage_stats
FROM api.connection_pool_monitoring
GROUP BY DATE_TRUNC('day', timestamp), pool_type
ORDER BY day DESC, pool_type;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'api' 
  AND table_name = 'connection_pool_monitoring' 
ORDER BY ordinal_position;