"""Integration tests for the async/sync bridge with actual scripts."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from magic_gateway.scripts.async_bridge import (
    execute_clickhouse_command,
    execute_clickhouse_query,
    AsyncBridge,
)


class TestAsyncBridgeIntegration:
    """Integration tests for async bridge functionality."""

    @patch("magic_gateway.scripts.async_bridge.run_async_in_sync")
    def test_clickhouse_operations_integration(self, mock_run_async):
        """Test ClickHouse operations work end-to-end."""
        # Mock connection manager
        mock_manager = MagicMock()
        mock_run_async.return_value = "success"

        # Test command execution
        result = execute_clickhouse_command(
            mock_manager, "CREATE TABLE test (id Int32) ENGINE = Memory"
        )
        assert result == "success"

        # Test query execution
        mock_run_async.return_value = MagicMock()  # Mock DataFrame
        result = execute_clickhouse_query(mock_manager, "SELECT * FROM test")
        assert result is not None

        # Verify async functions were called
        assert mock_run_async.call_count == 2

    def test_async_bridge_context_integration(self):
        """Test AsyncBridge context manager integration."""
        # Mock connection managers
        connection_managers = {
            "clickhouse_primary": MagicMock(),
            "postgres": MagicMock(),
        }

        with (
            patch(
                "magic_gateway.scripts.async_bridge.execute_clickhouse_command"
            ) as mock_ch_cmd,
            patch(
                "magic_gateway.scripts.async_bridge.execute_clickhouse_query"
            ) as mock_ch_query,
            patch(
                "magic_gateway.scripts.async_bridge.execute_postgres_query"
            ) as mock_pg_query,
        ):
            mock_ch_cmd.return_value = "command_success"
            mock_ch_query.return_value = MagicMock()  # Mock DataFrame
            mock_pg_query.return_value = [("result",)]

            with AsyncBridge(connection_managers) as bridge:
                # Test ClickHouse operations
                cmd_result = bridge.clickhouse_command("CREATE TABLE test")
                query_result = bridge.clickhouse_query("SELECT * FROM test")

                # Test PostgreSQL operations
                pg_result = bridge.postgres_query("SELECT 'test'")

                assert cmd_result == "command_success"
                assert query_result is not None
                assert pg_result == [("result",)]

                # Verify correct managers were used
                mock_ch_cmd.assert_called_with(
                    connection_managers["clickhouse_primary"], "CREATE TABLE test", None
                )
                mock_ch_query.assert_called_with(
                    connection_managers["clickhouse_primary"],
                    "SELECT * FROM test",
                    None,
                )
                mock_pg_query.assert_called_with(
                    connection_managers["postgres"], "SELECT 'test'", None
                )

    def test_error_propagation_integration(self):
        """Test that errors are properly propagated through the bridge."""
        mock_manager = MagicMock()

        with patch(
            "magic_gateway.scripts.async_bridge.run_async_in_sync"
        ) as mock_run_async:
            # Simulate an error in the async operation
            from magic_gateway.core.exceptions import ScriptExecutionException

            mock_run_async.side_effect = ScriptExecutionException("Test error")

            with pytest.raises(ScriptExecutionException) as exc_info:
                execute_clickhouse_command(mock_manager, "INVALID SQL")

            assert "Test error" in str(exc_info.value)

    @patch("magic_gateway.scripts.async_bridge.run_async_in_sync")
    def test_parameter_handling_integration(self, mock_run_async):
        """Test that parameters are correctly handled through the bridge."""
        mock_manager = MagicMock()
        mock_run_async.return_value = "success"

        # Test with parameters
        parameters = {"param1": "value1", "param2": 123}
        result = execute_clickhouse_command(
            mock_manager,
            "INSERT INTO test VALUES ({param1:String}, {param2:Int32})",
            parameters,
        )

        assert result == "success"
        mock_run_async.assert_called_once()

        # Verify the async function would be called with parameters
        # (We can't directly test the async function call, but we can verify
        # that run_async_in_sync was called, which means the parameters
        # were passed through correctly)

    def test_multiple_operations_integration(self):
        """Test multiple operations in sequence."""
        mock_manager = MagicMock()

        with patch(
            "magic_gateway.scripts.async_bridge.run_async_in_sync"
        ) as mock_run_async:
            # Set up different return values for different calls
            mock_run_async.side_effect = [
                "table_created",
                "data_inserted",
                MagicMock(),  # Mock DataFrame for query
                "table_dropped",
            ]

            # Simulate a sequence of operations like in a real script
            create_result = execute_clickhouse_command(
                mock_manager, "CREATE TABLE test (id Int32) ENGINE = Memory"
            )

            insert_result = execute_clickhouse_command(
                mock_manager, "INSERT INTO test VALUES (1)"
            )

            query_result = execute_clickhouse_query(mock_manager, "SELECT * FROM test")

            drop_result = execute_clickhouse_command(mock_manager, "DROP TABLE test")

            # Verify all operations completed
            assert create_result == "table_created"
            assert insert_result == "data_inserted"
            assert query_result is not None
            assert drop_result == "table_dropped"

            # Verify all async operations were called
            assert mock_run_async.call_count == 4
