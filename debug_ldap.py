#!/usr/bin/env python3
"""
Debug script to test LDAP authentication configuration.
This script helps identify LDAP authentication issues.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from magic_gateway.core.config import settings
from magic_gateway.auth.ldap_auth import LDAPA<PERSON>


def print_ldap_config():
    """Print current LDAP configuration (without sensitive data)."""
    print("=== LDAP Configuration ===")
    print(f"LDAP_SERVER: {settings.LDAP_SERVER}")
    print(f"LDAP_DOMAIN: {settings.LDAP_DOMAIN}")
    print(f"LDAP_DOMAIN_FQDN: {settings.LDAP_DOMAIN_FQDN}")
    print(f"LDAP_BASE_DN: {settings.LDAP_BASE_DN}")
    print(f"LDAP_SERVER_URL: {settings.LDAP_SERVER_URL}")
    print(f"LDAP_USER_SEARCH_BASE: {settings.LDAP_USER_SEARCH_BASE}")
    print(f"LDAP_USER_SEARCH_FILTER: {settings.LDAP_USER_SEARCH_FILTER}")
    print(f"LDAP_ADMIN_GROUP_DN: {settings.LDAP_ADMIN_GROUP_DN}")
    print(f"LDAP Available: {LDAPAuth.is_available()}")
    print()


def test_ldap_connection():
    """Test basic LDAP connection without authentication."""
    try:
        from ldap3 import Connection, Server, ALL

        server = Server(settings.LDAP_SERVER, get_info=ALL)
        conn = Connection(server)

        if conn.bind():
            print("✓ Anonymous LDAP connection successful")
            print(f"Server info: {server.info}")
            conn.unbind()
            return True
        else:
            print("✗ Anonymous LDAP connection failed")
            print(f"Error: {conn.result}")
            return False

    except Exception as e:
        print(f"✗ LDAP connection error: {e}")
        return False


async def test_authentication(username: str, password: str):
    """Test LDAP authentication with provided credentials."""
    print(f"=== Testing Authentication for: {username} ===")

    try:
        success, user_data = await LDAPAuth.authenticate(username, password)

        if success:
            print("✓ Authentication successful!")
            print(f"User data: {user_data}")
        else:
            print("✗ Authentication failed")

        return success

    except Exception as e:
        print(f"✗ Authentication error: {e}")
        return False


def main():
    """Main debug function."""
    print("LDAP Authentication Debug Tool")
    print("=" * 40)

    # Print configuration
    print_ldap_config()

    # Test basic connection
    print("=== Testing LDAP Connection ===")
    if not test_ldap_connection():
        print("Cannot proceed with authentication tests - basic connection failed")
        return

    # Test authentication with test credentials if available
    if settings.TEST_LDAP_USERNAME and settings.TEST_LDAP_PASSWORD:
        print(f"\n=== Testing with configured test credentials ===")
        import asyncio

        success = asyncio.run(
            test_authentication(
                settings.TEST_LDAP_USERNAME, settings.TEST_LDAP_PASSWORD
            )
        )

        if not success:
            print("\nTest authentication failed. Check credentials and configuration.")
    else:
        print(
            "\nNo test credentials configured (TEST_LDAP_USERNAME/TEST_LDAP_PASSWORD)"
        )
        print("You can test manually by setting these environment variables.")

    print("\n=== Troubleshooting Tips ===")
    print("1. Verify LDAP server is reachable")
    print("2. Check username format (try both 'username' and 'username@domain')")
    print("3. Verify password is correct")
    print("4. Check if account is locked/disabled/expired")
    print("5. Verify domain configuration")
    print("6. Check firewall/network connectivity")


if __name__ == "__main__":
    main()
