"""Tests for token refresh functionality."""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request, HTTPException

from magic_gateway.auth.dependencies import (
    get_current_user_with_refresh,
    get_current_active_user_with_refresh
)
from magic_gateway.auth.jwt import create_access_token, create_refresh_token
from magic_gateway.core.exceptions import NotAuthenticatedException


class TestTokenRefresh:
    """Test cases for token refresh functionality."""

    @pytest.fixture
    def mock_request(self):
        """Create a mock request object."""
        request = Mock(spec=Request)
        request.headers = {}
        request.state = Mock()
        return request

    @pytest.fixture
    def valid_token_data(self):
        """Create valid token data for testing."""
        return {
            "sub": "test_user",
            "is_admin": False,
            "auth_source": "ldap"
        }

    @pytest.fixture
    def valid_access_token(self, valid_token_data):
        """Create a valid access token."""
        return create_access_token(valid_token_data)

    @pytest.fixture
    def valid_refresh_token(self, valid_token_data):
        """Create a valid refresh token."""
        return create_refresh_token(valid_token_data)

    @pytest.fixture
    def expired_access_token(self, valid_token_data):
        """Create an expired access token."""
        return create_access_token(
            valid_token_data,
            expires_delta=timedelta(seconds=-1)  # Already expired
        )

    @pytest.mark.asyncio
    async def test_valid_token_no_refresh_needed(self, mock_request, valid_access_token):
        """Test that valid tokens don't trigger refresh."""
        user_data = await get_current_user_with_refresh(mock_request, valid_access_token)
        
        assert user_data["username"] == "test_user"
        assert user_data["is_admin"] is False
        assert user_data["auth_source"] == "ldap"
        
        # No new tokens should be set
        assert not hasattr(mock_request.state, 'new_access_token')
        assert not hasattr(mock_request.state, 'new_refresh_token')

    @pytest.mark.asyncio
    async def test_expired_token_with_valid_refresh(
        self, mock_request, expired_access_token, valid_refresh_token
    ):
        """Test that expired tokens are refreshed when valid refresh token is provided."""
        mock_request.headers = {"X-Refresh-Token": valid_refresh_token}
        
        user_data = await get_current_user_with_refresh(mock_request, expired_access_token)
        
        assert user_data["username"] == "test_user"
        assert user_data["is_admin"] is False
        assert user_data["auth_source"] == "ldap"
        
        # New tokens should be set in request state
        assert hasattr(mock_request.state, 'new_access_token')
        assert hasattr(mock_request.state, 'new_refresh_token')
        assert mock_request.state.new_access_token is not None
        assert mock_request.state.new_refresh_token is not None

    @pytest.mark.asyncio
    async def test_expired_token_without_refresh_token(
        self, mock_request, expired_access_token
    ):
        """Test that expired tokens without refresh token raise authentication error."""
        with pytest.raises(NotAuthenticatedException):
            await get_current_user_with_refresh(mock_request, expired_access_token)

    @pytest.mark.asyncio
    async def test_expired_token_with_invalid_refresh(
        self, mock_request, expired_access_token
    ):
        """Test that expired tokens with invalid refresh token raise authentication error."""
        mock_request.headers = {"X-Refresh-Token": "invalid_refresh_token"}
        
        with pytest.raises(NotAuthenticatedException):
            await get_current_user_with_refresh(mock_request, expired_access_token)

    @pytest.mark.asyncio
    async def test_proactive_refresh_near_expiry(self, mock_request, valid_token_data, valid_refresh_token):
        """Test that tokens are proactively refreshed when near expiry."""
        # Create a token that expires in 2 minutes (less than 5 minute threshold)
        near_expiry_token = create_access_token(
            valid_token_data,
            expires_delta=timedelta(minutes=2)
        )
        
        mock_request.headers = {"X-Refresh-Token": valid_refresh_token}
        
        user_data = await get_current_user_with_refresh(mock_request, near_expiry_token)
        
        assert user_data["username"] == "test_user"
        
        # New tokens should be set due to proactive refresh
        assert hasattr(mock_request.state, 'new_access_token')
        assert hasattr(mock_request.state, 'new_refresh_token')

    @pytest.mark.asyncio
    async def test_no_proactive_refresh_when_not_near_expiry(
        self, mock_request, valid_token_data, valid_refresh_token
    ):
        """Test that tokens are not proactively refreshed when not near expiry."""
        # Create a token that expires in 10 minutes (more than 5 minute threshold)
        long_expiry_token = create_access_token(
            valid_token_data,
            expires_delta=timedelta(minutes=10)
        )
        
        mock_request.headers = {"X-Refresh-Token": valid_refresh_token}
        
        user_data = await get_current_user_with_refresh(mock_request, long_expiry_token)
        
        assert user_data["username"] == "test_user"
        
        # No new tokens should be set
        assert not hasattr(mock_request.state, 'new_access_token')
        assert not hasattr(mock_request.state, 'new_refresh_token')

    @pytest.mark.asyncio
    async def test_get_current_active_user_with_refresh(self, mock_request, valid_access_token):
        """Test the active user dependency with refresh functionality."""
        with patch('magic_gateway.auth.dependencies.get_current_user_with_refresh') as mock_get_user:
            mock_get_user.return_value = {
                "username": "test_user",
                "is_admin": False,
                "auth_source": "ldap"
            }
            
            user_data = await get_current_active_user_with_refresh(mock_request, mock_get_user.return_value)
            
            assert user_data["username"] == "test_user"
            assert user_data["is_admin"] is False
            assert user_data["auth_source"] == "ldap"

    @pytest.mark.asyncio
    async def test_refresh_token_rotation(self, mock_request, expired_access_token, valid_refresh_token):
        """Test that both access and refresh tokens are rotated on refresh."""
        mock_request.headers = {"X-Refresh-Token": valid_refresh_token}
        
        await get_current_user_with_refresh(mock_request, expired_access_token)
        
        # Verify new tokens are different from original
        assert mock_request.state.new_access_token != expired_access_token
        assert mock_request.state.new_refresh_token != valid_refresh_token
        
        # Verify new tokens are valid
        from magic_gateway.auth.jwt import verify_access_token, decode_token
        
        new_access_payload = verify_access_token(mock_request.state.new_access_token)
        assert new_access_payload["sub"] == "test_user"
        
        new_refresh_payload = decode_token(mock_request.state.new_refresh_token)
        assert new_refresh_payload["type"] == "refresh"
        assert new_refresh_payload["sub"] == "test_user"