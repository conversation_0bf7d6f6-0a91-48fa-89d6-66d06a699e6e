"""Database connection managers for the MagicGateway application."""

import asyncio
import time
from abc import ABC, abstractmethod
from collections import deque
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from typing import Any, AsyncGenerator, Deque, Generic, List, Optional, TypeVar, Dict

from clickhouse_driver import Client as ClickHouseClient
from clickhouse_driver.errors import Error as ClickHouseDriverError
from clickhouse_driver.errors import NetworkError as ClickHouseNetworkError
from clickhouse_driver.errors import ServerException as ClickHouseServerException
from psycopg import AsyncConnection
from psycopg.errors import InterfaceError as PsycopgInterfaceError
from psycopg.errors import OperationalError as PsycopgOperationalError
from psycopg_pool import AsyncConnectionPool, PoolTimeout

from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import (
    ClickHouseException,
    PostgresException,
    ConnectionPoolException,
    ConnectionPoolExhaustedException,
    ConnectionTimeoutException,
    TransientConnectionException,
)

# Assuming logger setup remains the same
from magic_gateway.core.logging_config import log

# Import monitoring service (will be imported later to avoid circular imports)
# Define global variable for monitoring service
monitoring_service = None

# Type variable for connection objects (e.g., AsyncConnection, ClickHouseClient)
ConnType = TypeVar("ConnType")


class BaseConnectionManager(ABC, Generic[ConnType]):
    """Base class for database connection managers."""

    def __init__(self):
        """Initialize the connection manager."""
        self._pool: Optional[Any] = None  # Specific pool type in subclasses
        self._initialized = False
        self._initializing_lock = asyncio.Lock()
        self._startup_complete = False

    def mark_startup_complete(self):
        """Mark that application startup is complete to enable debug logging."""
        self._startup_complete = True

    @abstractmethod
    async def initialize(self, max_retries: int = 3, retry_delay: int = 5) -> None:
        """Initialize the connection pool with retry logic."""
        pass

    @abstractmethod
    async def close(self) -> None:
        """Close the connection pool."""
        pass

    @abstractmethod
    @asynccontextmanager
    async def connection(self) -> AsyncGenerator[ConnType, None]:
        """Provide a connection context manager."""
        # Yield type needs to be defined correctly in subclasses
        # This is a placeholder; actual implementation is in subclasses
        if False:  # pragma: no cover
            yield None  # type: ignore

    @property
    def initialized(self) -> bool:
        """Check if the connection pool is initialized."""
        return self._initialized

    async def _ensure_initialized(self) -> None:
        """Ensure the pool is initialized, attempting initialization if necessary."""
        if not self._initialized:
            async with self._initializing_lock:
                # Double-check after acquiring the lock
                if not self._initialized:
                    log.warning(
                        f"{self.__class__.__name__} accessed before explicit initialization. Attempting lazy initialization."
                    )
                    await self.initialize()
                    if not self._initialized:
                        raise RuntimeError(  # Use RuntimeError instead of custom exception? Or define a base InitError
                            f"{self.__class__.__name__} failed to initialize lazily."
                        )


# ==============================================================
# PostgreSQL Connection Manager
# ==============================================================


class PostgresConnectionManager(BaseConnectionManager[AsyncConnection]):
    """
    Connection manager for PostgreSQL using psycopg_pool.
    Provides an async context manager for acquiring connections.
    """

    _pool: Optional[AsyncConnectionPool]  # Type hint refinement

    async def initialize(self, max_retries: int = 3, retry_delay: int = 5) -> None:
        """Initialize the PostgreSQL connection pool with retry logic."""
        if self._initialized:
            log.debug("PostgreSQL pool already initialized.")
            return

        async with self._initializing_lock:
            # Double-check after acquiring lock
            if self._initialized:
                log.debug("PostgreSQL pool already initialized (double check).")
                return

            log.info("Initializing PostgreSQL connection pool...")
            conn_info = f"PostgreSQL: {settings.POSTGRES_USER}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
            log.debug(f"Attempting to connect to {conn_info}")

            retry_count = 0
            last_error = None
            while retry_count <= max_retries:
                try:
                    # Create the pool without opening it initially
                    pool = AsyncConnectionPool(
                        conninfo=settings.POSTGRES_DSN,
                        min_size=settings.POSTGRES_MIN_CONNECTIONS,
                        max_size=settings.POSTGRES_MAX_CONNECTIONS,
                        timeout=settings.POSTGRES_POOL_TIMEOUT,  # Use setting
                        max_idle=settings.POSTGRES_MAX_IDLE_TIME,  # Use setting
                        max_lifetime=settings.POSTGRES_MAX_LIFETIME,  # Use setting
                        open=False,  # Open manually for better error handling
                        # check=AsyncConnectionPool.check_connection, # Enable simple check
                        name=f"{settings.POSTGRES_APPLICATION_NAME}_pool",  # Use application name in pool name
                    )

                    # Open the pool explicitly and wait for min_size connections
                    # Use a timeout slightly longer than the pool's internal timeout
                    await asyncio.wait_for(
                        pool.open(wait=True), timeout=settings.POSTGRES_POOL_TIMEOUT + 5
                    )

                    # Test the connection with a simple query using the pool's context manager
                    async with pool.connection() as conn:
                        await conn.execute("SELECT 1")

                    self._pool = pool
                    self._initialized = True
                    log.info(
                        f"PostgreSQL connection pool initialized successfully ({conn_info}). "
                        f"Pool stats: {self._pool.get_stats()}"
                    )
                    return  # Success
                except (
                    PsycopgOperationalError,
                    PsycopgInterfaceError,
                    PoolTimeout,
                    TimeoutError,  # asyncio.wait_for timeout
                    Exception,  # Catch broad exceptions during init
                ) as e:
                    last_error = e
                    retry_count += 1
                    error_type = type(e).__name__
                    error_msg = str(e).split("\n")[0]  # Get first line of error

                    if self._pool:  # Cleanup partially opened pool on error
                        await self._pool.close()
                        self._pool = None

                    if retry_count <= max_retries:
                        log.warning(
                            f"PostgreSQL connection attempt {retry_count}/{max_retries} failed: {error_type}: {error_msg}. "
                            f"Retrying in {retry_delay} seconds..."
                        )
                        await asyncio.sleep(retry_delay)
                    else:
                        log.error(
                            f"Failed to initialize PostgreSQL connection pool after {max_retries + 1} attempts. "
                            f"Last error: {error_type}: {error_msg}",
                            exc_info=True
                            if settings.LOG_LEVEL.upper() == "DEBUG"
                            else False,
                        )
                        log.error(f"Connection details used: {conn_info}")
                        # Log specific advice based on common errors
                        if isinstance(e, PoolTimeout):
                            log.error(
                                "Pool timeout occurred. Check max_size, network latency, or DB responsiveness."
                            )
                        elif "connection refused" in error_msg.lower():
                            log.error(
                                "Connection refused. Ensure PostgreSQL server is running and accessible."
                            )
                        elif "authentication failed" in error_msg.lower():
                            log.error(
                                "Authentication failed. Check PostgreSQL credentials (user/password/host)."
                            )
                        elif (
                            "database" in error_msg.lower()
                            and "does not exist" in error_msg.lower()
                        ):
                            log.error(
                                f"Database '{settings.POSTGRES_DB}' does not exist."
                            )

                        # Keep manager uninitialized
                        self._initialized = False
                        self._pool = None
                        # Re-raise a specific exception to signal failure
                        raise PostgresException(
                            f"Failed to initialize PostgreSQL pool after {max_retries + 1} attempts: {error_type}: {error_msg}"
                        ) from last_error

    async def close(self) -> None:
        """Close the PostgreSQL connection pool gracefully."""
        if not self._initialized or not self._pool:
            log.debug("PostgreSQL pool already closed or not initialized.")
            return

        log.info("Closing PostgreSQL connection pool...")
        try:
            pool_stats = self._pool.get_stats()
            log.info(f"PostgreSQL pool stats before closing: {pool_stats}")
            await self._pool.close()
            log.info("PostgreSQL connection pool closed successfully.")
        except Exception as e:
            log.error(f"Error closing PostgreSQL connection pool: {e}", exc_info=True)
            # Avoid raising an exception during shutdown if possible
        finally:
            self._initialized = False
            self._pool = None

    @asynccontextmanager
    async def connection(self) -> AsyncGenerator[AsyncConnection, None]:
        """
        Provide an async context manager to get a connection from the pool.

        Usage:
            async with postgres_connection_manager.connection() as conn:
                await conn.execute(...)
        """
        await self._ensure_initialized()
        if (
            not self._pool
        ):  # Should be caught by _ensure_initialized, but defensive check
            raise PostgresException("PostgreSQL connection pool is not available.")

        conn: Optional[AsyncConnection] = None
        acquisition_start = time.time()
        acquisition_time_ms = 0
        usage_start = 0

        try:
            # Use the pool's context manager
            async with self._pool.connection() as acquired_conn:
                # Record acquisition time
                acquisition_time_ms = (time.time() - acquisition_start) * 1000
                monitoring_svc = _get_monitoring_service()
                if monitoring_svc:
                    # Try to get script context for script-specific tracking
                    try:
                        from magic_gateway.monitoring.script_context import (
                            get_current_script_name,
                        )

                        script_name = get_current_script_name()
                    except ImportError:
                        script_name = None
                    monitoring_svc.record_acquisition_time(
                        "postgres", acquisition_time_ms, script_name
                    )

                conn = acquired_conn  # Assign for finally block if needed
                # Optional: Log successful acquisition with timing (skip during startup)
                if hasattr(self, '_startup_complete') and self._startup_complete:
                    log.debug(
                        f"Acquired PostgreSQL connection in {acquisition_time_ms:.2f}ms. Pool stats: {self._pool.get_stats()}"
                    )

                # Record usage start time
                usage_start = time.time()

                yield conn

                # Record usage time after yield returns
                monitoring_svc = _get_monitoring_service()
                if usage_start > 0 and monitoring_svc:
                    usage_time_ms = (time.time() - usage_start) * 1000
                    # Try to get script context for script-specific tracking
                    try:
                        from magic_gateway.monitoring.script_context import (
                            get_current_script_name,
                        )

                        script_name = get_current_script_name()
                    except ImportError:
                        script_name = None
                    monitoring_svc.record_usage_time(
                        "postgres", usage_time_ms, script_name
                    )
                    # Log usage time (skip during startup)
                    if hasattr(self, '_startup_complete') and self._startup_complete:
                        log.debug(f"PostgreSQL connection used for {usage_time_ms:.2f}ms")
        except PoolTimeout as e:
            # Record timeout in monitoring
            monitoring_svc = _get_monitoring_service()
            if (
                monitoring_svc
                and hasattr(monitoring_svc, "_last_metrics")
                and "postgres" in monitoring_svc._last_metrics
            ):
                metrics = monitoring_svc._last_metrics["postgres"]
                metrics.connection_timeouts += 1

            pool_stats = self._pool.get_stats()
            log.error(
                f"PostgreSQL connection pool exhausted. Timeout acquiring connection after {settings.POSTGRES_POOL_TIMEOUT}s. "
                f"Pool stats: {pool_stats}. "
                f"Consider increasing pool size or reducing connection hold time."
            )
            raise ConnectionPoolExhaustedException(
                f"PostgreSQL connection pool exhausted. All connections are in use. "
                f"Pool stats: {pool_stats}. Waited {settings.POSTGRES_POOL_TIMEOUT}s for an available connection."
            ) from e
        except (PsycopgOperationalError, PsycopgInterfaceError) as e:
            # Record error in monitoring
            monitoring_svc = _get_monitoring_service()
            if (
                monitoring_svc
                and hasattr(monitoring_svc, "_last_metrics")
                and "postgres" in monitoring_svc._last_metrics
            ):
                metrics = monitoring_svc._last_metrics["postgres"]
                metrics.connection_errors += 1

            # Classify the error type for better handling
            error_msg = str(e).lower()
            if (
                "connection refused" in error_msg
                or "timeout" in error_msg
                or "network" in error_msg
            ):
                log.error(
                    f"PostgreSQL network/timeout error during acquisition or use: {e}",
                    exc_info=True,
                )
                raise TransientConnectionException(
                    f"PostgreSQL network/timeout error: {e}"
                ) from e
            elif "too many connections" in error_msg or "connection limit" in error_msg:
                log.error(
                    f"PostgreSQL server connection limit reached: {e}",
                    exc_info=True,
                )
                raise ConnectionPoolExhaustedException(
                    f"PostgreSQL server connection limit reached: {e}"
                ) from e
            else:
                log.error(
                    f"PostgreSQL connection error during acquisition or use: {e}",
                    exc_info=True,
                )
                raise PostgresException(f"PostgreSQL connection error: {e}") from e
        except Exception as e:
            log.error(
                f"Unexpected error with PostgreSQL connection: {e}", exc_info=True
            )
            raise PostgresException(
                f"Unexpected error with PostgreSQL connection: {e}"
            ) from e
        # Connection is automatically released by the pool's context manager `async with`


# ==============================================================
# ClickHouse Connection Manager (Custom Pool)
# ==============================================================


@dataclass
class _ClickHousePoolState:
    """Internal state for the ClickHouse connection pool."""

    idle: Deque[ClickHouseClient] = field(default_factory=deque)
    active_connections: int = 0
    lock: asyncio.Lock = field(default_factory=asyncio.Lock)
    semaphore: asyncio.Semaphore = field(default_factory=lambda: asyncio.Semaphore(10))
    min_size: int = 3
    max_size: int = 10
    connect_timeout: int = 10
    ping_timeout: int = 5  # Timeout for liveness check


class ClickHouseConnectionManager(BaseConnectionManager[ClickHouseClient]):
    """
    Connection manager for ClickHouse using a custom async pool.
    Provides an async context manager for acquiring connections.
    Manages connection lifecycle and limits concurrency.

    Supports multiple clusters with independent connection pools.
    """

    _pool: Optional[_ClickHousePoolState]  # Type hint refinement

    def __init__(
        self,
        cluster_name: Optional[str] = None,
        config_settings: Optional[Any] = None,
    ):
        """
        Initialize the ClickHouse connection manager.

        Args:
            cluster_name: Optional cluster name for identification and logging.
                          Use "primary" for the default connection or a specific
                          cluster name for cluster connections.
            config_settings: Optional settings object to use instead of global settings
        """
        super().__init__()
        self.cluster_name = cluster_name or "primary"
        self.settings = config_settings or settings

        # Determine which configuration to use based on cluster name
        if self.cluster_name == "primary":
            # Use primary ClickHouse configuration
            self.host = self.settings.CLICKHOUSE_HOST
            self.port = self.settings.CLICKHOUSE_PORT
            self.user = self.settings.CLICKHOUSE_USER
            self.password = self.settings.CLICKHOUSE_PASSWORD
            self.database = self.settings.CLICKHOUSE_DATABASE
            self.min_connections = self.settings.CLICKHOUSE_MIN_CONNECTIONS
            self.max_connections = self.settings.CLICKHOUSE_MAX_CONNECTIONS
            self.connect_timeout = self.settings.CLICKHOUSE_CONNECT_TIMEOUT
            self.ping_timeout = self.settings.CLICKHOUSE_PING_TIMEOUT
            self.socket_timeout = self.settings.CLICKHOUSE_SOCKET_TIMEOUT
            self.pool_wait_timeout = self.settings.CLICKHOUSE_POOL_WAIT_TIMEOUT
            self.http_port = self.settings.CLICKHOUSE_HTTP_PORT
        else:
            # Use cluster-specific configuration
            self.host = self.settings.CLICKHOUSE_CLUSTER_HOST
            self.port = self.settings.CLICKHOUSE_CLUSTER_PORT
            self.user = self.settings.CLICKHOUSE_CLUSTER_USER
            self.password = self.settings.CLICKHOUSE_CLUSTER_PASSWORD
            self.database = self.settings.CLICKHOUSE_CLUSTER_DATABASE
            self.min_connections = self.settings.CLICKHOUSE_CLUSTER_MIN_CONNECTIONS
            self.max_connections = self.settings.CLICKHOUSE_CLUSTER_MAX_CONNECTIONS
            self.connect_timeout = self.settings.CLICKHOUSE_CLUSTER_CONNECT_TIMEOUT
            self.ping_timeout = self.settings.CLICKHOUSE_CLUSTER_PING_TIMEOUT
            self.socket_timeout = self.settings.CLICKHOUSE_CLUSTER_SOCKET_TIMEOUT
            self.pool_wait_timeout = self.settings.CLICKHOUSE_CLUSTER_POOL_WAIT_TIMEOUT
            self.http_port = (
                self.settings.CLICKHOUSE_CLUSTER_HTTP_PORT
                or self.settings.CLICKHOUSE_HTTP_PORT
            )

        # Store connection info for logging and monitoring
        self.connection_info = f"ClickHouse ({self.cluster_name}): {self.user}@{self.host}:{self.port}/{self.database}"

        # Validate cluster configuration
        if self.cluster_name != "primary":
            self._validate_cluster_configuration()

    def _validate_cluster_configuration(self) -> None:
        """
        Validate that cluster-specific configuration is available and complete.

        This method ensures all required configuration parameters for a cluster
        are properly set before attempting to establish connections.

        Raises:
            ClickHouseException: If cluster configuration is incomplete or invalid
        """
        required_fields = {
            "CLICKHOUSE_CLUSTER_HOST": self.host,
            "CLICKHOUSE_CLUSTER_PORT": self.port,
            "CLICKHOUSE_CLUSTER_USER": self.user,
            "CLICKHOUSE_CLUSTER_PASSWORD": self.password,
            "CLICKHOUSE_CLUSTER_DATABASE": self.database,
        }

        missing_fields = [
            field for field, value in required_fields.items() if not value
        ]

        if missing_fields:
            error_msg = (
                f"ClickHouse cluster '{self.cluster_name}' configuration is incomplete. "
                f"Missing required fields: {', '.join(missing_fields)}"
            )
            log.error(error_msg)
            raise ClickHouseException(error_msg)

        # Validate port is a valid integer
        if not isinstance(self.port, int) or self.port <= 0 or self.port > 65535:
            error_msg = f"Invalid port number for ClickHouse cluster '{self.cluster_name}': {self.port}"
            log.error(error_msg)
            raise ClickHouseException(error_msg)

        # Validate connection limits
        if self.min_connections <= 0:
            log.warning(
                f"Invalid min_connections for ClickHouse cluster '{self.cluster_name}': {self.min_connections}. "
                f"Using default value of 3."
            )
            self.min_connections = 3

        if self.max_connections <= 0:
            log.warning(
                f"Invalid max_connections for ClickHouse cluster '{self.cluster_name}': {self.max_connections}. "
                f"Using default value of 10."
            )
            self.max_connections = 10

        if self.min_connections > self.max_connections:
            log.warning(
                f"min_connections ({self.min_connections}) is greater than max_connections ({self.max_connections}) "
                f"for ClickHouse cluster '{self.cluster_name}'. Setting min_connections = max_connections."
            )
            self.min_connections = self.max_connections

        log.info(
            f"ClickHouse cluster '{self.cluster_name}' configuration validated successfully. "
            f"Target: {self.user}@{self.host}:{self.port}/{self.database}"
        )

    async def initialize(self, max_retries: int = 3, retry_delay: int = 5) -> None:
        """
        Initialize the ClickHouse connection pool with retry logic.

        This method creates a connection pool for the specified ClickHouse cluster
        with the configured number of minimum connections. It includes retry logic
        to handle temporary connection issues during startup.

        Args:
            max_retries: Maximum number of retry attempts if initialization fails
            retry_delay: Delay in seconds between retry attempts

        Raises:
            ClickHouseException: If initialization fails after all retry attempts
        """
        if self._initialized:
            log.debug(f"ClickHouse pool '{self.cluster_name}' already initialized.")
            return

        async with self._initializing_lock:
            # Double-check after acquiring lock
            if self._initialized:
                log.debug(
                    f"ClickHouse pool '{self.cluster_name}' already initialized (double check)."
                )
                return

            log.info(
                f"Initializing ClickHouse connection pool for cluster '{self.cluster_name}'..."
            )
            conn_info = f"ClickHouse ({self.cluster_name}): {self.user}@{self.host}:{self.port}/{self.database}"
            log.debug(f"Attempting to connect to {conn_info}")

            # Initialize internal state with cluster-specific configuration
            pool_state = _ClickHousePoolState()
            pool_state.min_size = self.min_connections
            pool_state.max_size = self.max_connections
            pool_state.connect_timeout = self.connect_timeout
            pool_state.ping_timeout = self.ping_timeout
            pool_state.semaphore = asyncio.Semaphore(self.max_connections)
            self._pool = pool_state  # Assign early for potential cleanup

            retry_count = 0
            last_error = None
            initial_connections_created = 0
            while retry_count <= max_retries:
                try:
                    # Create initial connections up to min_size
                    # Use semaphore to respect max_size even during initialization
                    connections_to_create = pool_state.min_size - len(pool_state.idle)
                    log.debug(
                        f"Attempting to create {connections_to_create} initial ClickHouse connections..."
                    )

                    creation_tasks = []
                    for _ in range(connections_to_create):
                        # Don't block initialization forever if semaphore is busy
                        try:
                            await asyncio.wait_for(
                                pool_state.semaphore.acquire(),
                                timeout=pool_state.connect_timeout + 1,
                            )
                        except TimeoutError:
                            log.warning(
                                "Timeout waiting for semaphore during initial connection creation."
                            )
                            raise ClickHouseException(
                                "Timeout waiting for semaphore during initialization"
                            )

                        # Create connection in thread pool as driver is sync
                        creation_tasks.append(
                            asyncio.get_event_loop().run_in_executor(
                                None, self._create_and_test_connection
                            )
                        )

                    # Gather results of connection creation
                    results = await asyncio.gather(
                        *creation_tasks, return_exceptions=True
                    )

                    # Process results
                    created_this_attempt = 0
                    for result in results:
                        if isinstance(result, ClickHouseClient):
                            pool_state.idle.append(result)
                            # Don't increment active_connections here - connections in idle pool are not active
                            created_this_attempt += 1
                        elif isinstance(result, Exception):
                            # Release semaphore for failed creations
                            pool_state.semaphore.release()
                            log.warning(
                                f"Failed to create an initial ClickHouse connection: {result}"
                            )
                            # Store the first error encountered for reporting
                            if last_error is None:
                                last_error = result
                        else:  # Should not happen
                            pool_state.semaphore.release()
                            log.error(
                                f"Unexpected result type during CH connection creation: {type(result)}"
                            )

                    initial_connections_created += created_this_attempt
                    log.debug(
                        f"Created {created_this_attempt} connections this attempt. Total initial: {initial_connections_created}/{pool_state.min_size}"
                    )

                    # Check if we met the minimum requirement or if an error forces retry/failure
                    if initial_connections_created >= pool_state.min_size:
                        self._initialized = True
                        log.info(
                            f"ClickHouse connection pool for cluster '{self.cluster_name}' initialized successfully ({conn_info}). "
                            f"Pool size: {len(pool_state.idle)} idle, {pool_state.active_connections} active (target min: {pool_state.min_size})"
                        )
                        return  # Success
                    elif last_error:
                        # If we failed to create some connections, trigger retry logic
                        raise last_error  # Raise the captured error

                except (ClickHouseDriverError, ClickHouseException, Exception) as e:
                    # Catch errors from _create_and_test_connection or other issues
                    if not isinstance(e, (ClickHouseDriverError, ClickHouseException)):
                        log.error(
                            f"Unexpected error during ClickHouse pool initialization: {type(e).__name__}: {e}",
                            exc_info=True,
                        )

                    last_error = e
                    retry_count += 1
                    error_type = type(e).__name__
                    error_msg = str(e).split("\n")[0]  # First line

                    # Partial cleanup might be needed if some connections were created but min_size not reached
                    # For simplicity here, we rely on the close() method for full cleanup if init fails finally

                    if retry_count <= max_retries:
                        log.warning(
                            f"ClickHouse connection attempt {retry_count}/{max_retries} for cluster '{self.cluster_name}' failed: {error_type}: {error_msg}. "
                            f"Retrying in {retry_delay} seconds..."
                        )
                        # Release any semaphores potentially held by failed tasks (best effort)
                        # This is tricky without tracking tasks precisely; rely on gather cleanup
                        await asyncio.sleep(retry_delay)
                    else:
                        log.error(
                            f"Failed to initialize ClickHouse connection pool for cluster '{self.cluster_name}' after {max_retries + 1} attempts. "
                            f"Created {initial_connections_created}/{pool_state.min_size} connections. "
                            f"Last error: {error_type}: {error_msg}",
                            exc_info=True
                            if self.settings.LOG_LEVEL.upper() == "DEBUG"
                            else False,
                        )
                        log.error(f"Connection details used: {conn_info}")
                        # Log specific advice
                        if "connection refused" in error_msg.lower():
                            log.error(
                                "Connection refused. Ensure ClickHouse server is running and accessible."
                            )
                        elif (
                            "authentication failed" in error_msg.lower()
                            or "code: 516" in error_msg
                        ):  # Code 516 is auth failed
                            log.error(
                                "Authentication failed. Check ClickHouse credentials."
                            )
                        elif (
                            "database" in error_msg.lower()
                            and "not exist" in error_msg.lower()
                        ):
                            log.error(f"Database '{self.database}' does not exist.")
                        elif (
                            isinstance(e, TimeoutError)
                            or "timeout" in error_msg.lower()
                        ):
                            log.error(
                                "Connection timeout occurred. Check network or ClickHouse server responsiveness."
                            )

                        # Ensure pool is cleaned up on final failure
                        await (
                            self.close()
                        )  # Close potentially partially created connections
                        self._initialized = False
                        # Re-raise a specific exception
                        raise ClickHouseException(
                            f"Failed to initialize ClickHouse pool after {max_retries + 1} attempts: {error_type}: {error_msg}"
                        ) from last_error

    def _create_and_test_connection(self) -> ClickHouseClient:
        """Creates and performs a basic test on a new ClickHouse connection."""
        # This runs in a thread pool executor
        try:
            client = ClickHouseClient(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=self.connect_timeout,
                send_receive_timeout=self.socket_timeout,
                # Consider adding secure=True if using TLS
            )
            # Basic liveness check immediately after connect
            client.execute(
                "SELECT 1",
                settings={"max_execution_time": self.ping_timeout},
            )
            log.debug(
                f"Successfully created and tested new ClickHouse connection for cluster '{self.cluster_name}'."
            )
            return client
        except Exception as e:
            log.warning(
                f"Failed to create or test ClickHouse connection for cluster '{self.cluster_name}': {e}"
            )
            # Don't disconnect here, caller (gather) handles semaphore release on exception
            raise  # Re-raise the exception to be caught by asyncio.gather

    async def close(self) -> None:
        """
        Close all connections in the ClickHouse pool.

        This method gracefully closes all idle connections in the pool and
        prevents new connections from being created. Active connections will
        be closed when they are returned to the pool.
        """
        if not self._pool:  # Check internal state obj
            log.debug(
                f"ClickHouse pool for cluster '{self.cluster_name}' already closed or not initialized."
            )
            return

        log.info(
            f"Closing ClickHouse connection pool for cluster '{self.cluster_name}'..."
        )
        async with self._pool.lock:  # Prevent acquiring connections during close
            # Set initialized to false early to prevent new acquisitions
            self._initialized = False
            state = self._pool

            active_count = state.active_connections
            idle_count = len(state.idle)
            log.info(
                f"ClickHouse pool stats for cluster '{self.cluster_name}' before closing - Idle: {idle_count}, Active: {active_count}"
            )

            # Close idle connections
            closed_count = 0
            while state.idle:
                client = state.idle.popleft()
                try:
                    # Run disconnect in executor
                    await asyncio.get_event_loop().run_in_executor(
                        None, client.disconnect
                    )
                    closed_count += 1
                except Exception as e:
                    log.warning(f"Error disconnecting ClickHouse client: {e}")

            log.info(f"Closed {closed_count} idle ClickHouse connections.")
            # Note: Active connections cannot be forcibly closed easily without cancellation.
            # They will be disconnected upon release if the pool is marked closed.
            # The semaphore prevents new connections beyond max_size.

            # Clear state
            state.idle.clear()
            state.active_connections = 0  # Reset counter
            # Release all semaphore permits potentially held (might over-release, but safe)
            # for _ in range(state.max_size):
            #    try: state.semaphore.release()
            #    except ValueError: break # Already fully released
            self._pool = None  # Release the state object

        log.info(
            f"ClickHouse connection pool for cluster '{self.cluster_name}' closed successfully."
        )

    async def _ping_connection(self, client: ClickHouseClient) -> bool:
        """
        Check if a ClickHouse connection is alive.

        This method performs a simple SELECT 1 query to verify the connection
        is still active and responsive.

        Args:
            client: The ClickHouse client to test

        Returns:
            bool: True if connection is alive, False otherwise
        """
        if not client or not client.connection or not client.connection.connected:
            return False
        try:
            # Run ping (SELECT 1) in thread pool with a short timeout
            # We need to create a wrapper function to pass settings to execute
            def execute_with_settings():
                return client.execute(
                    "SELECT 1", settings={"max_execution_time": self._pool.ping_timeout}
                )

            await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(None, execute_with_settings),
                timeout=self._pool.ping_timeout + 1,  # Add buffer to wait_for
            )
            return True
        except asyncio.TimeoutError:
            log.warning(
                f"ClickHouse connection ping for cluster '{self.cluster_name}' timed out after {self._pool.ping_timeout}s."
            )
            return False
        except ClickHouseDriverError as e:
            log.warning(f"ClickHouse connection ping failed: {e}")
            return False
        except Exception as e:
            log.error(
                f"Unexpected error during ClickHouse connection ping: {e}",
                exc_info=True,
            )
            return False

    @asynccontextmanager
    async def connection(self) -> AsyncGenerator[ClickHouseClient, None]:
        """
        Provide an async context manager to get a connection from the pool.

        This method manages the lifecycle of a ClickHouse connection, including:
        - Acquiring a connection from the pool or creating a new one
        - Testing the connection before use
        - Returning the connection to the pool after use
        - Handling errors and resource cleanup

        Each cluster has its own independent connection pool, ensuring that
        connections to different clusters don't interfere with each other.

        Usage:
            async with clickhouse_connection_manager.connection() as client:
                # Use client (e.g., with run_in_threadpool)
                result = await run_in_threadpool(client.execute, query)
        """
        await self._ensure_initialized()
        if not self._pool:
            raise ClickHouseException(
                f"ClickHouse connection pool for cluster '{self.cluster_name}' is not available."
            )

        state = self._pool
        client: Optional[ClickHouseClient] = None
        acquired_new = False  # Flag if we created a new connection vs reused one
        acquisition_start = time.time()
        acquisition_time_ms = 0
        usage_start = 0

        # Acquire semaphore - limits total concurrent connections (active + creating)
        # Use a timeout related to query execution time? Or a dedicated pool wait timeout?
        # Let's use a configurable pool wait timeout.
        try:
            await asyncio.wait_for(
                state.semaphore.acquire(), timeout=self.pool_wait_timeout
            )
        except asyncio.TimeoutError:
            # Record timeout in monitoring
            monitoring_svc = _get_monitoring_service()
            if monitoring_svc:
                pool_type = "clickhouse" if self.cluster_name == "primary" else f"clickhouse_{self.cluster_name}"
                if (
                    hasattr(monitoring_svc, "_last_metrics")
                    and pool_type in monitoring_svc._last_metrics
                ):
                    metrics = monitoring_svc._last_metrics[pool_type]
                    metrics.connection_timeouts += 1

            pool_stats = f"{state.active_connections}/{state.max_size} active, {len(state.idle)} idle"
            log.error(
                f"ClickHouse connection pool exhausted for cluster '{self.cluster_name}'. "
                f"Timeout waiting for connection after {self.pool_wait_timeout}s. "
                f"Pool state: {pool_stats}. "
                f"Consider increasing pool size or reducing connection hold time."
            )
            raise ConnectionPoolExhaustedException(
                f"ClickHouse connection pool exhausted for cluster '{self.cluster_name}'. "
                f"All {state.max_size} connections are in use. "
                f"Waited {self.pool_wait_timeout}s for an available connection."
            )

        try:
            # Lock access to the idle queue and active count
            async with state.lock:
                # Try to get an idle connection
                while state.idle:
                    potential_client = state.idle.popleft()
                    # Check liveness before yielding
                    is_alive = await self._ping_connection(potential_client)
                    if is_alive:
                        client = potential_client
                        log.debug("Reusing idle ClickHouse connection from pool.")
                        break  # Found a live connection
                    else:
                        log.warning(
                            "Discarding dead ClickHouse connection found in pool."
                        )
                        # Don't decrement active_connections here - idle connections are not counted as active
                        # Don't release semaphore here, it was acquired for the *request*
                        # Run disconnect in background task to avoid blocking release
                        asyncio.create_task(
                            asyncio.get_event_loop().run_in_executor(
                                None, potential_client.disconnect
                            )
                        )

                # If no suitable idle connection found, create a new one if allowed
                if client is None:
                    # We already hold the semaphore permit, check active count logic is sound
                    # If active_connections < max_size (should be guaranteed by semaphore), create new.
                    # Note: active_connections count might slightly lag if ping fails above,
                    # but semaphore is the primary guard against exceeding max_size.
                    log.debug(
                        f"No idle ClickHouse connection available. Creating new one. Active: {state.active_connections}/{state.max_size}"
                    )
                    try:
                        # Create connection in thread pool
                        client = await asyncio.get_event_loop().run_in_executor(
                            None, self._create_and_test_connection
                        )
                        state.active_connections += 1
                        acquired_new = True
                        log.debug(
                            f"New ClickHouse connection created. Active: {state.active_connections}/{state.max_size}"
                        )
                    except Exception as create_error:
                        # Record error in monitoring
                        monitoring_svc = _get_monitoring_service()
                        if monitoring_svc:
                            pool_type = "clickhouse" if self.cluster_name == "primary" else f"clickhouse_{self.cluster_name}"
                            if (
                                hasattr(monitoring_svc, "_last_metrics")
                                and pool_type in monitoring_svc._last_metrics
                            ):
                                metrics = monitoring_svc._last_metrics[pool_type]
                                metrics.connection_errors += 1

                        # Failed to create connection, release semaphore and raise
                        state.semaphore.release()

                        # Classify the error type for better handling
                        error_msg = str(create_error).lower()
                        if isinstance(create_error, ClickHouseNetworkError):
                            log.error(
                                f"Network error creating ClickHouse connection for cluster '{self.cluster_name}': {create_error}"
                            )
                            raise TransientConnectionException(
                                f"Network error creating ClickHouse connection for cluster '{self.cluster_name}': {create_error}"
                            ) from create_error
                        elif (
                            "connection refused" in error_msg or "timeout" in error_msg
                        ):
                            log.error(
                                f"Connection timeout/refused for ClickHouse cluster '{self.cluster_name}': {create_error}"
                            )
                            raise ConnectionTimeoutException(
                                f"Connection timeout/refused for ClickHouse cluster '{self.cluster_name}': {create_error}"
                            ) from create_error
                        elif (
                            "too many connections" in error_msg
                            or "connection limit" in error_msg
                        ):
                            log.error(
                                f"ClickHouse server connection limit reached for cluster '{self.cluster_name}': {create_error}"
                            )
                            raise ConnectionPoolExhaustedException(
                                f"ClickHouse server connection limit reached for cluster '{self.cluster_name}': {create_error}"
                            ) from create_error
                        else:
                            log.error(
                                f"Failed to create new ClickHouse connection for cluster '{self.cluster_name}': {create_error}"
                            )
                            raise ClickHouseException(
                                f"Failed to create new ClickHouse connection for cluster '{self.cluster_name}': {create_error}"
                            ) from create_error

            # Record acquisition time
            acquisition_time_ms = (time.time() - acquisition_start) * 1000
            monitoring_svc = _get_monitoring_service()
            if monitoring_svc:
                # Try to get script context for script-specific tracking
                try:
                    from magic_gateway.monitoring.script_context import (
                        get_current_script_name,
                    )

                    script_name = get_current_script_name()
                except ImportError:
                    script_name = None
                
                pool_type = "clickhouse" if self.cluster_name == "primary" else f"clickhouse_{self.cluster_name}"
                monitoring_svc.record_acquisition_time(
                    pool_type, acquisition_time_ms, script_name
                )

            # Log successful acquisition with timing (skip during startup)
            if hasattr(self, '_startup_complete') and self._startup_complete:
                log.debug(
                    f"Acquired ClickHouse connection in {acquisition_time_ms:.2f}ms. Active: {state.active_connections}, Idle: {len(state.idle)}"
                )

            # Record usage start time
            usage_start = time.time()

            # Yield the connection outside the lock
            yield client

            # Record usage time after yield returns
            monitoring_svc = _get_monitoring_service()
            if usage_start > 0 and monitoring_svc:
                usage_time_ms = (time.time() - usage_start) * 1000
                # Try to get script context for script-specific tracking
                try:
                    from magic_gateway.monitoring.script_context import (
                        get_current_script_name,
                    )

                    script_name = get_current_script_name()
                except ImportError:
                    script_name = None
                pool_type = "clickhouse" if self.cluster_name == "primary" else f"clickhouse_{self.cluster_name}"
                monitoring_svc.record_usage_time(
                    pool_type, usage_time_ms, script_name
                )
                log.debug(
                    f"ClickHouse connection for cluster '{self.cluster_name}' used for {usage_time_ms:.2f}ms"
                )

        except Exception as e:
            # Catch errors during acquire/create or from the `yield` block
            log.error(f"Error during ClickHouse connection context: {e}", exc_info=True)
            # Ensure semaphore is released if an error occurred before or during yield
            if (
                client is None or acquired_new
            ):  # If creation failed or error during yield of new conn
                state.semaphore.release()
            # If we had a reused client, the finally block handles release
            raise  # Re-raise the exception
        finally:
            # This block executes when exiting the `async with`
            if client:
                # Lock to safely return to idle queue or handle disconnect
                return_to_pool = True
                async with state.lock:
                    # Optional: Final check if connection is still usable after user code
                    # is_alive = await self._ping_connection(client)
                    # if not is_alive:
                    #    log.warning("ClickHouse connection died during use, discarding.")
                    #    return_to_pool = False
                    #    state.active_connections -= 1
                    #    asyncio.create_task(
                    #        asyncio.get_event_loop().run_in_executor(None, client.disconnect)
                    #    )

                    # Decide whether to keep the connection (return to idle queue)
                    # Keep if pool is not closing and we have space
                    if (
                        self._initialized and return_to_pool
                    ):  # Check if pool is still active
                        if (
                            len(state.idle) < state.max_size
                        ):  # Simple check: keep if < max
                            state.idle.append(client)
                            log.debug(
                                f"Returned ClickHouse connection to pool. Idle: {len(state.idle)}"
                            )
                        else:
                            # Pool is full (or we decided not to keep), disconnect it
                            log.debug(
                                f"ClickHouse pool idle queue full ({len(state.idle)}). Discarding connection."
                            )
                            # Don't decrement active_connections here if connection was reused from idle
                            # Only decrement if it was a newly created connection
                            if acquired_new:
                                state.active_connections -= 1
                            asyncio.create_task(
                                asyncio.get_event_loop().run_in_executor(
                                    None, client.disconnect
                                )
                            )
                    elif not return_to_pool:
                        # Already handled decrementing active_connections and disconnect task above
                        pass
                    else:  # Pool is closing or shutting down
                        log.debug(
                            "ClickHouse pool closing. Discarding connection instead of returning."
                        )
                        # Only decrement if it was a newly created connection
                        if acquired_new:
                            state.active_connections -= 1
                        asyncio.create_task(
                            asyncio.get_event_loop().run_in_executor(
                                None, client.disconnect
                            )
                        )

                # Always release the semaphore AFTER handling the connection
                state.semaphore.release()


# ==============================================================
# Logs Database Connection Manager
# ==============================================================


class LogsConnectionManager(BaseConnectionManager[AsyncConnection]):
    """
    Connection manager for Logs PostgreSQL database using a single connection.
    Provides an async context manager for acquiring a connection to the logs database.
    """

    _connection: Optional[AsyncConnection] = None  # Single connection instead of pool
    _connection_lock: asyncio.Lock = asyncio.Lock()  # Lock for connection access

    async def initialize(self, max_retries: int = 3, retry_delay: int = 5) -> None:
        """Initialize a single Logs PostgreSQL connection with retry logic."""
        if self._initialized:
            log.debug("Logs PostgreSQL connection already initialized.")
            return

        async with self._initializing_lock:
            # Double-check after acquiring lock
            if self._initialized:
                log.debug(
                    "Logs PostgreSQL connection already initialized (double check)."
                )
                return

            log.info("Initializing Logs PostgreSQL connection...")
            conn_info = f"Logs PostgreSQL: {settings.LOGS_DB_USER}@{settings.LOGS_DB_HOST}:{settings.LOGS_DB_PORT}/{settings.LOGS_DB_NAME}"
            log.debug(f"Attempting to connect to {conn_info}")

            retry_count = 0
            last_error = None
            while retry_count <= max_retries:
                try:
                    # Create a single connection
                    # Application name is already included in the DSN
                    connection = await AsyncConnection.connect(
                        conninfo=settings.LOGS_DB_DSN,
                        autocommit=True,  # Auto-commit for logging operations
                    )

                    # Test the connection with a simple query
                    await connection.execute("SELECT 1")

                    self._connection = connection
                    self._initialized = True
                    log.info(
                        f"Logs PostgreSQL connection initialized successfully ({conn_info})."
                    )
                    return  # Success
                except (
                    PsycopgOperationalError,
                    PsycopgInterfaceError,
                    TimeoutError,  # asyncio.wait_for timeout
                    Exception,  # Catch broad exceptions during init
                ) as e:
                    last_error = e
                    retry_count += 1
                    error_type = type(e).__name__
                    error_msg = str(e).split("\n")[0]  # Get first line of error

                    if self._connection:  # Cleanup connection on error
                        await self._connection.close()
                        self._connection = None

                    if retry_count <= max_retries:
                        log.warning(
                            f"Logs PostgreSQL connection attempt {retry_count}/{max_retries} failed: {error_type}: {error_msg}. "
                            f"Retrying in {retry_delay} seconds..."
                        )
                        await asyncio.sleep(retry_delay)
                    else:
                        log.error(
                            f"Failed to initialize Logs PostgreSQL connection after {max_retries + 1} attempts. "
                            f"Last error: {error_type}: {error_msg}",
                            exc_info=True
                            if settings.LOG_LEVEL.upper() == "DEBUG"
                            else False,
                        )
                        log.error(f"Connection details used: {conn_info}")
                        # Log specific advice based on common errors
                        if "connection refused" in error_msg.lower():
                            log.error(
                                "Connection refused. Ensure Logs PostgreSQL server is running and accessible."
                            )
                        elif "authentication failed" in error_msg.lower():
                            log.error(
                                "Authentication failed. Check Logs PostgreSQL credentials (user/password/host)."
                            )
                        elif (
                            "database" in error_msg.lower()
                            and "does not exist" in error_msg.lower()
                        ):
                            log.error(
                                f"Database '{settings.LOGS_DB_NAME}' does not exist."
                            )

                        # Keep manager uninitialized
                        self._initialized = False
                        self._connection = None
                        # Re-raise a specific exception to signal failure
                        raise PostgresException(
                            f"Failed to initialize Logs PostgreSQL connection after {max_retries + 1} attempts: {error_type}: {error_msg}"
                        ) from last_error

    async def close(self) -> None:
        """Close the Logs PostgreSQL connection gracefully."""
        if not self._initialized or not self._connection:
            log.debug("Logs PostgreSQL connection already closed or not initialized.")
            return

        log.info("Closing Logs PostgreSQL connection...")
        try:
            await self._connection.close()
            log.info("Logs PostgreSQL connection closed successfully.")
        except Exception as e:
            log.error(f"Error closing Logs PostgreSQL connection: {e}", exc_info=True)
            # Avoid raising an exception during shutdown if possible
        finally:
            self._initialized = False
            self._connection = None

    @asynccontextmanager
    async def connection(self) -> AsyncGenerator[AsyncConnection, None]:
        """
        Provide an async context manager to get the logs connection.
        Uses a lock to ensure exclusive access to the connection.

        Usage:
            async with logs_connection_manager.connection() as conn:
                await conn.execute(...)
        """
        await self._ensure_initialized()
        if (
            not self._connection
        ):  # Should be caught by _ensure_initialized, but defensive check
            raise PostgresException("Logs PostgreSQL connection is not available.")

        acquisition_start = time.time()
        acquisition_time_ms = 0
        usage_start = 0

        async with self._connection_lock:
            try:
                # Check if connection is still alive
                try:
                    await self._connection.execute("SELECT 1")
                except (PsycopgOperationalError, PsycopgInterfaceError):
                    # Connection lost, try to reconnect
                    log.warning(
                        "Logs PostgreSQL connection lost, attempting to reconnect..."
                    )
                    await self.close()
                    self._initialized = False
                    await self.initialize()
                    if not self._connection:
                        # Record error in monitoring
                        monitoring_svc = _get_monitoring_service()
                        if (
                            monitoring_svc
                            and hasattr(monitoring_svc, "_last_metrics")
                            and "logs" in monitoring_svc._last_metrics
                        ):
                            metrics = monitoring_svc._last_metrics["logs"]
                            metrics.connection_errors += 1

                        raise PostgresException(
                            "Failed to reconnect to Logs PostgreSQL."
                        )

                # Record acquisition time
                acquisition_time_ms = (time.time() - acquisition_start) * 1000
                monitoring_svc = _get_monitoring_service()
                if monitoring_svc:
                    # Try to get script context for script-specific tracking
                    try:
                        from magic_gateway.monitoring.script_context import (
                            get_current_script_name,
                        )

                        script_name = get_current_script_name()
                    except ImportError:
                        script_name = None
                    monitoring_svc.record_acquisition_time(
                        "logs", acquisition_time_ms, script_name
                    )

                # Log successful acquisition with timing (skip during startup)
                if hasattr(self, '_startup_complete') and self._startup_complete:
                    log.debug(
                        f"Acquired Logs PostgreSQL connection in {acquisition_time_ms:.2f}ms"
                    )

                # Record usage start time
                usage_start = time.time()

                yield self._connection

                # Record usage time after yield returns
                monitoring_svc = _get_monitoring_service()
                if usage_start > 0 and monitoring_svc:
                    usage_time_ms = (time.time() - usage_start) * 1000
                    # Try to get script context for script-specific tracking
                    try:
                        from magic_gateway.monitoring.script_context import (
                            get_current_script_name,
                        )

                        script_name = get_current_script_name()
                    except ImportError:
                        script_name = None
                    monitoring_svc.record_usage_time("logs", usage_time_ms, script_name)
                    # Log usage time (skip during startup)
                    if hasattr(self, '_startup_complete') and self._startup_complete:
                        log.debug(
                            f"Logs PostgreSQL connection used for {usage_time_ms:.2f}ms"
                        )
            except (PsycopgOperationalError, PsycopgInterfaceError) as e:
                # Record error in monitoring
                monitoring_svc = _get_monitoring_service()
                if (
                    monitoring_svc
                    and hasattr(monitoring_svc, "_last_metrics")
                    and "logs" in monitoring_svc._last_metrics
                ):
                    metrics = monitoring_svc._last_metrics["logs"]
                    metrics.connection_errors += 1

                log.error(
                    f"Logs PostgreSQL connection error: {e}",
                    exc_info=True,
                )
                raise PostgresException(f"Logs PostgreSQL connection error: {e}") from e
            except Exception as e:
                log.error(
                    f"Unexpected error with Logs PostgreSQL connection: {e}",
                    exc_info=True,
                )
                raise PostgresException(
                    f"Unexpected error with Logs PostgreSQL connection: {e}"
                ) from e


# ==============================================================
# ClickHouse Connection Manager Registry
# ==============================================================


class ClickHouseConnectionRegistry:
    """Registry for managing multiple ClickHouse connection managers."""

    def __init__(self):
        self._managers: Dict[str, ClickHouseConnectionManager] = {}
        self._initialized = False
        self._initializing_lock = asyncio.Lock()

    async def initialize(self) -> None:
        """Initialize all configured connection managers."""
        if self._initialized:
            log.debug("ClickHouse connection registry already initialized.")
            return

        async with self._initializing_lock:
            # Double-check after acquiring lock
            if self._initialized:
                log.debug(
                    "ClickHouse connection registry already initialized (double check)."
                )
                return

            log.info("Initializing ClickHouse connection registry...")

            try:
                # Initialize primary connection manager
                log.info("Initializing primary ClickHouse connection manager...")
                primary_manager = ClickHouseConnectionManager()
                await primary_manager.initialize()
                self._managers["primary"] = primary_manager
                log.info(
                    "Primary ClickHouse connection manager initialized successfully."
                )

                # Initialize cluster connection manager if configured
                if settings.has_clickhouse_cluster:
                    cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
                    log.info(
                        f"Initializing ClickHouse cluster connection manager '{cluster_name}'..."
                    )

                    # Create a cluster-specific connection manager with cluster name
                    cluster_manager = ClickHouseConnectionManager(
                        cluster_name=cluster_name, config_settings=settings
                    )

                    await cluster_manager.initialize()
                    self._managers[cluster_name] = cluster_manager
                    log.info(
                        f"ClickHouse cluster connection manager '{cluster_name}' initialized successfully."
                    )
                else:
                    log.info(
                        "ClickHouse cluster configuration not found. Only primary connection manager will be available."
                    )

                self._initialized = True
                log.info(
                    f"ClickHouse connection registry initialized successfully with {len(self._managers)} manager(s)."
                )

            except Exception as e:
                log.error(
                    f"Failed to initialize ClickHouse connection registry: {e}",
                    exc_info=True,
                )
                # Clean up any partially initialized managers
                await self.close()
                raise ClickHouseException(
                    f"Failed to initialize ClickHouse connection registry: {e}"
                ) from e

    def get_manager(self, cluster: Optional[str] = None) -> ClickHouseConnectionManager:
        """
        Get connection manager for specified cluster.

        Args:
            cluster: Optional cluster name. If None, returns primary manager.

        Returns:
            ClickHouseConnectionManager for the specified cluster

        Raises:
            ClickHouseException: If cluster is not found or registry not initialized
        """
        if not self._initialized:
            raise ClickHouseException(
                "ClickHouse connection registry is not initialized."
            )

        # Default to primary if no cluster specified
        if not cluster:
            cluster = "primary"

        if cluster not in self._managers:
            available_clusters = list(self._managers.keys())
            raise ClickHouseException(
                f"Unknown ClickHouse cluster: '{cluster}'. Available clusters: {available_clusters}"
            )

        return self._managers[cluster]

    async def close(self) -> None:
        """Close all connection managers."""
        if not self._managers:
            log.debug(
                "ClickHouse connection registry already closed or not initialized."
            )
            return

        log.info("Closing ClickHouse connection registry...")

        # Close all managers
        for cluster_name, manager in self._managers.items():
            try:
                log.info(f"Closing ClickHouse connection manager '{cluster_name}'...")
                await manager.close()
                log.info(
                    f"ClickHouse connection manager '{cluster_name}' closed successfully."
                )
            except Exception as e:
                log.error(
                    f"Error closing ClickHouse connection manager '{cluster_name}': {e}",
                    exc_info=True,
                )

        self._managers.clear()
        self._initialized = False
        log.info("ClickHouse connection registry closed successfully.")

    @property
    def initialized(self) -> bool:
        """Check if the registry is initialized."""
        return self._initialized

    def get_available_clusters(self) -> List[str]:
        """Get list of available cluster names."""
        return list(self._managers.keys())


# ==============================================================
# Global Instances
# ==============================================================

postgres_connection_manager = PostgresConnectionManager()
clickhouse_connection_manager = (
    ClickHouseConnectionManager()
)  # Keep for backward compatibility
logs_connection_manager = LogsConnectionManager()

# Global registry instance
clickhouse_registry = ClickHouseConnectionRegistry()

# Import monitoring service lazily to avoid circular imports
monitoring_service = None


def _get_monitoring_service():
    """Get the monitoring service instance lazily."""
    global monitoring_service
    if monitoring_service is None:
        try:
            from magic_gateway.monitoring.service import (
                connection_pool_monitoring_service,
            )

            monitoring_service = connection_pool_monitoring_service
        except ImportError:
            # If import fails, monitoring is not available
            monitoring_service = None
    return monitoring_service
