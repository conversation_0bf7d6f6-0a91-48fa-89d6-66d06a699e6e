# Application Settings
APP_NAME=MagicGateway
APP_VERSION=0.1.0
DEBUG=False
ENVIRONMENT=development  # development, staging, production
LOG_LEVEL=INFO

# Server Settings
HOST=0.0.0.0
PORT=8000

# Security Settings
SECRET_KEY=your-secret-key-here  # Generate a secure key for production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# LDAP Settings (New Format)
LDAP_SERVER=your-ldap-server.domain.com  # LDAP server hostname or IP
LDAP_DOMAIN=YOURDOMAIN  # NetBIOS domain name
LDAP_DOMAIN_FQDN=domain.com  # Fully qualified domain name
LDAP_BASE_DN=DC=domain,DC=com  # Base DN for LDAP searches
LDAP_ADMIN_GROUP_DN=CN=Domain Admins,CN=Users,DC=domain,DC=com  # Admin group DN

# LDAP Settings (Legacy Format - for backward compatibility)
LDAP_SERVER_URL=ldap://your-ldap-server:389
LDAP_BIND_DN=cn=admin,dc=example,dc=com
LDAP_BIND_PASSWORD=admin_password
LDAP_USER_SEARCH_BASE=ou=users,dc=example,dc=com
LDAP_USER_SEARCH_FILTER=(sAMAccountName={username})

# Test LDAP Credentials (for development/testing)
TEST_LDAP_USERNAME=testuser
TEST_LDAP_PASSWORD=testpassword

# PostgreSQL Settings
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=magic_gateway
POSTGRES_MIN_CONNECTIONS=1
POSTGRES_MAX_CONNECTIONS=10
POSTGRES_POOL_TIMEOUT=30  # Timeout for acquiring a connection from the pool
POSTGRES_MAX_IDLE_TIME=600  # Maximum time a connection can be idle
POSTGRES_MAX_LIFETIME=3600  # Maximum lifetime of a connection

# Logs Database Settings (separate PostgreSQL connection for logs)
LOGS_DB_HOST=***********
LOGS_DB_PORT=5432
LOGS_DB_USER=msr.shinyproxy.svc
LOGS_DB_PASSWORD=63wClvYSbfyCFH
LOGS_DB_NAME=logs
LOGS_DB_SCHEMA=api

# ClickHouse Settings (Primary Server)
CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=9000  # Native client port
CLICKHOUSE_HTTP_PORT=8123  # HTTP interface port
CLICKHOUSE_USER=msr.shinyproxy.svc
CLICKHOUSE_PASSWORD=63wClvYSbfyCFH
CLICKHOUSE_DATABASE=default
CLICKHOUSE_MIN_CONNECTIONS=1
CLICKHOUSE_MAX_CONNECTIONS=10
CLICKHOUSE_CONNECT_TIMEOUT=10  # Connection timeout in seconds
CLICKHOUSE_PING_TIMEOUT=5  # Timeout for liveness check in seconds
CLICKHOUSE_POOL_WAIT_TIMEOUT=30  # Timeout for waiting for a connection from the pool
CLICKHOUSE_SOCKET_TIMEOUT=30  # Socket timeout for send/receive operations

# ClickHouse Cluster Settings (Secondary Server)
# Configure these settings to enable the cluster connection
# This cluster is used for job_tmp database access (host: ***********, port: 5000)
CLICKHOUSE_CLUSTER_HOST=***********
CLICKHOUSE_CLUSTER_PORT=5000  # Native client port
CLICKHOUSE_CLUSTER_HTTP_PORT=8123  # HTTP interface port (optional)
CLICKHOUSE_CLUSTER_USER=svc_shinyproxy
CLICKHOUSE_CLUSTER_PASSWORD=63wClvYSbfyCFH
CLICKHOUSE_CLUSTER_DATABASE=job_tmp
CLICKHOUSE_CLUSTER_NAME=default_cluster  # Name used to identify this cluster in API calls
CLICKHOUSE_CLUSTER_MIN_CONNECTIONS=3
CLICKHOUSE_CLUSTER_MAX_CONNECTIONS=10
CLICKHOUSE_CLUSTER_CONNECT_TIMEOUT=10  # Connection timeout in seconds
CLICKHOUSE_CLUSTER_PING_TIMEOUT=5  # Timeout for liveness check in seconds
CLICKHOUSE_CLUSTER_POOL_WAIT_TIMEOUT=30  # Timeout for waiting for a connection from the pool
CLICKHOUSE_CLUSTER_SOCKET_TIMEOUT=30  # Socket timeout for send/receive operations

# Query Settings
MAX_QUERY_EXECUTION_TIME=300  # seconds
TRACK_QUERIES=True

# Temporary File Storage Settings
TEMP_FILE_STORAGE_PATH=downloads  # Path for temporary file storage
TEMP_FILE_RETENTION_HOURS=24  # Hours to keep temporary files before cleanup
TEMP_FILE_CLEANUP_INTERVAL_MINUTES=60  # Minutes between cleanup runs
TEMP_FILE_MAX_SIZE_MB=100  # Maximum size of temporary files in MB
TEMP_FILE_ALLOWED_EXTENSIONS=xlsx,csv,json,txt  # Allowed file extensions

# Script Execution Settings
ASSORTMENT_OPTIMIZER_TIMEOUT=1200  # Timeout for assortment optimizer script in seconds (20 minutes)
ASSORTMENT_OPTIMIZER_FILE_PREFIX=assortment_optimizer_  # Prefix for assortment optimizer result files
ASSORTMENT_OPTIMIZER_DEFAULT_EXTENSION=xlsx  # Default file extension for assortment optimizer results
