"""Monitoring package for MagicGateway."""

# Import will be done lazily to avoid circular imports
connection_pool_monitoring_service = None


def get_connection_pool_monitoring_service():
    """Get the connection pool monitoring service instance."""
    global connection_pool_monitoring_service
    if connection_pool_monitoring_service is None:
        from magic_gateway.monitoring.service import (
            connection_pool_monitoring_service as service,
        )

        connection_pool_monitoring_service = service
    return connection_pool_monitoring_service


__all__ = [
    "connection_pool_monitoring_service",
    "get_connection_pool_monitoring_service",
]
