"""JWT authentication for the MagicGateway application."""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

from jose import JW<PERSON><PERSON>r, jwt

from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import JWTException
from magic_gateway.core.logging_config import log


def create_access_token(
    data: Dict[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create a JWT access token.

    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time

    Returns:
        JWT token string
    """
    to_encode = data.copy()

    # Set expiration time
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    # Add expiration time and issued at time to payload
    to_encode.update(
        {"exp": expire, "iat": datetime.now(timezone.utc), "type": "access"}
    )

    # Create token
    try:
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        log.error(f"Failed to create access token: {e}")
        raise JWTException(f"Failed to create access token: {e}")


def create_refresh_token(
    data: Dict[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create a JWT refresh token.

    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time

    Returns:
        JWT token string
    """
    to_encode = data.copy()

    # Set expiration time
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS
        )

    # Add expiration time and issued at time to payload
    to_encode.update(
        {"exp": expire, "iat": datetime.now(timezone.utc), "type": "refresh"}
    )

    # Create token
    try:
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        log.error(f"Failed to create refresh token: {e}")
        raise JWTException(f"Failed to create refresh token: {e}")


def decode_token(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token.

    Args:
        token: JWT token string

    Returns:
        Decoded token payload
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        return payload
    except JWTError as e:
        log.error(f"Failed to decode token: {e}")
        raise JWTException(f"Failed to decode token: {e}")


def verify_access_token(token: str) -> Dict[str, Any]:
    """
    Verify an access token.

    Args:
        token: JWT token string

    Returns:
        Decoded token payload if valid
    """
    payload = decode_token(token)

    # Check token type
    if payload.get("type") != "access":
        raise JWTException("Invalid token type")

    return payload


def verify_refresh_token(token: str) -> Dict[str, Any]:
    """
    Verify a refresh token.

    Args:
        token: JWT token string

    Returns:
        Decoded token payload if valid
    """
    payload = decode_token(token)

    # Check token type
    if payload.get("type") != "refresh":
        raise JWTException("Invalid token type")

    return payload
