"""Authentication endpoints for the MagicGateway application."""

from typing import Any, Dict

from fastapi import API<PERSON>outer, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm


from magic_gateway.api.v1.models import Token, User, RefreshTokenRequest, LoginRequest
from magic_gateway.auth.dependencies import get_current_active_user, get_current_active_user_with_refresh
from magic_gateway.auth.jwt import (
    create_access_token,
    create_refresh_token,
    verify_refresh_token,
)
from magic_gateway.auth.ldap_auth import LDAPAuth
from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import NotAuthenticatedException
from magic_gateway.core.logging_config import log

router = APIRouter()


@router.post("/login", response_model=Token, tags=["authentication"])
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests.

    This endpoint authenticates users via LDAP and returns JWT tokens.
    Use the username and password fields in the form data.

    - **username**: LDAP username (UPN format for users, DN format for service accounts)
    - **password**: LDAP password

    Returns access token (30min) and refresh token (7 days).
    """
    # LDAP is the only authentication method
    try:
        log.info(f"Attempting LDAP authentication for user: {form_data.username}")
        ldap_success, ldap_user = await LDAPAuth.authenticate(
            form_data.username, form_data.password
        )

        if ldap_success and ldap_user:
            log.info(f"LDAP authentication successful for user: {form_data.username}")
            # Create token data
            token_data = {
                "sub": ldap_user["username"],
                "is_admin": ldap_user.get("is_admin", False),
                "auth_source": "ldap",
            }

            # Create tokens
            access_token = create_access_token(token_data)
            refresh_token = create_refresh_token(token_data)

            return Token(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            )
        else:
            log.warning(f"LDAP authentication failed for user: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed. Invalid username or password.",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"LDAP authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/login-json", response_model=Token, tags=["authentication"])
async def login_with_json(login_data: LoginRequest) -> Token:
    """
    JSON-based login endpoint for API clients.

    This endpoint provides an alternative to the OAuth2 form-based login
    for clients that prefer to send JSON data.

    - **username**: LDAP username (UPN format for users, DN format for service accounts)
    - **password**: LDAP password

    Returns access token (30min) and refresh token (7 days).
    """
    try:
        log.info(f"Attempting LDAP authentication for user: {login_data.username}")
        ldap_success, ldap_user = await LDAPAuth.authenticate(
            login_data.username, login_data.password
        )

        if ldap_success and ldap_user:
            log.info(f"LDAP authentication successful for user: {login_data.username}")
            # Create token data
            token_data = {
                "sub": ldap_user["username"],
                "is_admin": ldap_user.get("is_admin", False),
                "auth_source": "ldap",
            }

            # Create tokens
            access_token = create_access_token(token_data)
            refresh_token = create_refresh_token(token_data)

            return Token(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            )
        else:
            log.warning(f"LDAP authentication failed for user: {login_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed. Invalid username or password.",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"LDAP authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/refresh", response_model=Token, tags=["authentication"])
async def refresh_access_token(request: RefreshTokenRequest) -> Token:
    """
    Refresh access token using a valid refresh token.

    Provide a valid refresh token to get a new access token and refresh token pair.
    The old refresh token will be invalidated after successful refresh.

    - **refresh_token**: Valid refresh token obtained from login

    Returns new access token (30min) and refresh token (7 days).
    """
    try:
        # Verify refresh token
        payload = verify_refresh_token(request.refresh_token)

        # Create new token data
        token_data = {
            "sub": payload["sub"],
            "is_admin": payload.get("is_admin", False),
            "auth_source": payload.get("auth_source"),
        }

        # Add user_id if available
        if "user_id" in payload:
            token_data["user_id"] = payload["user_id"]

        # Create new tokens
        access_token = create_access_token(token_data)
        new_refresh_token = create_refresh_token(token_data)

        return Token(
            access_token=access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )
    except Exception as e:
        log.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/me", response_model=User)
async def read_users_me(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
) -> Any:
    """
    Get current user.
    """
    return current_user


@router.get("/ldap-config", tags=["authentication"])
async def get_ldap_config() -> Dict[str, Any]:
    """
    Get LDAP configuration status (for debugging).

    This endpoint returns the current LDAP configuration status
    without exposing sensitive information like passwords.
    """
    from magic_gateway.auth.ldap_auth import LDAPAuth

    config_status = {
        "ldap_available": LDAPAuth.is_available(),
        "ldap_server": bool(settings.LDAP_SERVER),
        "ldap_domain": bool(settings.LDAP_DOMAIN),
        "ldap_domain_fqdn": bool(settings.LDAP_DOMAIN_FQDN),
        "ldap_base_dn": bool(settings.LDAP_BASE_DN),
        "ldap_admin_group_dn": bool(settings.LDAP_ADMIN_GROUP_DN),
        "ldap_server_url": bool(settings.LDAP_SERVER_URL),
        "ldap_user_search_base": bool(settings.LDAP_USER_SEARCH_BASE),
    }

    # Add configuration values (without sensitive data)
    if settings.LDAP_SERVER:
        config_status["server_host"] = settings.LDAP_SERVER
    if settings.LDAP_DOMAIN_FQDN:
        config_status["domain_fqdn"] = settings.LDAP_DOMAIN_FQDN
    if settings.LDAP_BASE_DN:
        config_status["base_dn"] = settings.LDAP_BASE_DN

    return config_status


# User management endpoints have been removed
