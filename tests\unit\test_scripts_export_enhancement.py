"""Unit tests for the scripts export endpoint enhancement."""

import pytest
from magic_gateway.utils.db_utils import get_database_from_table


class TestScriptsExportEnhancement:
    """Test the export endpoint enhancement for database detection and logging."""

    def test_database_extraction_from_table_names(self):
        """Test that database names are correctly extracted from table names."""
        # Test cases for different table name formats
        test_cases = [
            ("job_result.result_table_123", "job_result"),
            ("kpi_results.result_table_456", "kpi_results"),
            ("database.schema.table", "database"),
            ("simple_table", ""),
            ("", ""),
        ]

        for table_name, expected_database in test_cases:
            result = get_database_from_table(table_name)
            assert result == expected_database, f"Failed for table_name: {table_name}"

    def test_database_logging_logic(self):
        """Test the database extraction and logging logic used in the export endpoint."""
        # Test the core logic that was added to the export endpoint

        # Simulate the metadata result from ClickHouse
        metadata_result = {
            "final_result_table": "job_result.result_table_123",
            "analysis_name": "test_analysis",
            "job_info": {},
        }

        # Extract database name (this is the logic added to the export endpoint)
        final_result_table = metadata_result["final_result_table"]
        database_name = get_database_from_table(final_result_table)

        # Verify the database extraction works correctly
        assert database_name == "job_result", (
            f"Expected 'job_result', got '{database_name}'"
        )

        # Test with different table names
        test_cases = [
            ("job_result.result_table_456", "job_result"),
            ("kpi_results.result_table_789", "kpi_results"),
            ("other_db.some_table", "other_db"),
            ("simple_table", ""),
        ]

        for table_name, expected_db in test_cases:
            extracted_db = get_database_from_table(table_name)
            assert extracted_db == expected_db, (
                f"Failed for {table_name}: expected '{expected_db}', got '{extracted_db}'"
            )

    def test_cluster_routing_logic(self):
        """Test that the cluster routing logic works correctly for different databases."""
        from magic_gateway.utils.db_utils import (
            should_use_cluster_for_database,
            get_cluster_for_table,
        )

        # Test cluster detection for different databases
        assert should_use_cluster_for_database("job_result") == True
        assert should_use_cluster_for_database("kpi_results") == False
        assert should_use_cluster_for_database("other_database") == False
        assert should_use_cluster_for_database("") == False
        assert should_use_cluster_for_database(None) == False

        # Test cluster selection for different table names
        assert get_cluster_for_table("job_result.table_123") == "default_cluster"
        assert get_cluster_for_table("kpi_results.table_456") is None
        assert get_cluster_for_table("simple_table") is None

        # Test explicit cluster parameter override
        assert (
            get_cluster_for_table("job_result.table_123", "custom_cluster")
            == "custom_cluster"
        )
        assert (
            get_cluster_for_table("kpi_results.table_456", "custom_cluster")
            == "custom_cluster"
        )

    def test_export_endpoint_imports(self):
        """Test that the export endpoint can import the required database utilities."""
        # This test verifies that the import we added to the scripts endpoint works
        try:
            from magic_gateway.api.v1.endpoints.scripts import get_database_from_table

            # Test that the imported function works
            result = get_database_from_table("job_result.test_table")
            assert result == "job_result"
        except ImportError as e:
            pytest.fail(
                f"Failed to import get_database_from_table in scripts endpoint: {e}"
            )
