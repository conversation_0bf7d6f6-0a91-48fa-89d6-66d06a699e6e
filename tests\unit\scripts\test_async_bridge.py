"""Tests for the async/sync bridge utilities."""

import asyncio
import pytest
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch

from magic_gateway.core.exceptions import ScriptExecutionException
from magic_gateway.scripts.async_bridge import (
    run_async_in_sync,
    sync_wrapper,
    execute_clickhouse_command,
    execute_clickhouse_query,
    execute_postgres_query,
    AsyncBridge,
)


class TestRunAsyncInSync:
    """Test the run_async_in_sync function."""

    async def sample_async_function(self, value: int) -> int:
        """Sample async function for testing."""
        await asyncio.sleep(0.01)  # Small delay to make it truly async
        return value * 2

    def test_run_async_in_sync_no_loop(self):
        """Test running async function when no event loop is running."""
        # This test runs in a sync context, so no event loop should be running
        result = run_async_in_sync(self.sample_async_function(5))
        assert result == 10

    def test_run_async_in_sync_with_exception(self):
        """Test error propagation from async to sync context."""

        async def failing_function():
            raise ValueError("Test error")

        with pytest.raises(ScriptExecutionException) as exc_info:
            run_async_in_sync(failing_function())

        assert "Async operation failed" in str(exc_info.value)
        assert "Test error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_run_async_in_sync_with_running_loop(self):
        """Test running async function when event loop is already running."""
        # This test runs in an async context, so we need to use thread pool
        result = run_async_in_sync(self.sample_async_function(3))
        assert result == 6


class TestSyncWrapper:
    """Test the sync_wrapper decorator."""

    def test_sync_wrapper_basic(self):
        """Test basic sync wrapper functionality."""

        @sync_wrapper
        async def async_add(a: int, b: int) -> int:
            await asyncio.sleep(0.01)
            return a + b

        result = async_add(3, 4)
        assert result == 7

    def test_sync_wrapper_with_exception(self):
        """Test sync wrapper error handling."""

        @sync_wrapper
        async def failing_async_function():
            raise RuntimeError("Async error")

        with pytest.raises(ScriptExecutionException):
            failing_async_function()


class TestClickHouseOperations:
    """Test ClickHouse async/sync bridge operations."""

    def test_execute_clickhouse_command_no_manager(self):
        """Test command execution with no connection manager."""
        with pytest.raises(ScriptExecutionException) as exc_info:
            execute_clickhouse_command(None, "SELECT 1")

        assert "ClickHouse connection manager is required" in str(exc_info.value)

    @patch("magic_gateway.scripts.async_bridge.run_async_in_sync")
    def test_execute_clickhouse_command_success(self, mock_run_async):
        """Test successful command execution."""
        mock_manager = MagicMock()
        mock_run_async.return_value = "success"

        result = execute_clickhouse_command(mock_manager, "CREATE TABLE test")

        assert result == "success"
        mock_run_async.assert_called_once()

    def test_execute_clickhouse_query_no_manager(self):
        """Test query execution with no connection manager."""
        with pytest.raises(ScriptExecutionException) as exc_info:
            execute_clickhouse_query(None, "SELECT * FROM test")

        assert "ClickHouse connection manager is required" in str(exc_info.value)

    @patch("magic_gateway.scripts.async_bridge.run_async_in_sync")
    def test_execute_clickhouse_query_success(self, mock_run_async):
        """Test successful query execution."""
        mock_manager = MagicMock()
        mock_df = MagicMock()
        mock_run_async.return_value = mock_df

        result = execute_clickhouse_query(mock_manager, "SELECT * FROM test")

        assert result == mock_df
        mock_run_async.assert_called_once()


class TestPostgreSQLOperations:
    """Test PostgreSQL async/sync bridge operations."""

    def test_execute_postgres_query_no_manager(self):
        """Test query execution with no connection manager."""
        with pytest.raises(ScriptExecutionException) as exc_info:
            execute_postgres_query(None, "SELECT 1")

        assert "PostgreSQL connection manager is required" in str(exc_info.value)

    @patch("magic_gateway.scripts.async_bridge.run_async_in_sync")
    def test_execute_postgres_query_success(self, mock_run_async):
        """Test successful query execution."""
        mock_manager = MagicMock()
        mock_result = [("test",)]
        mock_run_async.return_value = mock_result

        result = execute_postgres_query(mock_manager, "SELECT 'test'")

        assert result == mock_result
        mock_run_async.assert_called_once()


class TestAsyncBridge:
    """Test the AsyncBridge context manager."""

    def test_async_bridge_context_manager(self):
        """Test AsyncBridge as context manager."""
        connection_managers = {
            "clickhouse_primary": MagicMock(),
            "postgres": MagicMock(),
        }

        with AsyncBridge(connection_managers) as bridge:
            assert bridge.connection_managers == connection_managers
            assert bridge._executor is not None

    @patch("magic_gateway.scripts.async_bridge.execute_clickhouse_command")
    def test_async_bridge_clickhouse_command(self, mock_execute):
        """Test ClickHouse command execution through AsyncBridge."""
        mock_manager = MagicMock()
        connection_managers = {"clickhouse_primary": mock_manager}
        mock_execute.return_value = "success"

        with AsyncBridge(connection_managers) as bridge:
            result = bridge.clickhouse_command("CREATE TABLE test")

        assert result == "success"
        mock_execute.assert_called_once_with(mock_manager, "CREATE TABLE test", None)

    @patch("magic_gateway.scripts.async_bridge.execute_clickhouse_query")
    def test_async_bridge_clickhouse_query(self, mock_execute):
        """Test ClickHouse query execution through AsyncBridge."""
        mock_manager = MagicMock()
        connection_managers = {"clickhouse_primary": mock_manager}
        mock_df = MagicMock()
        mock_execute.return_value = mock_df

        with AsyncBridge(connection_managers) as bridge:
            result = bridge.clickhouse_query("SELECT * FROM test")

        assert result == mock_df
        mock_execute.assert_called_once_with(mock_manager, "SELECT * FROM test", None)

    @patch("magic_gateway.scripts.async_bridge.execute_postgres_query")
    def test_async_bridge_postgres_query(self, mock_execute):
        """Test PostgreSQL query execution through AsyncBridge."""
        mock_manager = MagicMock()
        connection_managers = {"postgres": mock_manager}
        mock_result = [("test",)]
        mock_execute.return_value = mock_result

        with AsyncBridge(connection_managers) as bridge:
            result = bridge.postgres_query("SELECT 'test'")

        assert result == mock_result
        mock_execute.assert_called_once_with(mock_manager, "SELECT 'test'", None)

    def test_async_bridge_missing_manager(self):
        """Test AsyncBridge with missing connection manager."""
        with AsyncBridge({}) as bridge:
            with pytest.raises(ScriptExecutionException):
                bridge.clickhouse_command("SELECT 1")

    @patch("magic_gateway.scripts.async_bridge.run_async_in_sync")
    def test_async_bridge_run_async(self, mock_run_async):
        """Test running async coroutine through AsyncBridge."""

        async def sample_coro():
            return "async_result"

        mock_run_async.return_value = "async_result"

        with AsyncBridge() as bridge:
            result = bridge.run_async(sample_coro())

        assert result == "async_result"
        mock_run_async.assert_called_once()


class TestAsyncFunctions:
    """Test the async functions directly."""

    @pytest.mark.asyncio
    async def test_execute_clickhouse_command_async_success(self):
        """Test async ClickHouse command execution."""
        from magic_gateway.scripts.async_bridge import execute_clickhouse_command_async

        # Mock connection manager and client
        mock_client = MagicMock()
        mock_client.command.return_value = "command_result"

        mock_manager = MagicMock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_client
        mock_manager.connection.return_value.__aexit__.return_value = None

        # Mock the event loop's run_in_executor
        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = AsyncMock()
            mock_loop.run_in_executor.return_value = "command_result"
            mock_get_loop.return_value = mock_loop

            result = await execute_clickhouse_command_async(
                mock_manager, "CREATE TABLE test"
            )

            assert result == "command_result"
            mock_loop.run_in_executor.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_clickhouse_query_async_success(self):
        """Test async ClickHouse query execution."""
        from magic_gateway.scripts.async_bridge import execute_clickhouse_query_async

        # Mock connection manager and client
        mock_client = MagicMock()
        mock_df = MagicMock()
        mock_client.query_df.return_value = mock_df

        mock_manager = MagicMock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_client
        mock_manager.connection.return_value.__aexit__.return_value = None

        # Mock the event loop's run_in_executor
        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = AsyncMock()
            mock_loop.run_in_executor.return_value = mock_df
            mock_get_loop.return_value = mock_loop

            result = await execute_clickhouse_query_async(
                mock_manager, "SELECT * FROM test"
            )

            assert result == mock_df
            mock_loop.run_in_executor.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_postgres_query_async_success(self):
        """Test async PostgreSQL query execution."""
        from magic_gateway.scripts.async_bridge import execute_postgres_query_async

        # Mock connection and cursor
        mock_cursor = AsyncMock()
        mock_cursor.fetchall.return_value = [("test",)]

        mock_conn = AsyncMock()
        mock_conn.execute.return_value = mock_cursor

        mock_manager = MagicMock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None

        result = await execute_postgres_query_async(mock_manager, "SELECT 'test'")

        assert result == [("test",)]
        mock_conn.execute.assert_called_once_with("SELECT 'test'")
        mock_cursor.fetchall.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_postgres_query_async_non_select(self):
        """Test async PostgreSQL non-SELECT query execution."""
        from magic_gateway.scripts.async_bridge import execute_postgres_query_async

        # Mock connection and cursor
        mock_cursor = AsyncMock()
        mock_cursor.rowcount = 1

        mock_conn = AsyncMock()
        mock_conn.execute.return_value = mock_cursor

        mock_manager = MagicMock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn
        mock_manager.connection.return_value.__aexit__.return_value = None

        result = await execute_postgres_query_async(
            mock_manager, "INSERT INTO test VALUES (1)"
        )

        assert result == 1
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES (1)")
        # fetchall should not be called for non-SELECT queries
        mock_cursor.fetchall.assert_not_called()
