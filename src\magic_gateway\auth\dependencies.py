"""Authentication dependencies for the MagicGateway application."""

from typing import Dict, Any
from datetime import datetime, timezone, timedelta

from fastapi import Depends, HTTPException, status, Request
from fastapi.responses import JSONResponse


from magic_gateway.auth.jwt import verify_access_token, decode_token, create_access_token, create_refresh_token
from magic_gateway.core.exceptions import (
    JWTException,
    NotAuthenticatedException,
    ForbiddenException,
)
from magic_gateway.core.logging_config import log

# OAuth2 scheme for token authentication
from fastapi.security import OAuth2PasswordBearer

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    Get the current authenticated user from the JWT token.

    Args:
        token: JWT token from the Authorization header

    Returns:
        User data from the token
    """
    try:
        # Verify the token
        payload = verify_access_token(token)

        # Extract user data
        username = payload.get("sub")
        if username is None:
            raise NotAuthenticatedException("Invalid token")

        # Create user data
        user_data = {
            "username": username,
            "is_admin": payload.get("is_admin", False),
            "auth_source": payload.get("auth_source"),
        }

        return user_data
    except JWTException as e:
        log.warning(f"JWT authentication failed: {e}")
        raise NotAuthenticatedException(str(e))


async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Get the current active user.

    Args:
        current_user: User data from get_current_user

    Returns:
        User data if user is active
    """
    # For LDAP users, we assume they are active if they can authenticate
    # For database users, we check the is_active flag during authentication
    return current_user


async def get_current_user_with_refresh(
    request: Request,
    token: str = Depends(oauth2_scheme)
) -> Dict[str, Any]:
    """
    Get the current authenticated user with automatic token refresh capability.
    
    This dependency checks if the token is about to expire (within 5 minutes)
    and automatically refreshes it if a refresh token is available in the request headers.
    For web interface requests without refresh tokens, it provides a more user-friendly
    error message directing them to re-login.
    
    Args:
        request: FastAPI request object to access headers
        token: JWT token from the Authorization header
        
    Returns:
        User data from the token, with refreshed tokens if applicable
    """
    try:
        # First, try to verify the current token
        try:
            payload = verify_access_token(token)
        except JWTException as e:
            # If token is expired, check if we have a refresh token
            refresh_token = request.headers.get("X-Refresh-Token")
            if not refresh_token:
                # Check if this is a web interface request (common user agents)
                user_agent = request.headers.get("User-Agent", "").lower()
                is_web_request = any(browser in user_agent for browser in ["mozilla", "chrome", "safari", "edge", "firefox"])
                
                if is_web_request:
                    log.info(f"Web interface token expired for user, redirecting to login")
                    # For web interface, provide a more user-friendly error
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail={
                            "error": "session_expired",
                            "message": "Your session has expired. Please log in again.",
                            "redirect_to_login": True
                        },
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                else:
                    log.warning(f"JWT authentication failed and no refresh token provided: {e}")
                    raise NotAuthenticatedException(str(e))
            
            # Try to refresh the token
            try:
                refresh_payload = decode_token(refresh_token)
                if refresh_payload.get("type") != "refresh":
                    raise JWTException("Invalid refresh token type")
                
                # Create new token data
                token_data = {
                    "sub": refresh_payload["sub"],
                    "is_admin": refresh_payload.get("is_admin", False),
                    "auth_source": refresh_payload.get("auth_source"),
                }
                
                # Create new tokens
                new_access_token = create_access_token(token_data)
                new_refresh_token = create_refresh_token(token_data)
                
                # Set new tokens in response headers for client to pick up
                request.state.new_access_token = new_access_token
                request.state.new_refresh_token = new_refresh_token
                
                # Verify the new access token to get payload
                payload = verify_access_token(new_access_token)
                
                log.info(f"Token refreshed successfully for user: {payload.get('sub')}")
                
            except JWTException as refresh_error:
                log.warning(f"Token refresh failed: {refresh_error}")
                # Check if this is a web interface request
                user_agent = request.headers.get("User-Agent", "").lower()
                is_web_request = any(browser in user_agent for browser in ["mozilla", "chrome", "safari", "edge", "firefox"])
                
                if is_web_request:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail={
                            "error": "session_expired",
                            "message": "Your session has expired. Please log in again.",
                            "redirect_to_login": True
                        },
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                else:
                    raise NotAuthenticatedException(f"Token expired and refresh failed: {refresh_error}")
        
        # Check if token is about to expire (within 5 minutes) and refresh if possible
        exp_timestamp = payload.get("exp")
        if exp_timestamp:
            exp_datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
            time_until_expiry = exp_datetime - datetime.now(timezone.utc)
            
            # If token expires within 5 minutes, try to refresh it
            if time_until_expiry < timedelta(minutes=5):
                refresh_token = request.headers.get("X-Refresh-Token")
                if refresh_token:
                    try:
                        refresh_payload = decode_token(refresh_token)
                        if refresh_payload.get("type") == "refresh":
                            # Create new token data
                            token_data = {
                                "sub": payload["sub"],
                                "is_admin": payload.get("is_admin", False),
                                "auth_source": payload.get("auth_source"),
                            }
                            
                            # Create new tokens
                            new_access_token = create_access_token(token_data)
                            new_refresh_token = create_refresh_token(token_data)
                            
                            # Set new tokens in response headers for client to pick up
                            request.state.new_access_token = new_access_token
                            request.state.new_refresh_token = new_refresh_token
                            
                            log.info(f"Token proactively refreshed for user: {payload.get('sub')}")
                    except JWTException as refresh_error:
                        log.warning(f"Proactive token refresh failed: {refresh_error}")
                        # Continue with current token since it's still valid
                else:
                    # Token is about to expire and no refresh token available
                    # For web interface, warn about upcoming expiration
                    user_agent = request.headers.get("User-Agent", "").lower()
                    is_web_request = any(browser in user_agent for browser in ["mozilla", "chrome", "safari", "edge", "firefox"])
                    
                    if is_web_request:
                        log.info(f"Web interface token expiring soon for user: {payload.get('sub')}")
                        # Set a flag in request state to indicate token is expiring soon
                        request.state.token_expiring_soon = True
        
        # Extract user data
        username = payload.get("sub")
        if username is None:
            raise NotAuthenticatedException("Invalid token")

        # Create user data
        user_data = {
            "username": username,
            "is_admin": payload.get("is_admin", False),
            "auth_source": payload.get("auth_source"),
        }

        return user_data
        
    except HTTPException:
        # Re-raise HTTPException as-is (for web interface redirects)
        raise
    except JWTException as e:
        log.warning(f"JWT authentication failed: {e}")
        raise NotAuthenticatedException(str(e))


async def get_current_user_smart_refresh(
    request: Request,
    token: str = Depends(oauth2_scheme)
) -> Dict[str, Any]:
    """
    Smart authentication dependency that handles both API clients and web interface.
    
    For API clients with refresh tokens: Automatically refreshes expired tokens
    For web interface: Provides user-friendly error messages for expired tokens
    
    Args:
        request: FastAPI request object to access headers
        token: JWT token from the Authorization header
        
    Returns:
        User data from the token, with refreshed tokens if applicable
    """
    # Check if this is likely a web interface request
    user_agent = request.headers.get("User-Agent", "").lower()
    is_web_request = any(browser in user_agent for browser in ["mozilla", "chrome", "safari", "edge", "firefox"])
    
    try:
        # First, try to verify the current token
        try:
            payload = verify_access_token(token)
        except JWTException as e:
            # Token is expired or invalid
            refresh_token = request.headers.get("X-Refresh-Token")
            
            if refresh_token:
                # API client with refresh token - try to refresh
                try:
                    refresh_payload = decode_token(refresh_token)
                    if refresh_payload.get("type") != "refresh":
                        raise JWTException("Invalid refresh token type")
                    
                    # Create new token data
                    token_data = {
                        "sub": refresh_payload["sub"],
                        "is_admin": refresh_payload.get("is_admin", False),
                        "auth_source": refresh_payload.get("auth_source"),
                    }
                    
                    # Create new tokens
                    new_access_token = create_access_token(token_data)
                    new_refresh_token = create_refresh_token(token_data)
                    
                    # Set new tokens in response headers for client to pick up
                    request.state.new_access_token = new_access_token
                    request.state.new_refresh_token = new_refresh_token
                    
                    # Verify the new access token to get payload
                    payload = verify_access_token(new_access_token)
                    
                    log.info(f"Token refreshed successfully for user: {payload.get('sub')}")
                    
                except JWTException as refresh_error:
                    log.warning(f"Token refresh failed: {refresh_error}")
                    if is_web_request:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail={
                                "error": "session_expired",
                                "message": "Your session has expired. Please log in again.",
                                "redirect_to_login": True
                            },
                            headers={"WWW-Authenticate": "Bearer"},
                        )
                    else:
                        raise NotAuthenticatedException(f"Token expired and refresh failed: {refresh_error}")
            else:
                # No refresh token available
                if is_web_request:
                    log.info(f"Web interface token expired, redirecting to login")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail={
                            "error": "session_expired",
                            "message": "Your session has expired. Please log in again.",
                            "redirect_to_login": True
                        },
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                else:
                    log.warning(f"JWT authentication failed and no refresh token provided: {e}")
                    raise NotAuthenticatedException(str(e))
        
        # Check if token is about to expire and handle proactively
        exp_timestamp = payload.get("exp")
        if exp_timestamp:
            exp_datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
            time_until_expiry = exp_datetime - datetime.now(timezone.utc)
            
            # Different thresholds for web vs API clients
            refresh_threshold = timedelta(minutes=10) if is_web_request else timedelta(minutes=5)
            
            if time_until_expiry < refresh_threshold:
                refresh_token = request.headers.get("X-Refresh-Token")
                if refresh_token:
                    # Try proactive refresh for clients with refresh tokens
                    try:
                        refresh_payload = decode_token(refresh_token)
                        if refresh_payload.get("type") == "refresh":
                            # Create new token data
                            token_data = {
                                "sub": payload["sub"],
                                "is_admin": payload.get("is_admin", False),
                                "auth_source": payload.get("auth_source"),
                            }
                            
                            # Create new tokens
                            new_access_token = create_access_token(token_data)
                            new_refresh_token = create_refresh_token(token_data)
                            
                            # Set new tokens in response headers for client to pick up
                            request.state.new_access_token = new_access_token
                            request.state.new_refresh_token = new_refresh_token
                            
                            log.info(f"Token proactively refreshed for user: {payload.get('sub')}")
                    except JWTException as refresh_error:
                        log.warning(f"Proactive token refresh failed: {refresh_error}")
                        # Continue with current token since it's still valid
                else:
                    # No refresh token - warn web interface users
                    if is_web_request:
                        log.debug(f"Web interface token expiring soon for user: {payload.get('sub')}")
                        request.state.token_expiring_soon = True
        
        # Extract user data
        username = payload.get("sub")
        if username is None:
            raise NotAuthenticatedException("Invalid token")

        # Create user data
        user_data = {
            "username": username,
            "is_admin": payload.get("is_admin", False),
            "auth_source": payload.get("auth_source"),
        }

        return user_data
        
    except HTTPException:
        # Re-raise HTTPException as-is (for web interface redirects)
        raise
    except JWTException as e:
        log.warning(f"JWT authentication failed: {e}")
        if is_web_request:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "session_expired",
                    "message": "Your session has expired. Please log in again.",
                    "redirect_to_login": True
                },
                headers={"WWW-Authenticate": "Bearer"},
            )
        else:
            raise NotAuthenticatedException(str(e))


async def get_current_active_user_with_refresh(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user_smart_refresh),
) -> Dict[str, Any]:
    """
    Get the current active user with smart refresh capability.

    Args:
        request: FastAPI request object
        current_user: User data from get_current_user_smart_refresh

    Returns:
        User data if user is active, with refreshed tokens if applicable
    """
    # For LDAP users, we assume they are active if they can authenticate
    # For database users, we check the is_active flag during authentication
    return current_user


async def get_current_user_web_friendly(
    request: Request,
    token: str = Depends(oauth2_scheme)
) -> Dict[str, Any]:
    """
    Get the current authenticated user with web-friendly error handling.
    
    This dependency is specifically designed for web interface endpoints.
    It provides user-friendly error messages and handles token expiration gracefully.
    
    Args:
        request: FastAPI request object to access headers
        token: JWT token from the Authorization header
        
    Returns:
        User data from the token
    """
    try:
        # Try to verify the current token
        payload = verify_access_token(token)
        
        # Check if token is about to expire (within 10 minutes for web interface)
        exp_timestamp = payload.get("exp")
        if exp_timestamp:
            exp_datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
            time_until_expiry = exp_datetime - datetime.now(timezone.utc)
            
            # If token expires within 10 minutes, set warning flag
            if time_until_expiry < timedelta(minutes=10):
                request.state.token_expiring_soon = True
                log.info(f"Web interface token expiring soon for user: {payload.get('sub')}")
        
        # Extract user data
        username = payload.get("sub")
        if username is None:
            raise NotAuthenticatedException("Invalid token")

        # Create user data
        user_data = {
            "username": username,
            "is_admin": payload.get("is_admin", False),
            "auth_source": payload.get("auth_source"),
        }

        return user_data
        
    except JWTException as e:
        log.info(f"Web interface authentication failed: {e}")
        # For web interface, always provide user-friendly error
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "error": "session_expired",
                "message": "Your session has expired. Please log in again.",
                "redirect_to_login": True
            },
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user_web_friendly(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user_web_friendly),
) -> Dict[str, Any]:
    """
    Get the current active user with web-friendly error handling.

    Args:
        request: FastAPI request object
        current_user: User data from get_current_user_web_friendly

    Returns:
        User data if user is active
    """
    return current_user


async def require_admin(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """
    Require the current user to be an admin.

    Args:
        current_user: User data from get_current_active_user

    Returns:
        User data if user is an admin
    """
    if not current_user.get("is_admin", False):
        log.warning(f"Admin access denied for user: {current_user.get('username')}")
        raise ForbiddenException("Admin privileges required")

    return current_user
