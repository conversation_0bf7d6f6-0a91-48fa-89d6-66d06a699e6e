-- насчитывание трипов на уровне ДХ
CREATE TEMPORARY TABLE 	{{buyers_final}} as 
SELECT  
	hhkey, 
	position_number, 	
	rwbasis,
	count(*) as trips_raw,
	any(projectc) as projectc,
	sum(weight_wave) as trips_ww,
	any(population) AS rw_population,
	sum(fullmass*rw_compensat) as trips_fullmass,
	any(weight_wave) AS buyers_ww
FROM axis_grouped as a
{% if calc_exclusive_penetration %}
INNER JOIN (
	SELECT hhkey, min(position_number) as position_number,  COUNT(*)
	FROM 
	(
		SELECT DISTINCT hhkey, position_number
		FROM axis_grouped
	)
	group by hhkey
    having count(*) = 1
) as b on b.hhkey = a.hhkey and  b.position_number = a.position_number 
{% endif %}
WHERE position_number NOT IN %(excluded_positions)s
{% if hh_filter %}
and hhkey in (SELECT hhkey FROM hh_filter_hhkey)
{% endif%}
GROUP BY 
	hhkey, 
	position_number, 
	rwbasis;


