"""Example script for the MagicGateway application."""

import time
from typing import Dict, Any, List, Optional

# Script metadata
METADATA = {
    "name": "Example Script",
    "description": "A simple example script that demonstrates the script execution system",
    "author": "MagicGateway Team",
    "version": "1.0.0",
    "requires_admin": False,
}


def run(
    name: str,
    count: int = 5,
    delay: float = 0.1,
    include_timestamp: bool = True,
    # Connection manager parameters (optional, injected by the script runner)
    clickhouse_manager: Optional[Any] = None,
    clickhouse_cluster_manager: Optional[Any] = None,
    postgres_manager: Optional[Any] = None,
    logs_manager: Optional[Any] = None,
) -> Dict[str, Any]:
    """
    Run the example script.

    Args:
        name: Name to greet
        count: Number of items to generate
        delay: Delay between items in seconds
        include_timestamp: Whether to include timestamps
        clickhouse_manager: ClickHouse primary connection manager (optional, injected by script runner)
        clickhouse_cluster_manager: ClickHouse cluster connection manager (optional, injected by script runner)
        postgres_manager: PostgreSQL connection manager (optional, injected by script runner)
        logs_manager: Logs PostgreSQL connection manager (optional, injected by script runner)

    Returns:
        Dictionary with results
    """
    # Log available connection managers (for debugging/monitoring purposes)
    available_managers = []
    if clickhouse_manager:
        available_managers.append("clickhouse_primary")
    if clickhouse_cluster_manager:
        available_managers.append("clickhouse_cluster")
    if postgres_manager:
        available_managers.append("postgres")
    if logs_manager:
        available_managers.append("logs")

    result = {
        "message": f"Hello, {name}!",
        "items": [],
        "connection_managers_available": available_managers,
    }

    # Generate items with delay
    for i in range(count):
        # Simulate some work
        time.sleep(delay)

        item = {
            "index": i,
            "value": f"Item {i} for {name}",
        }

        if include_timestamp:
            import datetime

            item["timestamp"] = datetime.datetime.now().isoformat()

        result["items"].append(item)

    return result
