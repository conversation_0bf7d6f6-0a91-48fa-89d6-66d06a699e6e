"""
Unit tests for OptimizedExportHandler.

This module tests the main orchestration class for export operations,
including job metadata retrieval, connection pool selection, and basic workflow coordination.
"""

import pytest
import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from magic_gateway.export.handlers import OptimizedExportHandler
from magic_gateway.export.adapters import ConnectionPoolAdapter
from magic_gateway.export.models import (
    ExportFormat,
    ExportContext,
    ExportOptions,
    ConnectionSelection,
)
from magic_gateway.export.exceptions import (
    ExportError,
    TableNotFoundError,
    ConnectionPoolExhaustedError,
)
from magic_gateway.db.connection_manager import ClickHouseConnectionManager


class TestOptimizedExportHandler:
    """Test cases for OptimizedExportHandler class."""

    @pytest.fixture
    def mock_connection_adapter(self):
        """Create a mock ConnectionPoolAdapter."""
        adapter = AsyncMock(spec=ConnectionPoolAdapter)
        return adapter

    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock ClickHouseConnectionManager."""
        manager = MagicMock(spec=ClickHouseConnectionManager)
        return manager

    @pytest.fixture
    def sample_job_metadata(self):
        """Sample job metadata for testing."""
        return {
            "table_name": "job_result.job_123_results",
            "database_name": "job_result",
            "engine": "MergeTree",
            "total_rows": 1000,
            "total_bytes": 50000,
            "job_id": 123
        }

    @pytest.fixture
    def sample_connection_selection(self, mock_connection_manager):
        """Sample connection selection result."""
        return ConnectionSelection(
            manager=mock_connection_manager,
            cluster_name="test_cluster",
            is_fallback=False,
            routing_reason="table_routing"
        )

    def test_init_default_adapter(self):
        """Test handler initialization with default connection adapter."""
        handler = OptimizedExportHandler()
        
        assert handler.connection_adapter is not None
        assert isinstance(handler.connection_adapter, ConnectionPoolAdapter)
        assert handler.request_id is not None
        assert len(handler.request_id) > 0

    def test_init_custom_adapter(self, mock_connection_adapter):
        """Test handler initialization with custom connection adapter."""
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        assert handler.connection_adapter is mock_connection_adapter
        assert handler.request_id is not None

    @pytest.mark.asyncio
    async def test_export_job_data_success(
        self,
        mock_connection_adapter,
        sample_job_metadata,
        sample_connection_selection
    ):
        """Test successful job data export."""
        # Setup mocks
        mock_connection_adapter.get_optimal_connection.return_value = sample_connection_selection
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch.object(handler, '_retrieve_job_metadata', return_value=sample_job_metadata) as mock_retrieve:
            with patch.object(handler, '_coordinate_export') as mock_coordinate:
                mock_coordinate.return_value = MagicMock()  # Mock StreamingResponse
                
                result = await handler.export_job_data(
                    job_id=123,
                    format=ExportFormat.CSV,
                    separate_periods=False,
                    intelligent_format=True
                )
                
                # Verify method calls
                mock_retrieve.assert_called_once_with(123)
                mock_connection_adapter.get_optimal_connection.assert_called_once()
                mock_coordinate.assert_called_once()
                
                # Verify connection adapter was called with correct table name
                call_args = mock_connection_adapter.get_optimal_connection.call_args
                assert call_args[1]['table_name'] == sample_job_metadata['table_name']
                assert call_args[1]['request_id'] == handler.request_id

    @pytest.mark.asyncio
    async def test_export_job_data_with_custom_request_id(
        self,
        mock_connection_adapter,
        sample_job_metadata,
        sample_connection_selection
    ):
        """Test job data export with custom request ID."""
        custom_request_id = "custom-request-123"
        mock_connection_adapter.get_optimal_connection.return_value = sample_connection_selection
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch.object(handler, '_retrieve_job_metadata', return_value=sample_job_metadata):
            with patch.object(handler, '_coordinate_export') as mock_coordinate:
                mock_coordinate.return_value = MagicMock()
                
                await handler.export_job_data(
                    job_id=123,
                    format=ExportFormat.EXCEL,
                    request_id=custom_request_id
                )
                
                # Verify request ID was updated
                assert handler.request_id == custom_request_id

    @pytest.mark.asyncio
    async def test_export_job_data_table_not_found(
        self,
        mock_connection_adapter
    ):
        """Test export when job table is not found."""
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        table_not_found_error = TableNotFoundError(
            request_id="test-request",
            job_id=123,
            table_name="job_123_results",
            database_name="job_result"
        )
        
        with patch.object(handler, '_retrieve_job_metadata', side_effect=table_not_found_error):
            with pytest.raises(TableNotFoundError) as exc_info:
                await handler.export_job_data(
                    job_id=123,
                    format=ExportFormat.CSV
                )
            
            assert exc_info.value.error_type == "table_not_found"
            assert "not found for job 123" in exc_info.value.message

    @pytest.mark.asyncio
    async def test_export_job_data_connection_pool_exhausted(
        self,
        mock_connection_adapter,
        sample_job_metadata
    ):
        """Test export when connection pool is exhausted."""
        # Setup mocks
        pool_exhausted_error = ConnectionPoolExhaustedError(
            request_id="test-request",
            pool_name="test_pool",
            active_connections=10,
            max_connections=10
        )
        mock_connection_adapter.get_optimal_connection.side_effect = pool_exhausted_error
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch.object(handler, '_retrieve_job_metadata', return_value=sample_job_metadata):
            with pytest.raises(ConnectionPoolExhaustedError) as exc_info:
                await handler.export_job_data(
                    job_id=123,
                    format=ExportFormat.PARQUET
                )
            
            assert exc_info.value.error_type == "connection_pool_exhausted"

    @pytest.mark.asyncio
    async def test_export_job_data_unexpected_error(
        self,
        mock_connection_adapter,
        sample_job_metadata
    ):
        """Test export with unexpected error gets wrapped in ExportError."""
        mock_connection_adapter.get_optimal_connection.side_effect = ValueError("Unexpected error")
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch.object(handler, '_retrieve_job_metadata', return_value=sample_job_metadata):
            with pytest.raises(ExportError) as exc_info:
                await handler.export_job_data(
                    job_id=123,
                    format=ExportFormat.CSV
                )
            
            assert exc_info.value.error_type == "unexpected_error"
            assert "Unexpected error during export" in exc_info.value.message
            assert exc_info.value.context["job_id"] == 123

    @pytest.mark.asyncio
    @patch('magic_gateway.export.handlers.optimized_export_handler.ClickHouseHandler.execute_query')
    async def test_retrieve_job_metadata_success(self, mock_execute_query):
        """Test successful job metadata retrieval."""
        # Mock successful table query response
        mock_execute_query.return_value = [{
            "database": "job_result",
            "table_name": "job_123_results",
            "engine": "MergeTree",
            "total_rows": 1000,
            "total_bytes": 50000
        }]
        
        handler = OptimizedExportHandler()
        result = await handler._retrieve_job_metadata(123)
        
        assert result["job_id"] == 123
        assert result["table_name"] == "job_result.job_123_results"
        assert result["database_name"] == "job_result"
        assert result["total_rows"] == 1000
        assert result["total_bytes"] == 50000
        assert result["engine"] == "MergeTree"

    @pytest.mark.asyncio
    @patch('magic_gateway.export.handlers.optimized_export_handler.ClickHouseHandler.execute_query')
    async def test_retrieve_job_metadata_table_not_found(self, mock_execute_query):
        """Test job metadata retrieval when table doesn't exist."""
        # Mock empty response (table not found)
        mock_execute_query.return_value = []
        
        handler = OptimizedExportHandler()
        
        with pytest.raises(TableNotFoundError) as exc_info:
            await handler._retrieve_job_metadata(123)
        
        assert exc_info.value.error_type == "table_not_found"
        assert "not found for job 123" in exc_info.value.message
        # The context is set by the exception constructor, not our custom context
        assert "job_id" in exc_info.value.context or "full_table_name" in exc_info.value.context

    @pytest.mark.asyncio
    @patch('magic_gateway.export.handlers.optimized_export_handler.ClickHouseHandler.execute_query')
    async def test_retrieve_job_metadata_database_error(self, mock_execute_query):
        """Test job metadata retrieval with database error."""
        # Mock database error
        mock_execute_query.side_effect = Exception("Database connection failed")
        
        handler = OptimizedExportHandler()
        
        with pytest.raises(ExportError) as exc_info:
            await handler._retrieve_job_metadata(123)
        
        assert exc_info.value.error_type == "metadata_retrieval_failed"
        assert "Failed to retrieve job metadata" in exc_info.value.message

    @pytest.mark.asyncio
    async def test_create_export_context(self, sample_job_metadata):
        """Test export context creation."""
        handler = OptimizedExportHandler()
        
        context = await handler._create_export_context(
            job_id=123,
            format=ExportFormat.EXCEL_FACTS,
            separate_periods=True,
            intelligent_format=False,
            metadata=sample_job_metadata
        )
        
        assert context.job_id == 123
        assert context.format == ExportFormat.EXCEL_FACTS
        assert context.table_name == sample_job_metadata["table_name"]
        assert context.database_name == sample_job_metadata["database_name"]
        assert context.metadata == sample_job_metadata
        assert context.connection_manager is None  # Not set yet
        
        # Check options
        assert context.options.separate_periods is True
        assert context.options.intelligent_format is False
        assert context.options.horizontal_facts is True  # Should be True for EXCEL_FACTS
        assert context.options.allow_format_change is True

    @pytest.mark.asyncio
    async def test_coordinate_export_integration(self, sample_job_metadata):
        """Test export coordination integration with mocked dependencies."""
        handler = OptimizedExportHandler()
        
        context = ExportContext(
            job_id=123,
            request_id=uuid.uuid4(),
            table_name=sample_job_metadata["table_name"],
            database_name=sample_job_metadata["database_name"],
            format=ExportFormat.CSV,
            options=ExportOptions(),
            metadata=sample_job_metadata,
            connection_manager=MagicMock()
        )
        
        # Mock the dependencies to avoid actual implementation calls
        mock_parquet_stream = AsyncMock()
        mock_streaming_response = MagicMock()
        
        with patch.object(handler.connection_adapter, 'export_to_parquet_stream_with_fallback', return_value=mock_parquet_stream):
            with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
                mock_pipeline = AsyncMock()
                mock_pipeline.convert_stream.return_value = mock_streaming_response
                mock_pipeline_class.return_value = mock_pipeline
                
                with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                    mock_resource_manager = AsyncMock()
                    mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                    
                    response = await handler._coordinate_export(context)
                    
                    # Verify it returns the mocked streaming response
                    assert response == mock_streaming_response


class TestOptimizedExportHandlerIntegration:
    """Integration tests for OptimizedExportHandler with real components."""

    @pytest.mark.asyncio
    async def test_handler_with_real_connection_adapter(self):
        """Test handler initialization with real ConnectionPoolAdapter."""
        # This test verifies that the handler can work with a real adapter
        # without mocking, testing the integration between components
        handler = OptimizedExportHandler()
        
        # Verify the handler has a real connection adapter
        assert isinstance(handler.connection_adapter, ConnectionPoolAdapter)
        assert handler.connection_adapter.registry is not None

    @pytest.mark.asyncio
    async def test_export_context_creation_with_all_formats(self):
        """Test export context creation with all supported formats."""
        handler = OptimizedExportHandler()
        sample_metadata = {
            "table_name": "job_result.job_456_results",
            "database_name": "job_result",
            "engine": "MergeTree",
            "total_rows": 500,
            "total_bytes": 25000,
            "job_id": 456
        }
        
        # Test all export formats
        formats_to_test = [
            (ExportFormat.CSV, False),
            (ExportFormat.EXCEL, False),
            (ExportFormat.EXCEL_FACTS, True),
            (ExportFormat.PARQUET, False),
        ]
        
        for format_type, expected_horizontal_facts in formats_to_test:
            context = await handler._create_export_context(
                job_id=456,
                format=format_type,
                separate_periods=False,
                intelligent_format=True,
                metadata=sample_metadata
            )
            
            assert context.format == format_type
            assert context.options.horizontal_facts == expected_horizontal_facts
            assert context.job_id == 456
            assert context.table_name == sample_metadata["table_name"]


class TestOptimizedExportHandlerCoordination:
    """Test cases for the unified parquet export coordination functionality."""

    @pytest.fixture
    def mock_connection_adapter(self):
        """Create a mock ConnectionPoolAdapter."""
        adapter = AsyncMock(spec=ConnectionPoolAdapter)
        return adapter

    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock ClickHouseConnectionManager."""
        manager = MagicMock(spec=ClickHouseConnectionManager)
        return manager

    @pytest.fixture
    def sample_export_context(self, mock_connection_manager):
        """Sample export context for testing."""
        return ExportContext(
            job_id=123,
            request_id=uuid.uuid4(),
            table_name="job_result.job_123_results",
            database_name="job_result",
            format=ExportFormat.CSV,
            options=ExportOptions(
                separate_periods=False,
                intelligent_format=True,
                horizontal_facts=False
            ),
            metadata={
                "total_rows": 1000,
                "total_bytes": 50000,
                "engine": "MergeTree"
            },
            connection_manager=mock_connection_manager
        )

    @pytest.mark.asyncio
    async def test_coordinate_export_success_csv(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test successful export coordination for CSV format."""
        # Setup mocks
        mock_parquet_stream = AsyncMock()
        mock_parquet_stream.__aiter__ = AsyncMock(return_value=iter([b"parquet_data_chunk"]))
        
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        
        mock_streaming_response = MagicMock()
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
            mock_pipeline = AsyncMock()
            mock_pipeline.convert_stream.return_value = mock_streaming_response
            mock_pipeline_class.return_value = mock_pipeline
            
            with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                mock_resource_manager = AsyncMock()
                mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                
                result = await handler._coordinate_export(sample_export_context)
                
                # Verify the result
                assert result == mock_streaming_response
                
                # Verify connection adapter was called correctly
                mock_connection_adapter.export_to_parquet_stream_with_fallback.assert_called_once()
                call_args = mock_connection_adapter.export_to_parquet_stream_with_fallback.call_args
                assert call_args[1]['table_name'] == sample_export_context.table_name
                assert call_args[1]['request_id'] == str(sample_export_context.request_id)
                assert call_args[1]['max_retries'] == 2
                
                # Verify format conversion pipeline was called
                mock_pipeline.convert_stream.assert_called_once()
                convert_args = mock_pipeline.convert_stream.call_args
                assert convert_args[1]['target_format'] == ExportFormat.CSV
                assert convert_args[1]['parquet_stream'] == mock_parquet_stream

    @pytest.mark.asyncio
    async def test_coordinate_export_success_excel_facts(
        self,
        mock_connection_adapter
    ):
        """Test successful export coordination for Excel Facts format."""
        # Create context for Excel Facts format
        excel_facts_context = ExportContext(
            job_id=456,
            request_id=uuid.uuid4(),
            table_name="job_result.job_456_results",
            database_name="job_result",
            format=ExportFormat.EXCEL_FACTS,
            options=ExportOptions(
                separate_periods=True,
                intelligent_format=True,
                horizontal_facts=True
            ),
            metadata={
                "total_rows": 2000,
                "total_bytes": 100000,
                "engine": "MergeTree"
            },
            connection_manager=MagicMock()
        )
        
        # Setup mocks
        mock_parquet_stream = AsyncMock()
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        mock_streaming_response = MagicMock()
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
            mock_pipeline = AsyncMock()
            mock_pipeline.convert_stream.return_value = mock_streaming_response
            mock_pipeline_class.return_value = mock_pipeline
            
            with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                mock_resource_manager = AsyncMock()
                mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                
                result = await handler._coordinate_export(excel_facts_context)
                
                # Verify the result
                assert result == mock_streaming_response
                
                # Verify conversion options were set correctly
                convert_args = mock_pipeline.convert_stream.call_args
                conversion_options = convert_args[1]['options']
                assert conversion_options.separate_periods is True
                assert conversion_options.horizontal_facts is True
                assert conversion_options.intelligent_format is True

    @pytest.mark.asyncio
    async def test_coordinate_export_parquet_streaming_failure(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test export coordination when parquet streaming fails."""
        # Setup connection adapter to raise an exception
        streaming_error = Exception("Connection failed")
        mock_connection_adapter.export_to_parquet_stream_with_fallback.side_effect = streaming_error
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
            mock_resource_manager = AsyncMock()
            mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
            
            with pytest.raises(ExportError) as exc_info:
                await handler._coordinate_export(sample_export_context)
            
            # Verify error details
            assert exc_info.value.error_type == "parquet_streaming_failed"
            assert "Failed to start parquet data streaming" in exc_info.value.message
            assert exc_info.value.context["job_id"] == sample_export_context.job_id
            assert exc_info.value.context["table_name"] == sample_export_context.table_name
            assert "Check ClickHouse server connectivity" in exc_info.value.recovery_suggestions

    @pytest.mark.asyncio
    async def test_coordinate_export_format_conversion_failure(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test export coordination when format conversion fails."""
        # Setup successful parquet streaming but failed conversion
        mock_parquet_stream = AsyncMock()
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        
        conversion_error = Exception("Format conversion failed")
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
            mock_pipeline = AsyncMock()
            mock_pipeline.convert_stream.side_effect = conversion_error
            mock_pipeline_class.return_value = mock_pipeline
            
            with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                mock_resource_manager = AsyncMock()
                mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                
                with pytest.raises(ExportError) as exc_info:
                    await handler._coordinate_export(sample_export_context)
                
                # Verify error details
                assert exc_info.value.error_type == "format_conversion_failed"
                assert f"Failed to convert parquet data to {sample_export_context.format.value}" in exc_info.value.message
                assert exc_info.value.context["job_id"] == sample_export_context.job_id
                assert exc_info.value.context["target_format"] == sample_export_context.format.value
                assert "Try a different export format" in exc_info.value.recovery_suggestions

    @pytest.mark.asyncio
    async def test_coordinate_export_unexpected_error(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test export coordination with unexpected error."""
        # Setup connection adapter to work but cause unexpected error elsewhere
        mock_parquet_stream = AsyncMock()
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        # Cause an unexpected error by making the resource manager fail
        with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
            mock_resource_manager_class.side_effect = RuntimeError("Unexpected system error")
            
            with pytest.raises(ExportError) as exc_info:
                await handler._coordinate_export(sample_export_context)
            
            # Verify error details
            assert exc_info.value.error_type == "export_coordination_failed"
            assert "Unexpected error during export coordination" in exc_info.value.message
            assert exc_info.value.context["job_id"] == sample_export_context.job_id
            assert exc_info.value.context["error_type"] == "RuntimeError"
            assert "Check system resources and connectivity" in exc_info.value.recovery_suggestions

    @pytest.mark.asyncio
    async def test_build_export_query(self, sample_export_context):
        """Test export query building."""
        handler = OptimizedExportHandler()
        
        query = await handler._build_export_query(sample_export_context)
        
        expected_query = f"SELECT * FROM {sample_export_context.table_name}"
        assert query == expected_query

    @pytest.mark.asyncio
    async def test_prepare_conversion_metadata(self, sample_export_context):
        """Test conversion metadata preparation."""
        handler = OptimizedExportHandler()
        
        metadata = await handler._prepare_conversion_metadata(sample_export_context)
        
        # Verify basic metadata structure
        assert metadata["filename"] == f"job_{sample_export_context.job_id}_export"
        assert metadata["request_id"] == str(sample_export_context.request_id)
        assert metadata["archive_parquet"] is True
        assert "export_timestamp" in metadata
        
        # Verify job info
        job_info = metadata["job_info"]
        assert job_info["job_id"] == sample_export_context.job_id
        assert job_info["export_format"] == sample_export_context.format.value
        assert job_info["table_name"] == sample_export_context.table_name
        assert job_info["database_name"] == sample_export_context.database_name
        assert job_info["total_rows"] == sample_export_context.metadata["total_rows"]
        assert job_info["total_bytes"] == sample_export_context.metadata["total_bytes"]
        assert job_info["request_id"] == str(sample_export_context.request_id)

    @pytest.mark.asyncio
    async def test_prepare_conversion_metadata_with_existing_timestamp(self, sample_export_context):
        """Test conversion metadata preparation with existing timestamp."""
        # Add existing timestamp to context metadata
        existing_timestamp = "2023-12-01T10:30:00Z"
        sample_export_context.metadata["export_timestamp"] = existing_timestamp
        
        handler = OptimizedExportHandler()
        metadata = await handler._prepare_conversion_metadata(sample_export_context)
        
        # Verify existing timestamp is preserved
        assert metadata["export_timestamp"] == existing_timestamp
        assert metadata["job_info"]["export_timestamp"] == existing_timestamp

    @pytest.mark.asyncio
    async def test_coordinate_export_with_resource_cleanup(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test that export coordination properly uses resource manager for cleanup."""
        # Setup mocks
        mock_parquet_stream = AsyncMock()
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        mock_streaming_response = MagicMock()
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
            mock_pipeline = AsyncMock()
            mock_pipeline.convert_stream.return_value = mock_streaming_response
            mock_pipeline_class.return_value = mock_pipeline
            
            with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                mock_resource_manager = AsyncMock()
                mock_resource_manager.request_id = "test-resource-manager-id"
                mock_resource_manager.register_stream.return_value = "stream-resource-id"
                # Make list_resources return a regular dict, not a coroutine
                mock_resource_manager.list_resources = MagicMock(return_value={"stream-resource-id": {"type": "stream"}})
                mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                
                result = await handler._coordinate_export(sample_export_context)
                
                # Verify resource manager was used
                mock_resource_manager.register_stream.assert_called_once()
                stream_call_args = mock_resource_manager.register_stream.call_args
                assert stream_call_args[1]['stream'] == mock_parquet_stream
                assert stream_call_args[1]['resource_id'] == f"parquet_stream_{sample_export_context.job_id}"
                assert stream_call_args[1]['cleanup_callback'] == handler._cleanup_parquet_stream
                
                # Verify resource listing was called for logging
                mock_resource_manager.list_resources.assert_called()
                
                assert result == mock_streaming_response

    @pytest.mark.asyncio
    async def test_log_connection_pool_status_with_stats(self, sample_export_context):
        """Test connection pool status logging with available statistics."""
        handler = OptimizedExportHandler()
        
        # Mock connection manager with pool stats
        mock_connection_manager = AsyncMock()
        mock_connection_manager.get_pool_stats.return_value = {
            "pool_size": 10,
            "active_connections": 3,
            "idle_connections": 7,
            "pending_requests": 1
        }
        mock_connection_manager.check_health.return_value = "healthy"
        mock_connection_manager.id = "test-connection-manager-123"
        
        sample_export_context.connection_manager = mock_connection_manager
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            await handler._log_connection_pool_status(sample_export_context, "test_stage")
            
            # Verify info log was called with pool status
            mock_log.info.assert_called_once()
            info_call = mock_log.info.call_args[0][0]
            assert "Connection pool status [test_stage]" in info_call
            assert f"job {sample_export_context.job_id}" in info_call
            assert "active=3" in info_call
            assert "idle=7" in info_call
            assert "health=healthy" in info_call
            
            # Verify debug log was called with detailed info
            mock_log.debug.assert_called_once()
            debug_call = mock_log.debug.call_args[0][0]
            assert "Detailed connection pool info:" in debug_call

    @pytest.mark.asyncio
    async def test_log_connection_pool_status_no_connection_manager(self, sample_export_context):
        """Test connection pool status logging when no connection manager is available."""
        handler = OptimizedExportHandler()
        
        # Set connection manager to None
        sample_export_context.connection_manager = None
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            await handler._log_connection_pool_status(sample_export_context, "test_stage")
            
            # Verify warning was logged
            mock_log.warning.assert_called_once()
            warning_call = mock_log.warning.call_args[0][0]
            assert "No connection manager available" in warning_call
            assert f"job: {sample_export_context.job_id}" in warning_call
            assert "stage: test_stage" in warning_call

    @pytest.mark.asyncio
    async def test_log_connection_pool_status_stats_error(self, sample_export_context):
        """Test connection pool status logging when stats retrieval fails."""
        handler = OptimizedExportHandler()
        
        # Mock connection manager that raises error on stats
        mock_connection_manager = AsyncMock()
        mock_connection_manager.get_pool_stats.side_effect = Exception("Stats not available")
        mock_connection_manager.check_health.return_value = "healthy"
        mock_connection_manager.id = "test-connection-manager-456"
        
        sample_export_context.connection_manager = mock_connection_manager
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            await handler._log_connection_pool_status(sample_export_context, "error_test")
            
            # Verify info log still called but with error info
            mock_log.info.assert_called_once()
            info_call = mock_log.info.call_args[0][0]
            assert "Connection pool status [error_test]" in info_call
            assert "active=unknown" in info_call
            
            # Verify debug log shows the error
            mock_log.debug.assert_called()
            debug_calls = [call[0][0] for call in mock_log.debug.call_args_list]
            assert any("Could not retrieve pool stats: Stats not available" in call for call in debug_calls)

    @pytest.mark.asyncio
    async def test_cleanup_parquet_stream_async_close(self):
        """Test parquet stream cleanup with async close method."""
        handler = OptimizedExportHandler()
        
        # Mock stream with aclose method
        mock_stream = AsyncMock()
        mock_stream.aclose = AsyncMock()
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            await handler._cleanup_parquet_stream(mock_stream)
            
            # Verify aclose was called
            mock_stream.aclose.assert_called_once()
            
            # Verify debug log
            mock_log.debug.assert_called_once_with("Closed async parquet stream")

    @pytest.mark.asyncio
    async def test_cleanup_parquet_stream_sync_close(self):
        """Test parquet stream cleanup with sync close method."""
        handler = OptimizedExportHandler()
        
        # Mock stream with close method (no aclose)
        mock_stream = MagicMock()
        mock_stream.close = MagicMock()
        # Ensure hasattr returns False for aclose
        del mock_stream.aclose
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            await handler._cleanup_parquet_stream(mock_stream)
            
            # Verify close was called
            mock_stream.close.assert_called_once()
            
            # Verify debug log
            mock_log.debug.assert_called_once_with("Closed parquet stream")

    @pytest.mark.asyncio
    async def test_cleanup_parquet_stream_no_close_method(self):
        """Test parquet stream cleanup when no close method is available."""
        handler = OptimizedExportHandler()
        
        # Mock stream with no close methods
        mock_stream = MagicMock()
        del mock_stream.close
        del mock_stream.aclose
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            await handler._cleanup_parquet_stream(mock_stream)
            
            # Verify debug log about no cleanup method
            mock_log.debug.assert_called_once()
            debug_call = mock_log.debug.call_args[0][0]
            assert "No specific cleanup method for stream type" in debug_call

    @pytest.mark.asyncio
    async def test_cleanup_parquet_stream_error_handling(self):
        """Test parquet stream cleanup error handling."""
        handler = OptimizedExportHandler()
        
        # Mock stream that raises error on close
        mock_stream = AsyncMock()
        mock_stream.aclose.side_effect = Exception("Cleanup failed")
        
        with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
            # Should not raise exception
            await handler._cleanup_parquet_stream(mock_stream)
            
            # Verify warning was logged
            mock_log.warning.assert_called_once()
            warning_call = mock_log.warning.call_args[0][0]
            assert "Error during parquet stream cleanup: Cleanup failed" in warning_call

    @pytest.mark.asyncio
    async def test_coordinate_export_resource_manager_integration(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test that export coordination integrates properly with resource manager logging."""
        # Setup mocks
        mock_parquet_stream = AsyncMock()
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        mock_streaming_response = MagicMock()
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
            mock_pipeline = AsyncMock()
            mock_pipeline.convert_stream.return_value = mock_streaming_response
            mock_pipeline_class.return_value = mock_pipeline
            
            with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                mock_resource_manager = AsyncMock()
                mock_resource_manager.request_id = "resource-mgr-123"
                mock_resource_manager.register_stream.return_value = "stream-id-456"
                # Make list_resources return a regular dict, not a coroutine
                mock_resource_manager.list_resources = MagicMock(return_value={
                    "stream-id-456": {"type": "stream", "stream_type": "AsyncMock"},
                    "temp-file-789": {"type": "temp_file", "exists": True}
                })
                mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                
                with patch.object(handler, '_log_connection_pool_status') as mock_log_pool_status:
                    with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
                        result = await handler._coordinate_export(sample_export_context)
                        
                        # Verify resource manager initialization was logged
                        debug_calls = [call[0][0] for call in mock_log.debug.call_args_list]
                        assert any("Initialized ExportResourceManager" in call for call in debug_calls)
                        assert any("resource-mgr-123" in call for call in debug_calls)
                        
                        # Verify connection pool status was logged at key stages
                        assert mock_log_pool_status.call_count == 2
                        pool_status_calls = [call[0][1] for call in mock_log_pool_status.call_args_list]
                        assert "before_streaming" in pool_status_calls
                        assert "conversion_complete" in pool_status_calls
                        
                        # Verify resource summary was logged
                        info_calls = [call[0][0] for call in mock_log.info.call_args_list]
                        success_log = next((call for call in info_calls if "Successfully created streaming response" in call), None)
                        assert success_log is not None
                        assert "resources_managed: 2" in success_log
                        
                        # Verify detailed resource info was logged at debug level
                        resource_debug_log = next((call for call in debug_calls if "Resource summary for job" in call), None)
                        assert resource_debug_log is not None
                        
                        assert result == mock_streaming_response

    @pytest.mark.asyncio
    async def test_coordinate_export_failure_resource_logging(
        self,
        mock_connection_adapter,
        sample_export_context
    ):
        """Test that resource status is logged on export failures."""
        # Setup mocks for successful streaming but failed conversion
        mock_parquet_stream = AsyncMock()
        mock_connection_adapter.export_to_parquet_stream_with_fallback.return_value = mock_parquet_stream
        
        conversion_error = Exception("Conversion failed")
        
        handler = OptimizedExportHandler(connection_adapter=mock_connection_adapter)
        
        with patch('magic_gateway.export.pipeline.format_conversion.FormatConversionPipeline') as mock_pipeline_class:
            mock_pipeline = AsyncMock()
            mock_pipeline.convert_stream.side_effect = conversion_error
            mock_pipeline_class.return_value = mock_pipeline
            
            with patch('magic_gateway.export.resources.manager.ExportResourceManager') as mock_resource_manager_class:
                mock_resource_manager = AsyncMock()
                mock_resource_manager.register_stream.return_value = "stream-id"
                # Make list_resources return a regular dict, not a coroutine
                mock_resource_manager.list_resources = MagicMock(return_value={
                    "stream-id": {"type": "stream", "stream_type": "AsyncMock"}
                })
                mock_resource_manager_class.return_value.__aenter__.return_value = mock_resource_manager
                
                with patch.object(handler, '_log_connection_pool_status') as mock_log_pool_status:
                    with patch('magic_gateway.export.handlers.optimized_export_handler.log') as mock_log:
                        with pytest.raises(ExportError):
                            await handler._coordinate_export(sample_export_context)
                        
                        # Verify connection pool status was logged for failure
                        pool_status_calls = [call[0][1] for call in mock_log_pool_status.call_args_list]
                        assert "conversion_failed" in pool_status_calls
                        
                        # Verify resource status was logged on failure
                        debug_calls = [call[0][0] for call in mock_log.debug.call_args_list]
                        failure_resource_log = next((call for call in debug_calls if "Resource status on conversion failure" in call), None)
                        assert failure_resource_log is not None