"""Database models for the MagicGateway application."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel


# User database model has been removed


class ApiQueryHistoryInDB(BaseModel):
    """Database model for API query history."""

    id: int
    query_id: str
    username: str
    query_text: str
    database_type: str
    execution_time: Optional[float] = None
    row_count: Optional[int] = None
    status: str
    error_message: Optional[str] = None
    created_at: datetime
