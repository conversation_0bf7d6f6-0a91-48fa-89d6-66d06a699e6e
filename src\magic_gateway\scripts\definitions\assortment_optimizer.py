"""Assortment Optimization script for the MagicGateway application."""

import os
import uuid
import tempfile
import asyncio
import time
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

from magic_gateway.utils.temp_file_manager import temp_file_manager
from magic_gateway.core.logging_config import log
from magic_gateway.core.config import settings

# Script metadata
METADATA = {
    "name": "Assortment Optimization",
    "description": "Performs advanced assortment optimization analysis to determine optimal product positioning and selection strategies. Analyzes historical data within a specified date range and generates comprehensive Excel reports with optimization recommendations including position rankings, performance metrics, and strategic insights.",
    "author": "MagicGateway Team",
    "version": "1.0.0",
    "requires_admin": False,
    "category": "Analytics",
    "tags": ["optimization", "assortment", "analytics", "excel", "reporting"],
    "estimated_runtime": "2-10 minutes depending on data volume and date range",
    "output_format": "Excel (.xlsx) file with optimization results and recommendations",
}


def run(
    start_date: str,
    end_date: str,
    json_axis_id: int,
    json_flt_hh_id: Optional[int],
    total_position_number: int,
    rest_position_number: int,
    id_panel: int,
    clickhouse_manager: Optional[Any] = None,
    clickhouse_cluster_manager: Optional[Any] = None,
    postgres_manager: Optional[Any] = None,
    logs_manager: Optional[Any] = None,
) -> Dict[str, Any]:
    """
    Run the assortment optimization script.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        json_axis_id: JSON axis ID (integer)
        json_flt_hh_id: JSON filter household ID (optional integer)
        total_position_number: Total position number (required integer)
        rest_position_number: REST position number (-1 if no REST in axis)
        id_panel: Panel ID (integer)
        clickhouse_manager: ClickHouse primary connection manager (optional, injected by script runner)
        clickhouse_cluster_manager: ClickHouse cluster connection manager (optional, injected by script runner)
        postgres_manager: PostgreSQL connection manager (optional, injected by script runner)
        logs_manager: Logs PostgreSQL connection manager (optional, injected by script runner)

    Returns:
        Dictionary with results including file path for download
    """
    # Define execution timeout (use assortment optimizer specific timeout or fallback to general query timeout)
    execution_timeout = getattr(
        settings,
        "ASSORTMENT_OPTIMIZER_TIMEOUT",
        getattr(settings, "MAX_QUERY_EXECUTION_TIME", 600),
    )
    temp_file_path = None
    start_time = time.time()

    # Log available connection managers (for debugging/monitoring purposes)
    available_managers = []
    if clickhouse_manager:
        available_managers.append("clickhouse_primary")
    if clickhouse_cluster_manager:
        available_managers.append("clickhouse_cluster")
    if postgres_manager:
        available_managers.append("postgres")
    if logs_manager:
        available_managers.append("logs")

    if available_managers:
        log.info(
            f"Assortment optimizer script has access to connection managers: {available_managers}"
        )
    else:
        log.warning("Assortment optimizer script running without connection managers")

    try:
        # Validate date formats
        try:
            log.info(f"Validating date format for start_date: {start_date}")
            datetime.strptime(start_date, "%Y-%m-%d")
            log.info(f"Validating date format for end_date: {end_date}")
            datetime.strptime(end_date, "%Y-%m-%d")
            log.info("Date format validation passed")
        except ValueError as e:
            log.error("Invalid date format: {}", e)
            return {
                "status": "error",
                "message": f"Invalid date format: {str(e)}",
                "error_type": "ValidationError",
                "error_details": {
                    "parameter": "start_date or end_date",
                    "expected_format": "YYYY-MM-DD",
                    "suggestion": "Use YYYY-MM-DD format (e.g., 2024-01-01)",
                },
            }

        # Validate date range
        log.info(f"Validating date range: {start_date} to {end_date}")
        if start_date > end_date:
            log.error(
                f"Invalid date range: start_date {start_date} is after end_date {end_date}"
            )
            return {
                "status": "error",
                "message": "Start date must be before or equal to end date",
                "error_type": "ValidationError",
                "error_details": {
                    "parameter": "date_range",
                    "start_date": start_date,
                    "end_date": end_date,
                    "suggestion": "Ensure start_date is before or equal to end_date",
                },
            }
        log.info("Date range validation passed")

        # Import the modified ao module
        try:
            from .assortment_optimizer.ao_modified import (
                calc_assortment_optimizer_with_params,
                AxisTranslationError,
            )
            from .assortment_optimizer.exception import RequestValidationError
        except ImportError as e:
            log.error("Failed to import assortment optimizer module: {}", e)
            return {
                "status": "error",
                "message": "Failed to load optimization module",
                "error_type": "ImportError",
                "error_details": {
                    "module": "assortment_optimizer.ao_modified",
                    "suggestion": "Ensure the assortment optimizer module is properly installed and accessible",
                },
            }

        # Create a temporary file for the optimization output
        try:
            with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as temp_file:
                temp_file_path = temp_file.name
                log.info(
                    f"Created temporary file for optimization output: {temp_file_path}"
                )
        except (OSError, IOError) as e:
            log.error("Failed to create temporary file: {}", e)
            return {
                "status": "error",
                "message": f"Failed to create temporary output file: {str(e)}",
                "error_type": "FileSystemError",
                "error_details": {"operation": "create_temp_file"},
            }

        try:
            # Execute the optimization with parameters
            log.info(
                f"Starting assortment optimization with parameters: start_date={start_date}, end_date={end_date}, json_axis_id={json_axis_id}, id_panel={id_panel}"
            )
            log.info(f"Temp file path: {temp_file_path}")
            log.info(f"About to call calc_assortment_optimizer_with_params")

            # Check for timeout during execution
            remaining_time = execution_timeout - (time.time() - start_time)
            if remaining_time <= 0:
                raise TimeoutError(
                    f"Script execution timed out after {execution_timeout} seconds"
                )

            # Execute the optimization with comprehensive error handling
            try:
                calc_assortment_optimizer_with_params(
                    start_date=start_date,
                    end_date=end_date,
                    json_axis_id=json_axis_id,
                    json_flt_hh_id=json_flt_hh_id,
                    total_position_number=total_position_number,
                    rest_position_number=rest_position_number,
                    id_panel=id_panel,
                    output_file_path=temp_file_path,
                )
                log.info(
                    f"calc_assortment_optimizer_with_params completed successfully"
                )
            except ConnectionError as e:
                log.error("Database connection error during optimization: {}", e)
                return {
                    "status": "error",
                    "message": "Failed to connect to database during optimization",
                    "error_type": "ConnectionError",
                    "error_details": {
                        "database_type": "ClickHouse or CP-API",
                        "suggestion": "Check database connectivity and retry",
                        "original_error": str(e),
                    },
                }
            except RequestValidationError as e:
                log.error("Request validation error during optimization: {}", e)
                return {
                    "status": "error",
                    "message": f"Invalid request parameters: {str(e)}",
                    "error_type": "RequestValidationError",
                    "error_details": {
                        "suggestion": "Verify that all parameters are valid and within expected ranges",
                        "original_error": str(e),
                    },
                }

            # Check if the file was actually created and has content
            if (
                not os.path.exists(temp_file_path)
                or os.path.getsize(temp_file_path) == 0
            ):
                log.error(f"Output file not created or empty: {temp_file_path}")
                return {
                    "status": "error",
                    "message": "Optimization completed but no output file was generated",
                    "error_type": "FileNotFoundError",
                    "error_details": {
                        "expected_file": temp_file_path,
                        "suggestion": "Check optimization parameters and data availability for the specified date range",
                    },
                }

            # Read the generated file and save it through TempFileManager
            try:
                with open(temp_file_path, "rb") as f:
                    file_content = f.read()

                # Validate file content
                if len(file_content) == 0:
                    log.error(f"Generated file is empty: {temp_file_path}")
                    return {
                        "status": "error",
                        "message": "Generated optimization file is empty",
                        "error_type": "FileSystemError",
                        "error_details": {
                            "operation": "validate_file_content",
                            "suggestion": "Check optimization parameters and data availability",
                        },
                    }

                # Save the file through TempFileManager using configured settings
                file_id = temp_file_manager.save_result_file(
                    file_content,
                    extension=getattr(
                        settings, "ASSORTMENT_OPTIMIZER_DEFAULT_EXTENSION", "xlsx"
                    ),
                    prefix=getattr(
                        settings,
                        "ASSORTMENT_OPTIMIZER_FILE_PREFIX",
                        "assortment_optimizer_",
                    ),
                )
                log.info(f"Saved optimization result file with ID: {file_id}")

                return {
                    "status": "success",
                    "message": "Assortment optimization completed successfully",
                    "result_file_id": file_id,
                    "download_url": f"/api/v1/scripts/download/{file_id}",
                    "execution_time_seconds": round(time.time() - start_time, 2),
                    "parameters": {
                        "start_date": start_date,
                        "end_date": end_date,
                        "json_axis_id": json_axis_id,
                        "json_flt_hh_id": json_flt_hh_id,
                        "total_position_number": total_position_number,
                        "rest_position_number": rest_position_number,
                        "id_panel": id_panel,
                    },
                }
            except (OSError, IOError) as e:
                log.error("Failed to read or save result file: {}", e)
                return {
                    "status": "error",
                    "message": f"Failed to process optimization result file: {str(e)}",
                    "error_type": "FileSystemError",
                    "error_details": {
                        "operation": "save_result_file",
                        "suggestion": "Check disk space and file permissions",
                        "original_error": str(e),
                    },
                }
            except MemoryError as e:
                log.error("Memory error while processing result file: {}", e)
                return {
                    "status": "error",
                    "message": "Insufficient memory to process optimization result file",
                    "error_type": "MemoryError",
                    "error_details": {
                        "operation": "save_result_file",
                        "suggestion": "Try reducing the date range or contact system administrator",
                    },
                }

        except AxisTranslationError as e:
            log.error("Axis translation error: {}", e)
            return {
                "status": "error",
                "message": f"Failed to translate axis: {str(e)}",
                "error_type": "AxisTranslationError",
                "error_details": {
                    "axis_id": json_axis_id,
                    "suggestion": "Verify that the axis ID exists and is accessible",
                    "original_error": str(e),
                },
            }
        except ValueError as e:
            error_message = str(e)
            log.error("Value error in optimization: {}", e)

            # Provide more specific error handling for common ValueError scenarios
            if "empty dataset" in error_message.lower():
                return {
                    "status": "error",
                    "message": "No data found for the specified parameters",
                    "error_type": "ValueError",
                    "error_details": {
                        "reason": "empty_dataset",
                        "suggestion": "Try adjusting the date range or axis parameters",
                        "parameters_used": {
                            "start_date": start_date,
                            "end_date": end_date,
                            "json_axis_id": json_axis_id,
                            "id_panel": id_panel,
                        },
                    },
                }
            elif "not an axis" in error_message.lower():
                return {
                    "status": "error",
                    "message": f"Invalid axis configuration: {error_message}",
                    "error_type": "ValueError",
                    "error_details": {
                        "reason": "invalid_axis_type",
                        "axis_id": json_axis_id,
                        "suggestion": "Verify that the provided axis ID corresponds to a valid axis object",
                    },
                }
            else:
                return {
                    "status": "error",
                    "message": error_message,
                    "error_type": "ValueError",
                    "error_details": {
                        "suggestion": "Check parameter values and data availability",
                        "original_error": error_message,
                    },
                }
        except TimeoutError as e:
            log.error("Optimization timed out: {}", e)
            return {
                "status": "error",
                "message": f"Optimization timed out after {execution_timeout} seconds",
                "error_type": "TimeoutError",
                "error_details": {
                    "timeout_seconds": execution_timeout,
                    "suggestion": "Try reducing the date range or increasing the timeout value",
                    "parameters_used": {
                        "start_date": start_date,
                        "end_date": end_date,
                        "json_axis_id": json_axis_id,
                    },
                },
            }
        except Exception as e:
            error_message = str(e)
            log.error("Optimization execution error: {}", e, exc_info=True)

            # Handle specific common errors with better messages
            if "connection" in error_message.lower():
                return {
                    "status": "error",
                    "message": "Database connection error during optimization",
                    "error_type": "ConnectionError",
                    "error_details": {
                        "suggestion": "Check database connectivity and retry",
                        "original_error": error_message,
                        "traceback": traceback.format_exc(),
                    },
                }
            elif (
                "permission" in error_message.lower()
                or "access" in error_message.lower()
            ):
                return {
                    "status": "error",
                    "message": "Access denied during optimization",
                    "error_type": "PermissionError",
                    "error_details": {
                        "suggestion": "Check user permissions for database access",
                        "original_error": error_message,
                    },
                }
            else:
                return {
                    "status": "error",
                    "message": f"Optimization execution failed: {error_message}",
                    "error_type": type(e).__name__,
                    "error_details": {
                        "suggestion": "Contact system administrator if the problem persists",
                        "original_error": error_message,
                        "traceback": traceback.format_exc(),
                    },
                }

        finally:
            # Clean up the temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    log.info(f"Cleaned up temporary file: {temp_file_path}")
                except OSError as e:
                    log.warning(
                        f"Failed to clean up temporary file {temp_file_path}: {e}"
                    )

    except Exception as e:
        error_message = str(e)
        log.error("Unexpected error in assortment optimizer: {}", e, exc_info=True)

        # Provide specific error handling for common outer-level errors
        if "validation" in error_message.lower():
            return {
                "status": "error",
                "message": f"Parameter validation failed: {error_message}",
                "error_type": "ValidationError",
                "error_details": {
                    "suggestion": "Check that all required parameters are provided with correct formats",
                    "original_error": error_message,
                },
            }
        elif "import" in error_message.lower() or "module" in error_message.lower():
            return {
                "status": "error",
                "message": "Failed to load required modules",
                "error_type": "ImportError",
                "error_details": {
                    "suggestion": "Contact system administrator - required dependencies may be missing",
                    "original_error": error_message,
                },
            }
        elif (
            "configuration" in error_message.lower()
            or "settings" in error_message.lower()
        ):
            return {
                "status": "error",
                "message": "System configuration error",
                "error_type": "ConfigurationError",
                "error_details": {
                    "suggestion": "Contact system administrator - system configuration may be invalid",
                    "original_error": error_message,
                },
            }
        else:
            return {
                "status": "error",
                "message": f"Assortment optimization failed: {error_message}",
                "error_type": type(e).__name__,
                "error_details": {
                    "suggestion": "Contact system administrator if the problem persists",
                    "original_error": error_message,
                    "traceback": traceback.format_exc(),
                },
            }
