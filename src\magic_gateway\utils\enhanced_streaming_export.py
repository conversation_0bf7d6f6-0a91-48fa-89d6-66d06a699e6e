"""Enhanced streaming utility functions for exporting data to Excel format.

This module provides backward compatibility for the enhanced streaming export functionality.
The actual implementation has been moved to streaming_excel_writer.py for better organization.

This module now imports and re-exports the new streaming implementation.
"""

# Import the new streaming implementation
from magic_gateway.utils.streaming_excel_writer import (
    StreamingExcelWriter,
    EnhancedStreamingResponse,
    OPTIMAL_CHUNK_SIZE,
    get_memory_usage_mb,
    identify_fact_value_columns,
    identify_index_columns,
    identify_period_column,
)

# Backward compatibility alias
EnhancedStreamingExcelWriter = StreamingExcelWriter

# Re-export everything for backward compatibility
__all__ = [
    "StreamingExcelWriter",
    "EnhancedStreamingExcelWriter",
    "EnhancedStreamingResponse",
    "OPTIMAL_CHUNK_SIZE",
    "get_memory_usage_mb",
    "identify_fact_value_columns",
    "identify_index_columns",
    "identify_period_column",
]
