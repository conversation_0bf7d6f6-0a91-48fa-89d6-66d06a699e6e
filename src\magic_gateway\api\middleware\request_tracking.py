# magic_gateway/api/middleware/request_tracking.py

import time
from typing import Any, Dict, Optional
import uuid
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response
from starlette.types import ASGIApp
from fastapi import status
from datetime import datetime, timezone

from magic_gateway.core.logging_config import log
from magic_gateway.tracking.service import RequestTrackingService
from magic_gateway.tracking.models import RequestStatus  # Import RequestStatus enum

# Holder for the service instance (or inject via app state)
request_tracker: Optional[RequestTrackingService] = None


def set_request_tracker_instance(tracker: RequestTrackingService):
    """Set the global tracker instance for the middleware."""
    global request_tracker
    request_tracker = tracker


class RequestTrackingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # Ideally, inject the service instance here, but BaseHTTPMiddleware init is limited.
        # We'll use the global instance set via `set_request_tracker_instance`
        # or retrieve from app.state if configured there.

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        start_time = time.monotonic()
        start_dt = datetime.now(timezone.utc)  # Use timezone-aware datetime
        request_id = uuid.uuid4()

        # Make request_id accessible to endpoints
        request.state.request_id = request_id
        request.state.start_time_dt = start_dt  # Store start datetime

        # Get tracker instance (ensure it's initialized)
        tracker = (
            request_tracker or request.app.state.request_tracker_service
        )  # Example using app.state
        if not tracker:
            log.error("RequestTrackingService not initialized for middleware!")
            # Fallback or raise error? For now, proceed without tracking start
            initial_log = None
        else:
            # Log request start (fire and forget or await?) Await is safer.
            try:
                initial_log = await tracker.start_request(
                    request_id=request_id,
                    endpoint_path=request.url.path,
                    http_method=request.method,
                    client_ip=request.client.host if request.client else None,
                )
            except Exception as e:
                log.error(f"Middleware failed to log request start {request_id}: {e}")
                initial_log = None  # Ensure it's None if logging failed

        response = None
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR  # Default status code
        final_status = RequestStatus.FAILED  # Default status
        error: Optional[Exception] = None
        username: Optional[str] = None  # Will be populated if auth succeeds
        task_details: Optional[Dict[str, Any]] = None  # Can be updated by endpoint

        try:
            # --- Process Request ---
            response = await call_next(request)
            # --- Request Processed ---

            status_code = response.status_code
            # Determine final status based on HTTP code
            if 200 <= status_code < 400:
                final_status = RequestStatus.COMPLETED
            else:
                final_status = (
                    RequestStatus.FAILED
                )  # 4xx/5xx are considered failed requests

            # Try to get username and task_details potentially set by endpoint/auth deps
            username = getattr(request.state, "request_username", None)
            task_details = getattr(request.state, "request_task_details", None)

        except Exception as e:
            # Unhandled exception in endpoint or downstream middleware
            log.error(
                f"Unhandled exception during request {request_id}: {str(e)}", exc_info=True
            )
            error = e
            final_status = RequestStatus.FAILED
            # We won't have a response object here, keep default status_code 500
            # Re-raise the exception so FastAPI's exception handlers can process it
            # and generate the actual 500 response. The 'finally' block will still run.
            raise
        finally:
            # Log request end, regardless of success or failure
            if tracker:
                # Use the start_dt stored in state for accurate duration calculation
                start_dt_from_state = getattr(request.state, "start_time_dt", start_dt)

                # Get potentially updated username/task_details from state
                username = getattr(request.state, "request_username", username)
                task_details = getattr(
                    request.state, "request_task_details", task_details
                )

                if error:
                    # If an exception occurred before response was created
                    await tracker.fail_request(
                        request_id=request_id,
                        status_code=status_code,  # Should be 500
                        start_time=start_dt_from_state,
                        error=error,
                        username=username,
                        task_details=task_details,
                        status=final_status,  # Usually FAILED
                    )
                elif response:
                    # If request completed (successfully or with HTTP error code)
                    await tracker.complete_request(  # Use complete_request for both success/HTTP errors
                        request_id=request_id,
                        status_code=status_code,
                        start_time=start_dt_from_state,
                        username=username,
                        task_details=task_details,
                        status=final_status,  # COMPLETED or FAILED based on status_code
                    )
                else:
                    # Should not happen if error handling is correct, but log just in case
                    log.error(
                        f"Request {request_id} finished in middleware without response or error."
                    )
                    # Log a generic failure?
                    await tracker.fail_request(
                        request_id=request_id,
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        start_time=start_dt_from_state,
                        error=RuntimeError("Middleware finished unexpectedly"),
                        username=username,
                        task_details=task_details,
                        status=RequestStatus.FAILED,
                    )

            # Add Server-Timing header (optional)
            # process_time = time.monotonic() - start_time
            # if response:
            #    response.headers["Server-Timing"] = f"total;dur={process_time * 1000:.1f}"

        # Return the response (or allow the raised exception to propagate)
        # If an exception was raised, this line is effectively skipped for that request.
        return response