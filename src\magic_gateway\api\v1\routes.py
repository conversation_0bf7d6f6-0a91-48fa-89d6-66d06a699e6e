"""API routes for the MagicGateway application."""

from fastapi import APIRouter

from magic_gateway.api.v1.endpoints import (
    auth,
    clickhouse,
    postgres,
    scripts,
    admin,
)

# Create API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(clickhouse.router, prefix="/clickhouse", tags=["clickhouse"])
api_router.include_router(postgres.router, prefix="/postgres", tags=["postgres"])
api_router.include_router(scripts.router, prefix="/scripts", tags=["scripts"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
