"""Script execution context tracking for connection monitoring."""

import contextvars
from typing import Optional

# Context variable to track the current script name
current_script_name: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar(
    "current_script_name", default=None
)


class ScriptExecutionContext:
    """Context manager for tracking script execution context."""

    def __init__(self, script_name: str):
        """
        Initialize the script execution context.

        Args:
            script_name: Name of the script being executed
        """
        self.script_name = script_name
        self._token = None

    def __enter__(self):
        """Enter the script execution context."""
        self._token = current_script_name.set(self.script_name)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the script execution context."""
        if self._token is not None:
            current_script_name.reset(self._token)


def get_current_script_name() -> Optional[str]:
    """
    Get the name of the currently executing script.

    Returns:
        The script name if within a script execution context, None otherwise
    """
    return current_script_name.get()


def set_script_context(script_name: str) -> ScriptExecutionContext:
    """
    Create a script execution context.

    Args:
        script_name: Name of the script

    Returns:
        Script execution context manager
    """
    return ScriptExecutionContext(script_name)
