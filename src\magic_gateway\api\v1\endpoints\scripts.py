import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

# Import Request for injection
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.responses import StreamingResponse, HTMLResponse

# Import pg_parser library
from pg_parser.pg_ast_parser import PgAstParser

# Import settings for table mappings
from magic_gateway.core.config import settings

# Import the view checker script
from magic_gateway.utils.utils import get_axis_type_from_view_name
from magic_gateway.scripts.pg_to_ch_view_checker import check_pg_to_ch_view_conversion

# Import new dependencies and models
from magic_gateway.api.deps import (
    get_request_tracker_service,
    track_request_details,
    update_request_context,
    get_connection_managers,
    check_connection_managers_initialization,
    prepare_connection_managers_for_injection,
)
from magic_gateway.tracking.service import RequestTrackingService

# Import shared API models (likely defined in magic_gateway/api/v1/models.py)
# Ensure these models exist and are correctly defined
from magic_gateway.api.v1.models import (
    ScriptInfo,
    ScriptRequest,
    ScriptResponse,
    PgToClickHouseConversionRequest,
    PgToClickHouseConversionResponse,
    PgToChViewCheckerRequest,
    PgToChViewCheckerResponse,
    ExportFormat,
    ExcelLayout,
    ExportRequest,
    ExportResponse,
)

from magic_gateway.auth.dependencies import (
    get_current_active_user_with_refresh,
)  # Keep auth deps
from magic_gateway.core.exceptions import (
    ScriptExecutionException,
    ForbiddenException,
    PostgresException,
    ClickHouseException,
    ConnectionPoolException,
    ConnectionPoolExhaustedException,
    ConnectionTimeoutException,
    TransientConnectionException,
)  # Keep exceptions
from magic_gateway.core.logging_config import log
from magic_gateway.db.postgres_handler import PostgresHandler
from magic_gateway.utils import (
    get_formatted_column_mapping,
)
from magic_gateway.utils.temp_file_manager import temp_file_manager

# Assuming ScriptRunner exists and has been adapted or can be used as is
# Import the actual runner and its dependency getter
from magic_gateway.scripts.runner import ScriptRunner  # Assuming this exists
from magic_gateway.api.deps import get_script_runner  # Assuming this exists in deps.py

router = APIRouter(
    tags=["scripts"],
    responses={
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"},
        500: {"description": "Internal server error"},
    },
)


@router.get(
    "/",
    response_model=List[ScriptInfo],
    summary="List available scripts",
    description="Get a list of all scripts available to the current user",
    responses={
        200: {
            "description": "List of available scripts with metadata and parameter information",
            "content": {
                "application/json": {
                    "examples": {
                        "scripts_list": {
                            "summary": "Available Scripts",
                            "description": "Example response showing available scripts including assortment optimizer",
                            "value": [
                                {
                                    "name": "assortment_optimizer",
                                    "metadata": {
                                        "name": "Assortment Optimization",
                                        "description": "Performs advanced assortment optimization analysis to determine optimal product positioning and selection strategies",
                                        "author": "MagicGateway Team",
                                        "version": "1.0.0",
                                        "requires_admin": False,
                                    },
                                    "parameters": [
                                        {
                                            "name": "start_date",
                                            "type": "str",
                                            "required": True,
                                            "default": None,
                                            "description": "Start date for the analysis period in YYYY-MM-DD format",
                                        },
                                        {
                                            "name": "end_date",
                                            "type": "str",
                                            "required": True,
                                            "default": None,
                                            "description": "End date for the analysis period in YYYY-MM-DD format",
                                        },
                                        {
                                            "name": "json_axis_id",
                                            "type": "int",
                                            "required": True,
                                            "default": None,
                                            "description": "JSON axis identifier used for data filtering and analysis",
                                        },
                                        {
                                            "name": "json_flt_hh_id",
                                            "type": "Optional[int]",
                                            "required": False,
                                            "default": None,
                                            "description": "Optional JSON filter household ID for additional data filtering",
                                        },
                                        {
                                            "name": "total_position_number",
                                            "type": "int",
                                            "required": True,
                                            "default": None,
                                            "description": "Total number of positions to consider in the optimization analysis",
                                        },
                                        {
                                            "name": "rest_position_number",
                                            "type": "int",
                                            "required": True,
                                            "default": None,
                                            "description": "Number of REST positions in the analysis (-1 for no REST positions)",
                                        },
                                        {
                                            "name": "id_panel",
                                            "type": "int",
                                            "required": True,
                                            "default": None,
                                            "description": "Panel identifier specifying which data panel to use for the analysis",
                                        },
                                    ],
                                }
                            ],
                        }
                    }
                }
            },
        }
    },
)
async def list_scripts(
    request: Request,  # Inject Request
    request_id: uuid.UUID = Depends(
        track_request_details
    ),  # Get request_id (optional here, but consistent)
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
    script_runner: ScriptRunner = Depends(get_script_runner),
    _: RequestTrackingService = Depends(
        get_request_tracker_service
    ),  # Unused but required for middleware
) -> Any:
    """
    List all available scripts, filtered by user permissions.

    This endpoint returns a comprehensive list of all scripts available to the current user,
    including detailed metadata and parameter information for each script. Scripts requiring
    admin privileges are only shown to admin users.

    ## Response Format

    Each script in the response includes:
    - **name**: Internal script identifier used for execution
    - **metadata**: Script information including name, description, author, version, and admin requirements
    - **parameters**: Detailed parameter specifications including types, requirements, and descriptions

    ## Available Scripts

    ### Assortment Optimizer
    - **Purpose**: Advanced assortment optimization analysis
    - **Output**: Excel file with optimization recommendations
    - **Runtime**: 2-10 minutes depending on data volume
    - **Admin Required**: No

    ## Authentication

    Requires valid JWT token. Admin-only scripts are filtered out for non-admin users.

    ## Usage

    Use this endpoint to discover available scripts and their parameter requirements
    before executing them via the `/run/{script_name}` endpoint.
    """
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Add optional task details
    task_details = {"task_type": "script_list"}
    await update_request_context(request, task_details=task_details)

    try:
        # Use await if the runner method is async
        all_scripts_metadata = (
            await script_runner.get_available_scripts()
        )  # Assuming this returns list of dicts/ScriptInfo

        # Filter based on admin status
        available_scripts = []
        for script_data in all_scripts_metadata:
            # Instantiate ScriptInfo to access metadata easily, handle potential errors
            try:
                info_model = ScriptInfo(**script_data)  # Validate against model
                requires_admin = info_model.metadata.requires_admin
                if not requires_admin or current_user.is_admin:
                    available_scripts.append(info_model)
            except Exception as pydantic_error:
                log.error(
                    f"Failed to parse script metadata for '{script_data.get('name')}': {pydantic_error}"
                )

        # Middleware handles completion logging (status 200 OK)
        return available_scripts
    except Exception as e:
        # Middleware will catch and log as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error listing scripts: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list scripts: {e}",
        )


@router.post(
    "/run/{script_name}",
    tags=["scripts"],
    response_model=None,  # Allow dynamic response model based on script type
    responses={
        200: {
            "description": "Script executed successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "assortment_optimizer": {
                            "summary": "Assortment Optimizer Response",
                            "value": {
                                "script_id": "assortment_optimizer_20240101_123456",
                                "script_name": "assortment_optimizer",
                                "start_time": "2024-01-01T12:00:00Z",
                                "end_time": "2024-01-01T12:05:00Z",
                                "execution_time": 300.0,
                                "result": {
                                    "status": "success",
                                    "message": "Optimization completed",
                                },
                                "error": None,
                                "result_file_id": "ao_12345678-1234-1234-1234-123456789abc",
                                "download_url": "/api/v1/scripts/download/ao_12345678-1234-1234-1234-123456789abc",
                            },
                        },
                        "generic_script": {
                            "summary": "Generic Script Response",
                            "value": {
                                "script_id": "script_20240101_123456",
                                "script_name": "example_script",
                                "start_time": "2024-01-01T12:00:00Z",
                                "end_time": "2024-01-01T12:01:00Z",
                                "execution_time": 60.0,
                                "result": {"message": "Script completed successfully"},
                                "error": None,
                            },
                        },
                    }
                }
            },
        }
    },
    summary="Execute a script with parameters",
    description="Run a predefined script with the provided parameters and return execution results",
)
async def run_script(
    script_name: str,
    script_request: ScriptRequest,  # Contains parameters
    request: Request,  # Inject Request
    request_id: uuid.UUID = Depends(track_request_details),  # Get request_id
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
    script_runner: ScriptRunner = Depends(get_script_runner),
    connection_managers_dict: Optional[Dict[str, Any]] = Depends(
        get_connection_managers
    ),
    _: RequestTrackingService = Depends(
        get_request_tracker_service
    ),  # Unused but required for middleware
) -> Any:
    """
    Execute a script with the provided parameters.

    This endpoint allows execution of various scripts available in the system. Each script
    has its own parameter requirements and return formats. The response includes execution
    details, results, and error information if applicable.

    ## Available Scripts

    ### Assortment Optimizer (`assortment_optimizer`)
    Performs assortment optimization analysis with configurable parameters.

    **Required Parameters:**
    - `start_date` (string): Start date in YYYY-MM-DD format (e.g., "2024-01-01")
    - `end_date` (string): End date in YYYY-MM-DD format (e.g., "2024-12-31")
    - `json_axis_id` (integer): JSON axis ID, must be greater than 0
    - `total_position_number` (integer): Total position number, must be greater than 0
    - `rest_position_number` (integer): REST position number (-1 indicates no REST in axis)
    - `id_panel` (integer): Panel ID, must be greater than 0

    **Optional Parameters:**
    - `json_flt_hh_id` (integer): JSON filter household ID, must be greater than 0 if provided
    - `timeout_seconds` (integer): Custom timeout in seconds for script execution

    **Response Format:**
    The assortment optimizer returns an enhanced response that includes:
    - `result_file_id`: Unique identifier for the generated Excel result file
    - `download_url`: URL endpoint for downloading the result file
    - `execution_time_seconds`: Time taken to complete the optimization
    - `parameters`: Echo of the input parameters used

    **Usage Example:**
    ```json
    {
        "parameters": {
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "json_axis_id": 123,
            "json_flt_hh_id": 456,
            "total_position_number": 10,
            "rest_position_number": 5,
            "id_panel": 8,
            "timeout_seconds": 600
        }
    }
    ```

    ## Error Handling

    The endpoint provides detailed error information for various failure scenarios:

    - **Validation Errors (400)**: Invalid parameter formats or values
    - **Permission Errors (403)**: Insufficient privileges for admin-required scripts
    - **Not Found Errors (404)**: Script does not exist
    - **Execution Errors (400)**: Script-specific runtime errors
    - **System Errors (500)**: Unexpected system failures

    Error responses include:
    - `error_type`: Classification of the error
    - `error_details`: Additional context about the failure
    - `request_id`: Unique identifier for tracking the request

    ## Authentication

    This endpoint requires a valid JWT token. Admin-level scripts require the user
    to have admin privileges.

    ## Rate Limiting

    Script execution may be subject to timeout limits. Use the `timeout_seconds`
    parameter to specify custom timeouts where supported.
    """
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Add script details to task_details for tracking/potential cancellation
    task_details = {
        "task_type": "script_run",
        "script_name": script_name,
        "parameters": script_request.parameters,
        "start_time": datetime.now().isoformat(),
        # "script_run_id": None # Will be set if runner supports cancellation
    }
    await update_request_context(request, task_details=task_details)

    try:
        # 1. Check if script exists and user has permission
        script_info_dict = await script_runner.get_script_info(script_name)

        if not script_info_dict:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Script '{script_name}' not found.",
            )

        # Convert to ScriptInfo model for validation
        try:
            script_info = ScriptInfo(**script_info_dict)
        except Exception as e:
            log.error("Failed to parse script info for '{}': {}", script_name, repr(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Invalid script configuration for '{script_name}'",
            )

        if script_info.metadata.requires_admin and not current_user.is_admin:
            raise ForbiddenException(  # Use specific ForbiddenException
                "Admin privileges required to run this script."
            )  # Middleware will log as 403

        # Get custom timeout if provided in parameters
        timeout_seconds = None
        if "timeout_seconds" in script_request.parameters:
            try:
                timeout_seconds = int(script_request.parameters["timeout_seconds"])
                if timeout_seconds <= 0:
                    raise ValueError("Timeout must be positive")
                task_details["timeout_seconds"] = timeout_seconds
                await update_request_context(request, task_details=task_details)
                log.info(
                    f"Using custom timeout of {timeout_seconds}s for script execution"
                )
            except (ValueError, TypeError) as e:
                log.warning(f"Invalid timeout_seconds parameter: {e}")
                # Continue with default timeout

        # 2. Check connection manager initialization status
        try:
            initialization_status = await check_connection_managers_initialization()
            task_details["connection_managers_status"] = initialization_status
            await update_request_context(request, task_details=task_details)

            # Log connection manager status
            initialized_count = sum(
                1 for status in initialization_status.values() if status
            )
            total_count = len(initialization_status)
            log.info(
                f"ReqID: {str(request_id)[:8]} - Connection managers status: {initialized_count}/{total_count} initialized"
            )

            # Check if any critical connection managers failed to initialize
            failed_managers = [
                name for name, status in initialization_status.items() if not status
            ]
            if failed_managers:
                log.warning(
                    f"ReqID: {str(request_id)[:8]} - Failed connection managers: {failed_managers}"
                )
                task_details["failed_connection_managers"] = failed_managers
                await update_request_context(request, task_details=task_details)

        except Exception as e:
            log.error(
                f"ReqID: {str(request_id)[:8]} - Failed to check connection manager initialization: {e}",
                exc_info=True,
            )
            # Continue execution but log the failure
            task_details["connection_managers_check_failed"] = str(e)
            await update_request_context(request, task_details=task_details)

        # 3. Prepare connection managers for script injection
        try:
            script_connection_managers = prepare_connection_managers_for_injection(
                connection_managers_dict
            )
            if script_connection_managers:
                log.info(
                    f"ReqID: {str(request_id)[:8]} - Connection managers prepared for script injection"
                )
                task_details["connection_managers_injected"] = True
            else:
                log.warning(
                    f"ReqID: {str(request_id)[:8]} - No connection managers available for script injection"
                )
                task_details["connection_managers_injected"] = False

            await update_request_context(request, task_details=task_details)

        except Exception as e:
            log.error(
                f"ReqID: {str(request_id)[:8]} - Failed to prepare connection managers for injection: {e}",
                exc_info=True,
            )
            # Set connection managers to None and continue
            script_connection_managers = None
            task_details["connection_managers_injected"] = False
            task_details["connection_managers_preparation_failed"] = str(e)
            await update_request_context(request, task_details=task_details)

        # 4. Run the script with timeout and connection managers
        log.info(
            f"ReqID: {str(request_id)[:8]} - Running script '{script_name}' with parameters: {script_request.parameters}"
        )
        script_result: ScriptResponse = await script_runner.run_script(
            script_name=script_name,
            params=script_request.parameters,
            timeout_seconds=timeout_seconds,
            connection_managers=script_connection_managers,
            # Pass context if runner uses it:
            # request_id=request_id,
            # username=current_user.username,
        )

        # Update task details with execution time
        task_details["end_time"] = datetime.now().isoformat()
        task_details["execution_time_seconds"] = script_result.execution_time
        await update_request_context(request, task_details=task_details)

        # 3. Check script-specific errors reported in the response model
        if script_result.error:
            # Treat script-reported error as a client-side error (400)
            log.warning(
                f"ReqID: {str(request_id)[:8]} - Script '{script_name}' reported error: {script_result.error}"
            )

            # Extract error details if available
            error_response = {
                "detail": f"Script execution failed: {script_result.error}",
                "script_name": script_name,
                "request_id": str(request_id),
            }

            # Add error type and details if available in the result
            if hasattr(script_result.result, "get") and callable(
                script_result.result.get
            ):
                error_type = script_result.result.get("error_type")
                error_details = script_result.result.get("error_details")

                if error_type:
                    error_response["error_type"] = error_type
                if error_details:
                    error_response["error_details"] = error_details

            # Update task details with error information
            task_details["error"] = script_result.error
            task_details["error_type"] = error_response.get("error_type")
            await update_request_context(request, task_details=task_details)

            # Raise HTTPException so middleware logs it correctly as completed with 400
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_response,
            )

        # If successful, middleware logs completion with 200 OK
        # Update task details with file generation info if applicable
        final_details = task_details.copy()

        # Add file generation info for scripts that produce files
        if hasattr(script_result, "result_file_id") and script_result.result_file_id:
            final_details["file_generated"] = True
            final_details["result_file_id"] = script_result.result_file_id
            final_details["download_url"] = getattr(script_result, "download_url", None)

            # Add additional tracking for assortment optimizer script
            if script_name == "assortment_optimizer":
                file_info = temp_file_manager.get_file_info(
                    script_result.result_file_id
                )
                if file_info:
                    final_details["file_info"] = {
                        "filename": file_info["filename"],
                        "size_bytes": file_info["size_bytes"],
                        "created_time": file_info["created_time"].isoformat()
                        if hasattr(file_info["created_time"], "isoformat")
                        else str(file_info["created_time"]),
                        "file_type": file_info["filename"].split(".")[-1]
                        if "." in file_info["filename"]
                        else "unknown",
                    }

            log.info(
                f"ReqID: {str(request_id)[:8]} - Script '{script_name}' generated file: {script_result.result_file_id}"
            )
        else:
            final_details["file_generated"] = False

        # Add result preview for monitoring
        if script_result.result:
            final_details["result_preview"] = str(script_result.result)[:200]

        await update_request_context(request, task_details=final_details)

        # Debug: Log what we're returning
        log.info(
            f"ReqID: {str(request_id)[:8]} - Returning script result type: {type(script_result)}"
        )
        log.info(
            f"ReqID: {str(request_id)[:8]} - Script result has result_file_id: {hasattr(script_result, 'result_file_id')}"
        )
        if hasattr(script_result, "result_file_id"):
            log.info(
                f"ReqID: {str(request_id)[:8]} - result_file_id value: {script_result.result_file_id}"
            )
        if hasattr(script_result, "download_url"):
            log.info(
                f"ReqID: {str(request_id)[:8]} - download_url value: {script_result.download_url}"
            )

        # Return the result from the script runner (must match ScriptResponse model)
        return script_result

    except ConnectionPoolExhaustedException as e:
        # Handle connection pool exhaustion specifically
        log.error(
            f"ReqID: {str(request_id)[:8]} - Script execution failed due to connection pool exhaustion: {e}",
            exc_info=True,
        )
        task_details["connection_pool_exhausted"] = str(e)
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "error": "Script execution failed due to connection pool exhaustion",
                "error_type": "connection_pool_exhausted",
                "details": "All database connections are currently in use. Please try again later.",
                "script_name": script_name,
                "request_id": str(request_id),
            },
        )
    except ConnectionTimeoutException as e:
        # Handle connection timeout specifically
        log.error(
            f"ReqID: {str(request_id)[:8]} - Script execution failed due to connection timeout: {e}",
            exc_info=True,
        )
        task_details["connection_timeout"] = str(e)
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail={
                "error": "Script execution failed due to database connection timeout",
                "error_type": "connection_timeout",
                "details": str(e),
                "script_name": script_name,
                "request_id": str(request_id),
            },
        )
    except TransientConnectionException as e:
        # Handle transient connection failures
        log.error(
            f"ReqID: {str(request_id)[:8]} - Script execution failed due to transient connection error: {e}",
            exc_info=True,
        )
        task_details["transient_connection_error"] = str(e)
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail={
                "error": "Script execution failed due to temporary database connection issues",
                "error_type": "transient_connection_failure",
                "details": "Database connection temporarily unavailable. Please retry in a few moments.",
                "script_name": script_name,
                "request_id": str(request_id),
            },
        )
    except ConnectionPoolException as e:
        # Handle general connection pool errors
        log.error(
            f"ReqID: {str(request_id)[:8]} - Script execution failed due to connection pool error: {e}",
            exc_info=True,
        )
        task_details["connection_pool_error"] = str(e)
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Script execution failed due to database connection pool issues",
                "error_type": "connection_pool_failure",
                "details": str(e),
                "script_name": script_name,
                "request_id": str(request_id),
            },
        )
    except (ClickHouseException, PostgresException) as e:
        # Handle database-specific errors
        db_type = "ClickHouse" if isinstance(e, ClickHouseException) else "PostgreSQL"
        log.error(
            f"ReqID: {str(request_id)[:8]} - Script execution failed due to {db_type} error: {e}",
            exc_info=True,
        )
        task_details[f"{db_type.lower()}_error"] = str(e)
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": f"Script execution failed due to {db_type} database error",
                "error_type": f"{db_type.lower()}_failure",
                "details": str(e),
                "script_name": script_name,
                "request_id": str(request_id),
            },
        )
    except ScriptExecutionException as e:
        # Runner raised an exception during execution (likely 500 error)
        # Check if this is a connection manager related error
        error_str = str(e).lower()
        if any(
            keyword in error_str
            for keyword in ["connection", "pool", "database", "clickhouse", "postgres"]
        ):
            log.error(
                f"ReqID: {str(request_id)[:8]} - Script execution failed due to connection manager error: {e}",
                exc_info=True,
            )
            # Update task details with connection error information
            task_details["connection_manager_error"] = str(e)
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "Script execution failed due to database connection issues",
                    "error_type": "connection_manager_failure",
                    "details": str(e),
                    "script_name": script_name,
                    "request_id": str(request_id),
                },
            )
        else:
            # Regular script execution error
            log.error(
                f"ReqID: {str(request_id)[:8]} - Script execution runtime error: {e}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,  # Treat runtime errors as 500
                detail=str(e),
            )
    except ForbiddenException as e:
        # Let the specific ForbiddenException propagate for correct 403 response
        raise e
    except HTTPException as e:
        # Re-raise known HTTPExceptions (like 404 Not Found from get_script_info)
        raise e
    except Exception as e:
        # Catch any other unexpected errors (likely 500)
        # Check if this might be a connection manager related error
        error_str = str(e).lower()
        if any(
            keyword in error_str
            for keyword in ["connection", "pool", "database", "clickhouse", "postgres"]
        ):
            log.error(
                f"ReqID: {str(request_id)[:8]} - Unexpected connection manager error running script '{script_name}': {e}",
                exc_info=True,
            )
            # Update task details with connection error information
            task_details["unexpected_connection_error"] = str(e)
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "Script execution failed due to unexpected database connection issues",
                    "error_type": "unexpected_connection_failure",
                    "details": str(e),
                    "script_name": script_name,
                    "request_id": str(request_id),
                },
            )
        else:
            # Regular unexpected error
            log.error(
                f"ReqID: {str(request_id)[:8]} - Unexpected error running script '{script_name}': {e}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred while running the script: {e}",
            )


@router.post("/pg-to-clickhouse", response_model=PgToClickHouseConversionResponse)
async def convert_pg_to_clickhouse(
    request: Request,
    conversion_request: PgToClickHouseConversionRequest,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
) -> Any:
    """Convert PostgreSQL object DDL to ClickHouse SQL.

    Supports different output formats:
    - normal (or empty): Complete SQL with both CTE and queries parts
    - cte_only: Only the CTE part of the SQL
    - queries_only: Only the queries part of the SQL
    - queries_split: Returns a JSON array/list of all queries (split by UNION ALL)
    - combined: Both CTE and queries parts combined
    - click_app: Returns a dictionary with 'cte' and 'queries' keys for proper separation

    """
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "task_type": "pg_to_clickhouse_conversion",
        "object_name": conversion_request.object_name,
        "id_panel": conversion_request.id_panel,
        "output_mode": conversion_request.output_mode,
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Get PostgreSQL object DDL with custom timeout if provided
        custom_timeout = conversion_request.timeout_seconds
        if custom_timeout:
            log.info(
                f"Using custom timeout of {custom_timeout}s for pg-to-clickhouse conversion"
            )
            task_details["custom_timeout"] = custom_timeout
            await update_request_context(request, task_details=task_details)

        pg_object = await PostgresHandler.get_object_description(
            object_name=conversion_request.object_name,
            object_type=None,  # Removed object_type parameter
            request_id=request_id,  # Pass request_id for tracking
            timeout_seconds=custom_timeout,  # Pass custom timeout if provided
        )

        # Get the DDL string
        ddl = pg_object.get("ddl", "")
        if not ddl or ddl.startswith("-- DDL not available"):
            raise PostgresException(
                f"DDL not available for {conversion_request.object_name}"
            )

        # Get the output mode and axis type
        output_mode = conversion_request.output_mode.lower()
        axis_type = get_axis_type_from_view_name(conversion_request.object_name)
        log.info(f"Using output mode: {output_mode}")

        # Get column mapping from database
        raw_column_mapping = await get_formatted_column_mapping()

        # Convert the formatted mapping to a simple dict for pg_parser
        # The pg_parser library expects a flat dictionary, not a nested one
        column_mapping = {}
        if isinstance(raw_column_mapping, dict) and "global" in raw_column_mapping:
            column_mapping = raw_column_mapping["global"]

        # Get table mapping from settings, with id_panel if provided
        id_panel = conversion_request.id_panel
        try:
            if output_mode not in ["queries_only", "queries_split"]:
                table_mapping = (
                    {"art_product_groups_pet": f"pet.purchases_{id_panel or 1}"}
                    if axis_type in ("axsm", "fltm")
                    else settings.get_table_mapping(id_panel)
                )
            else:
                table_mapping = {
                    "cte_table": f"pet.purchases_{id_panel or 1}"
                    if axis_type in ("axsm", "fltm")
                    else "pet.household"
                    if axis_type == "axsh"
                    else "pet.ctlg_article_plus"
                    if axis_type == "axsa"
                    else "",
                    **settings.get_table_mapping(id_panel),
                }
        except Exception as e:
            log.error("Error getting table mapping: {}", repr(e), exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting table mapping: {e}",
            )

        # Add the id_panel to the task details for tracking if provided
        if id_panel:
            task_details["id_panel"] = id_panel
            await update_request_context(request, task_details=task_details)
            log.info(f"Using table mapping with id_panel={id_panel}: {table_mapping}")
        else:
            log.info(f"Using default table mapping: {table_mapping}")

        # Process based on output mode
        # Initialize the parser with the PostgreSQL DDL, table mapping, and column mapping
        parser = PgAstParser(
            sql_string=ddl,
            pretty_format=False,
            dialect="clickhouse",  # Use ClickHouse dialect for output
            table_mapping=table_mapping,
            column_mapping=column_mapping,
            parenthesize_where=True,
        )

        # Process the SQL and get the result
        parser_result = parser.process()

        # Extract CTE and query parts
        cte_sql = parser_result.get("cte")
        query_parts = parser_result.get("queries", [])

        # Join query parts if there are multiple (e.g., from UNION ALL)
        queries_sql = query_parts[0] if query_parts else ""
        for i in range(1, len(query_parts)):
            queries_sql += f"\nUNION ALL\n{query_parts[i]}"

        if output_mode == "cte_only" and cte_sql:
            # Return only the CTE part
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=cte_sql,
                output_mode="cte_only",
                status="completed",
            )
        elif output_mode == "queries_only" and queries_sql:
            # Return only the queries part
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=queries_sql,
                output_mode="queries_only",
                status="completed",
            )
        elif output_mode == "queries_split" and query_parts:
            # Return list of all queries (split by UNION ALL) as JSON array/list
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=query_parts,  # Return the actual list of queries
                output_mode="queries_split",
                status="completed",
            )
        elif output_mode == "click_app":
            # Return dictionary with CTE and queries
            result_dict = {"cte": cte_sql, "queries": query_parts}
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=result_dict,
                output_mode="click_app",
                status="completed",
            )
        elif output_mode == "combined" and cte_sql and queries_sql:
            # Return both CTE and queries parts combined
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=f"WITH {cte_sql}\n\n{queries_sql}",
                output_mode="combined",
                status="completed",
            )
        else:  # Normal mode
            # For normal mode, we'll create a complete view statement with CTE and queries parts combined
            view_name = "pet." + conversion_request.object_name.split(".")[-1]

            try:
                # Generate the complete view SQL with CTE and queries parts combined
                # Combine CTE and query parts into a CREATE VIEW statement
                if cte_sql and queries_sql:
                    clickhouse_sql = (
                        f"CREATE VIEW {view_name} AS\nWITH {cte_sql}\n\n{queries_sql}"
                    )
                elif queries_sql:
                    clickhouse_sql = f"CREATE VIEW {view_name} AS\n{queries_sql}"
                else:
                    # Handle the case where conversion failed
                    raise Exception("Failed to convert PostgreSQL SQL to ClickHouse")

                # No formatting needed
            except Exception as e:
                log.error("Error generating ClickHouse view SQL: {}", repr(e), exc_info=True)
                # Just use the raw parts without the CREATE VIEW wrapper
                clickhouse_sql = (
                    f"{cte_sql}\n\n{queries_sql}" if cte_sql else queries_sql
                )

            # Return the result
            return PgToClickHouseConversionResponse(
                request_id=request_id,
                original_ddl=ddl,
                clickhouse_sql=clickhouse_sql,
                output_mode="normal",  # Return combined CTE and queries parts
                status="completed",
            )
    except PostgresException as e:
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error converting PostgreSQL to ClickHouse: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to convert PostgreSQL to ClickHouse: {e}",
        )


@router.post("/pg-to-ch-view-checker", response_model=PgToChViewCheckerResponse)
async def check_pg_to_ch_view(
    request: Request,
    checker_request: PgToChViewCheckerRequest,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
) -> Any:
    """Check conversion from PostgreSQL view to ClickHouse view."""
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "task_type": "pg_to_ch_view_checker",
        "pg_view_name": checker_request.pg_view_name,
        "id_panel": checker_request.id_panel,  # Store the id_panel if provided
        "query_ids": [],  # Will store all query IDs sent to databases
        "last_update": datetime.now().isoformat(),  # Track when we last updated
    }
    await update_request_context(request, task_details=task_details)

    # Create a function to update task details during execution
    async def update_task_details():
        # Only update if at least 1 second has passed since last update
        current_time = datetime.now()
        last_update = datetime.fromisoformat(task_details["last_update"])
        if (current_time - last_update).total_seconds() >= 1:
            task_details["last_update"] = current_time.isoformat()
            await update_request_context(request, task_details=task_details)
            log.debug(
                f"ReqID: {str(request_id)[:8]} - Updated task details with {len(task_details.get('query_ids', []))} query IDs"
            )

    try:
        # Call the view checker function
        result = await check_pg_to_ch_view_conversion(
            pg_view_name=checker_request.pg_view_name,
            id_panel=checker_request.id_panel,
            request_id=request_id,
            timeout_seconds=checker_request.timeout_seconds,
            task_details=task_details,  # Pass task_details to track query IDs
            update_task_details_callback=update_task_details,  # Pass the callback function
        )

        # Update the request context with the final task_details (including all query IDs)
        await update_request_context(request, task_details=task_details)

        # Log the tracked query IDs
        if "query_ids" in task_details and task_details["query_ids"]:
            log.info(
                f"ReqID: {str(request_id)[:8]} - Tracked query IDs: {', '.join(task_details['query_ids'])}"
            )

        # Return the result
        return PgToChViewCheckerResponse(
            request_id=request_id,
            pg_view_name=result["pg_view_name"],
            id_panel=result["id_panel"] if "id_panel" in result else 1,
            pg_row_count=result["pg_row_count"],
            ch_row_count=result["ch_row_count"],
            row_count_match=result["row_count_match"],
            status=result["status"],
            error=result["error"],
        )
    except PostgresException as e:
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error checking PostgreSQL to ClickHouse view: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check PostgreSQL to ClickHouse view: {e}",
        )


@router.get(
    "/export/job/{job_id}",
    include_in_schema=True,
    summary="Export job data with query parameters",
    description="Export data for a specific job using query parameters",
    responses={
        200: {
            "description": "Export completed successfully",
            "content": {
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary Excel file content",
                },
                "application/zip": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary ZIP file content (compressed CSV)",
                },
                "application/octet-stream": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary Parquet file content",
                },
            },
            "headers": {
                "Content-Disposition": {
                    "description": "Attachment filename for download",
                    "schema": {"type": "string"},
                    "example": "attachment; filename=job_123_export.xlsx",
                },
                "Content-Length": {
                    "description": "Size of the file in bytes",
                    "schema": {"type": "integer"},
                    "example": 2048576,
                },
            },
        },
        400: {
            "description": "Bad request - invalid parameters or Excel limits exceeded",
            "content": {
                "application/json": {
                    "examples": {
                        "excel_limit_exceeded": {
                            "summary": "Excel Limit Exceeded",
                            "value": {
                                "error": "excel_limit_exceeded",
                                "message": "Dataset exceeds Excel row limit of 1,048,576 rows",
                                "total_rows": 2000000,
                                "excel_limit": 1048576,
                                "suggestions": [
                                    "Use CSV or Parquet format for large datasets",
                                    "Enable period separation to split data across sheets",
                                    "Filter data to reduce row count"
                                ],
                                "request_id": "12345678-1234-1234-1234-123456789abc"
                            }
                        },
                        "invalid_parameters": {
                            "summary": "Invalid Parameters",
                            "value": {
                                "error": "validation_error",
                                "message": "Invalid export parameters",
                                "details": "timeout_seconds must be between 1 and 3600",
                                "request_id": "12345678-1234-1234-1234-123456789abc"
                            }
                        }
                    }
                }
            },
        },
        404: {
            "description": "Job not found or metadata unavailable",
            "content": {
                "application/json": {
                    "example": {
                        "error": "job_not_found",
                        "message": "Job 123 not found or metadata unavailable",
                        "details": "No data found for the specified job ID",
                        "request_id": "12345678-1234-1234-1234-123456789abc"
                    }
                }
            },
        },
        500: {
            "description": "Internal server error during export processing",
            "content": {
                "application/json": {
                    "example": {
                        "error": "export_processing_error",
                        "message": "Failed to process export request",
                        "recovery_suggestions": [
                            "Retry the export request",
                            "Try a different export format",
                            "Contact support if the issue persists"
                        ],
                        "request_id": "12345678-1234-1234-1234-123456789abc"
                    }
                }
            },
        },
        503: {
            "description": "Service unavailable - database connection issues",
            "content": {
                "application/json": {
                    "example": {
                        "error": "database_unavailable",
                        "message": "ClickHouse connection manager not available",
                        "details": "The ClickHouse database connection is currently unavailable. Please try again later.",
                        "recovery_suggestions": [
                            "Wait a few moments and retry the request",
                            "Check system status for database connectivity issues",
                            "Contact support if the issue persists"
                        ],
                        "request_id": "12345678-1234-1234-1234-123456789abc"
                    }
                }
            },
        },
    },
)
async def export_job_data(
    job_id: int,
    request: Request,
    format: ExportFormat = Query(ExportFormat.EXCEL, description="Export format"),
    separate_periods: bool = Query(
        False,
        description="Create separate sheets/files for each period",
    ),
    horizontal_facts: bool = Query(
        False,
        description="Use horizontal facts layout (facts as columns) for Excel exports",
    ),
    excel_layout: Optional[ExcelLayout] = Query(
        None,
        description="Excel layout option (vertical or horizontal). If not specified, determined by horizontal_facts parameter",
    ),
    compression: bool = Query(
        True,
        description="Enable compression for supported formats (ZIP for CSV)",
    ),
    timeout_seconds: Optional[int] = Query(
        None,
        ge=1,
        le=3600,
        description="Custom timeout in seconds (1-3600 seconds)",
    ),
    request_id: uuid.UUID = Depends(track_request_details),
    connection_managers_dict: Optional[Dict[str, Any]] = Depends(get_connection_managers),
    # Removed authentication dependency
) -> StreamingResponse:
    """
    Export data for the latest execution of a specific job from ClickHouse.

    This endpoint retrieves data from the most recent execution of the specified job_id
    (based on created_at timestamp) from the kpi_results database using the enhanced
    export system with metadata-driven optimization and streaming capabilities.

    ## Export Formats

    - **CSV**: Comma-separated values with optional ZIP compression
    - **Excel**: Excel workbook with support for vertical and horizontal layouts
    - **Parquet**: Columnar format optimized for analytics

    ## Layout Options

    - **Vertical Layout**: Data as stored in database (default)
    - **Horizontal Layout**: Facts as columns for easier analysis

    ## Features

    - **Metadata Analysis**: Automatic optimization based on dataset characteristics
    - **Streaming Processing**: Memory-efficient handling of large datasets
    - **Period Separation**: Split data into separate sheets/files by period
    - **Excel Limit Validation**: Automatic validation against Excel row limits
    - **Compression**: ZIP compression for CSV exports

    ## Parameters

    - `job_id`: ID of the job to export data for
    - `format`: Export format (csv, excel, parquet)
    - `separate_periods`: Create separate sheets/files for each period
    - `horizontal_facts`: Use horizontal facts layout for Excel exports
    - `excel_layout`: Explicit Excel layout option (overrides horizontal_facts)
    - `compression`: Enable compression for supported formats
    - `timeout_seconds`: Custom timeout for export processing

    ## Error Handling

    The endpoint provides detailed error responses for various scenarios:
    - **400 Bad Request**: Invalid parameters or Excel limits exceeded
    - **404 Not Found**: Job not found or metadata unavailable
    - **500 Internal Server Error**: Processing errors with recovery suggestions

    This endpoint does not require authentication.

    Returns:
        StreamingResponse with the exported data
    """
    # Determine excel_layout from parameters
    if excel_layout is None:
        excel_layout = ExcelLayout.HORIZONTAL if horizontal_facts else ExcelLayout.VERTICAL
    
    # Store task details without username (anonymous access)
    task_details = {
        "task_type": "job_data_export",
        "job_id": job_id,
        "export_format": format.value,
        "separate_periods": separate_periods,
        "horizontal_facts": horizontal_facts,
        "excel_layout": excel_layout.value,
        "compression": compression,
        "timeout_seconds": timeout_seconds,
        "username": "anonymous",  # Mark as anonymous access
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Import the ExportOrchestrator and related models
        from magic_gateway.export.services.export_orchestrator import ExportOrchestrator
        from magic_gateway.export.models import (
            ExportFormat as ExportFormatModel,
            ExportOptions,
            ExcelLayout as ExcelLayoutModel
        )
        from magic_gateway.export.exceptions import (
            ExportError,
            ExcelLimitExceededError,
            ExportValidationError
        )
        
        log.info(f"Starting enhanced export for job {job_id} in {format.value} format (request {str(request_id)[:8]})")
        
        # Get ClickHouse connection manager (try primary first, then cluster)
        clickhouse_manager = None
        if connection_managers_dict:
            # Try to get primary ClickHouse connection manager first
            if 'clickhouse_primary' in connection_managers_dict:
                clickhouse_manager = connection_managers_dict['clickhouse_primary']
                log.debug(f"Using clickhouse_primary connection manager for export")
            elif 'clickhouse_cluster' in connection_managers_dict:
                clickhouse_manager = connection_managers_dict['clickhouse_cluster']
                log.debug(f"Using clickhouse_cluster connection manager for export")
            elif 'clickhouse' in connection_managers_dict:
                clickhouse_manager = connection_managers_dict['clickhouse']
                log.debug(f"Using clickhouse connection manager for export")
        
        if not clickhouse_manager:
            available_managers = list(connection_managers_dict.keys()) if connection_managers_dict else []
            log.error(f"No ClickHouse connection manager found. Available managers: {available_managers}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "error": "database_unavailable",
                    "message": "ClickHouse connection manager not available",
                    "details": f"No ClickHouse connection manager found. Available managers: {available_managers}",
                    "recovery_suggestions": [
                        "Wait a few moments and retry the request",
                        "Check system status for database connectivity issues",
                        "Verify ClickHouse connection pool configuration",
                        "Contact support if the issue persists"
                    ],
                    "request_id": str(request_id)
                }
            )
        
        # Create export orchestrator directly with the connection manager
        export_orchestrator = ExportOrchestrator(clickhouse_manager)
        
        # Map API format to export model format
        format_mapping = {
            ExportFormat.CSV: ExportFormatModel.CSV,
            ExportFormat.EXCEL: ExportFormatModel.EXCEL,
            ExportFormat.PARQUET: ExportFormatModel.PARQUET
        }
        
        # Map API excel layout to export model layout
        layout_mapping = {
            ExcelLayout.VERTICAL: ExcelLayoutModel.VERTICAL,
            ExcelLayout.HORIZONTAL: ExcelLayoutModel.HORIZONTAL
        }
        
        export_format = format_mapping[format]
        export_excel_layout = layout_mapping[excel_layout]
        
        # Create export options
        export_options = ExportOptions(
            format=export_format,
            separate_periods=separate_periods,
            horizontal_facts=horizontal_facts,
            excel_layout=export_excel_layout,
            compression=compression,
            timeout_seconds=timeout_seconds
        )
        
        # Execute the export using the orchestrator
        response = await export_orchestrator.export_job_data(
            job_id=job_id,
            format=export_format,
            options=export_options,
            request_id=str(request_id)
        )
        
        # Update task details with export completion
        task_details["status"] = "completed"
        await update_request_context(request, task_details=task_details)
        
        log.info(f"Successfully completed enhanced export for job {job_id} (request {str(request_id)[:8]})")
        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is (these come from the orchestrator with proper error details)
        raise
    except ExcelLimitExceededError as e:
        # Handle Excel limit exceeded errors specifically
        log.warning(
            "ReqID: {} - Excel limit exceeded for job {}: {}",
            str(request_id)[:8],
            job_id,
            e.message
        )

        # Update task details with error status
        task_details["status"] = "failed"
        task_details["error"] = e.message
        task_details["error_type"] = "ExcelLimitExceededError"
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "excel_limit_exceeded",
                "message": e.message,
                "total_rows": e.total_rows,
                "excel_limit": e.excel_limit,
                "suggestions": e.recovery_suggestions,
                "request_id": str(request_id)
            }
        )
    except ExportValidationError as e:
        # Handle export validation errors specifically
        log.warning(
            "ReqID: {} - Export validation error for job {}: {}",
            str(request_id)[:8],
            job_id,
            e.message
        )

        # Update task details with error status
        task_details["status"] = "failed"
        task_details["error"] = e.message
        task_details["error_type"] = "ExportValidationError"
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "export_validation_failed",
                "message": e.message,
                "details": e.context,
                "suggestions": e.recovery_suggestions,
                "request_id": str(request_id)
            }
        )
    except ExportError as e:
        # Handle other export errors
        log.error(
            "ReqID: {} - Export error for job {}: {}",
            str(request_id)[:8],
            job_id,
            e.message
        )

        # Update task details with error status
        task_details["status"] = "failed"
        task_details["error"] = e.message
        task_details["error_type"] = e.error_type
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": e.error_type,
                "message": e.message,
                "details": e.context,
                "suggestions": e.recovery_suggestions,
                "request_id": str(request_id)
            }
        )
    except Exception as e:
        # Check if this is an export-related error that should be handled more specifically
        error_str = str(e).lower()

        # Handle Excel limit exceeded errors specifically
        if ("excel_limit_exceeded" in error_str or
            "exceeding excel limit" in error_str or
            "excel row limit" in error_str or
            "excellimitexceedederror" in error_str):
            log.warning(
                "ReqID: {} - Excel limit exceeded for job {}: {}",
                str(request_id)[:8],
                job_id,
                str(e)
            )

            # Update task details with error status
            task_details["status"] = "failed"
            task_details["error"] = "Excel row limit exceeded"
            task_details["error_type"] = "ExcelLimitExceededError"
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "excel_limit_exceeded",
                    "message": "Dataset exceeds Excel row limit",
                    "details": str(e),
                    "recovery_suggestions": [
                        "Use CSV or Parquet format for large datasets",
                        "Enable period separation to split data across multiple sheets",
                        "Filter the data to reduce the number of rows",
                        "Consider using horizontal facts layout which may reduce row count"
                    ],
                    "request_id": str(request_id)
                }
            )

        # Handle other export validation errors
        elif any(keyword in error_str for keyword in ["validation failed", "limit exceeded", "format not supported"]):
            log.warning(
                "ReqID: {} - Export validation error for job {}: {}",
                str(request_id)[:8],
                job_id,
                str(e)
            )

            # Update task details with error status
            task_details["status"] = "failed"
            task_details["error"] = "Export validation failed"
            task_details["error_type"] = "ExportValidationError"
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "export_validation_failed",
                    "message": "Export validation failed",
                    "details": str(e),
                    "recovery_suggestions": [
                        "Check export parameters and format",
                        "Try a different export format",
                        "Enable period separation for large datasets",
                        "Contact support if the issue persists"
                    ],
                    "request_id": str(request_id)
                }
            )

        # Handle unexpected errors with enhanced error context
        else:
            log.error(
                "ReqID: {} - Unexpected error in enhanced export for job {}: {}",
                str(request_id)[:8],
                job_id,
                repr(e),
                exc_info=True,
            )

            # Update task details with error status
            task_details["status"] = "failed"
            task_details["error"] = str(e)
            task_details["error_type"] = type(e).__name__
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred during export processing",
                    "details": str(e),
                    "recovery_suggestions": [
                        "Retry the export request",
                        "Try a different export format",
                        "Enable period separation for large datasets",
                        "Contact support if the issue persists"
                    ],
                    "request_id": str(request_id)
                }
            )


@router.post(
    "/export/job/{job_id}",
    include_in_schema=True,
    summary="Export job data with request body",
    description="Export data for a specific job using request body parameters",
    responses={
        200: {
            "description": "Export completed successfully",
            "content": {
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary Excel file content",
                },
                "application/zip": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary ZIP file content (compressed CSV)",
                },
                "application/octet-stream": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary Parquet file content",
                },
            },
            "headers": {
                "Content-Disposition": {
                    "description": "Attachment filename for download",
                    "schema": {"type": "string"},
                    "example": "attachment; filename=job_123_export.xlsx",
                },
                "Content-Length": {
                    "description": "Size of the file in bytes",
                    "schema": {"type": "integer"},
                    "example": 2048576,
                },
            },
        },
        400: {
            "description": "Bad request - invalid parameters or Excel limits exceeded",
            "content": {
                "application/json": {
                    "examples": {
                        "excel_limit_exceeded": {
                            "summary": "Excel Limit Exceeded",
                            "value": {
                                "error": "excel_limit_exceeded",
                                "message": "Dataset exceeds Excel row limit of 1,048,576 rows",
                                "total_rows": 2000000,
                                "excel_limit": 1048576,
                                "suggestions": [
                                    "Use CSV or Parquet format for large datasets",
                                    "Enable period separation to split data across sheets",
                                    "Filter data to reduce row count"
                                ],
                                "request_id": "12345678-1234-1234-1234-123456789abc"
                            }
                        },
                        "validation_error": {
                            "summary": "Request Validation Error",
                            "value": {
                                "error": "validation_error",
                                "message": "Invalid request parameters",
                                "details": [
                                    {
                                        "field": "timeout_seconds",
                                        "error": "Value must be between 1 and 3600"
                                    }
                                ],
                                "request_id": "12345678-1234-1234-1234-123456789abc"
                            }
                        }
                    }
                }
            },
        },
        404: {
            "description": "Job not found or metadata unavailable",
            "content": {
                "application/json": {
                    "example": {
                        "error": "job_not_found",
                        "message": "Job 123 not found or metadata unavailable",
                        "details": "No data found for the specified job ID",
                        "request_id": "12345678-1234-1234-1234-123456789abc"
                    }
                }
            },
        },
        500: {
            "description": "Internal server error during export processing",
            "content": {
                "application/json": {
                    "example": {
                        "error": "export_processing_error",
                        "message": "Failed to process export request",
                        "recovery_suggestions": [
                            "Retry the export request",
                            "Try a different export format",
                            "Contact support if the issue persists"
                        ],
                        "request_id": "12345678-1234-1234-1234-123456789abc"
                    }
                }
            },
        },
        503: {
            "description": "Service unavailable - database connection issues",
            "content": {
                "application/json": {
                    "example": {
                        "error": "database_unavailable",
                        "message": "ClickHouse connection manager not available",
                        "details": "The ClickHouse database connection is currently unavailable. Please try again later.",
                        "recovery_suggestions": [
                            "Wait a few moments and retry the request",
                            "Check system status for database connectivity issues",
                            "Contact support if the issue persists"
                        ],
                        "request_id": "12345678-1234-1234-1234-123456789abc"
                    }
                }
            },
        },
    },
)
async def export_job_data_post(
    job_id: int,
    export_request: ExportRequest,
    request: Request,
    request_id: uuid.UUID = Depends(track_request_details),
    connection_managers_dict: Optional[Dict[str, Any]] = Depends(get_connection_managers),
    # Removed authentication dependency
) -> StreamingResponse:
    """
    Export data for a specific job with comprehensive options (POST method).

    This endpoint provides the same functionality as the GET endpoint but accepts
    export parameters in the request body, allowing for more complex configurations
    and better parameter validation.

    ## Request Body

    The request body should contain an ExportRequest object with the following fields:

    - `format`: Export format (csv, excel, parquet)
    - `separate_periods`: Create separate sheets/files for each period
    - `horizontal_facts`: Use horizontal facts layout for Excel exports
    - `excel_layout`: Explicit Excel layout option (overrides horizontal_facts)
    - `compression`: Enable compression for supported formats
    - `timeout_seconds`: Custom timeout for export processing

    ## Response

    Returns a streaming response with the exported data file, identical to the GET endpoint.

    ## Error Handling

    Provides enhanced error responses with detailed context and recovery suggestions:

    - **400 Bad Request**: Parameter validation errors or Excel limits exceeded
    - **404 Not Found**: Job not found or metadata unavailable
    - **500 Internal Server Error**: Processing errors with recovery suggestions
    - **503 Service Unavailable**: Database connection issues

    This endpoint does not require authentication.

    Returns:
        StreamingResponse with the exported data
    """
    # Determine excel_layout from request parameters
    excel_layout = export_request.excel_layout
    if excel_layout is None:
        excel_layout = ExcelLayout.HORIZONTAL if export_request.horizontal_facts else ExcelLayout.VERTICAL
    
    # Store task details without username (anonymous access)
    task_details = {
        "task_type": "job_data_export_post",
        "job_id": job_id,
        "export_format": export_request.format.value,
        "separate_periods": export_request.separate_periods,
        "horizontal_facts": export_request.horizontal_facts,
        "excel_layout": excel_layout.value,
        "compression": export_request.compression,
        "timeout_seconds": export_request.timeout_seconds,
        "username": "anonymous",  # Mark as anonymous access
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Import the ExportOrchestrator and related models
        from magic_gateway.export.services.export_orchestrator import ExportOrchestrator
        from magic_gateway.export.models import (
            ExportFormat as ExportFormatModel,
            ExportOptions,
            ExcelLayout as ExcelLayoutModel
        )
        from magic_gateway.export.exceptions import (
            ExportError,
            ExcelLimitExceededError,
            ExportValidationError
        )
        
        log.info(f"Starting enhanced export (POST) for job {job_id} in {export_request.format.value} format (request {str(request_id)[:8]})")
        
        # Get ClickHouse connection manager (try primary first, then cluster)
        clickhouse_manager = None
        if connection_managers_dict:
            # Try to get primary ClickHouse connection manager first
            if 'clickhouse_primary' in connection_managers_dict:
                clickhouse_manager = connection_managers_dict['clickhouse_primary']
                log.debug(f"Using clickhouse_primary connection manager for export (POST)")
            elif 'clickhouse_cluster' in connection_managers_dict:
                clickhouse_manager = connection_managers_dict['clickhouse_cluster']
                log.debug(f"Using clickhouse_cluster connection manager for export (POST)")
            elif 'clickhouse' in connection_managers_dict:
                clickhouse_manager = connection_managers_dict['clickhouse']
                log.debug(f"Using clickhouse connection manager for export (POST)")
        
        if not clickhouse_manager:
            available_managers = list(connection_managers_dict.keys()) if connection_managers_dict else []
            log.error(f"No ClickHouse connection manager found (POST). Available managers: {available_managers}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "error": "database_unavailable",
                    "message": "ClickHouse connection manager not available",
                    "details": f"No ClickHouse connection manager found. Available managers: {available_managers}",
                    "recovery_suggestions": [
                        "Wait a few moments and retry the request",
                        "Check system status for database connectivity issues",
                        "Verify ClickHouse connection pool configuration",
                        "Contact support if the issue persists"
                    ],
                    "request_id": str(request_id)
                }
            )
        
        # Create export orchestrator directly with the connection manager
        export_orchestrator = ExportOrchestrator(clickhouse_manager)
        
        # Map API format to export model format
        format_mapping = {
            ExportFormat.CSV: ExportFormatModel.CSV,
            ExportFormat.EXCEL: ExportFormatModel.EXCEL,
            ExportFormat.PARQUET: ExportFormatModel.PARQUET
        }
        
        # Map API excel layout to export model layout
        layout_mapping = {
            ExcelLayout.VERTICAL: ExcelLayoutModel.VERTICAL,
            ExcelLayout.HORIZONTAL: ExcelLayoutModel.HORIZONTAL
        }
        
        export_format = format_mapping[export_request.format]
        export_excel_layout = layout_mapping[excel_layout]
        
        # Create export options
        export_options = ExportOptions(
            format=export_format,
            separate_periods=export_request.separate_periods,
            horizontal_facts=export_request.horizontal_facts,
            excel_layout=export_excel_layout,
            compression=export_request.compression,
            timeout_seconds=export_request.timeout_seconds
        )
        
        # Execute the export using the orchestrator
        response = await export_orchestrator.export_job_data(
            job_id=job_id,
            format=export_format,
            options=export_options,
            request_id=str(request_id)
        )
        
        # Update task details with export completion
        task_details["status"] = "completed"
        await update_request_context(request, task_details=task_details)
        
        log.info(f"Successfully completed enhanced export (POST) for job {job_id} (request {str(request_id)[:8]})")
        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is (these come from the orchestrator with proper error details)
        raise
    except ExcelLimitExceededError as e:
        # Handle Excel limit exceeded errors specifically
        log.warning(
            "ReqID: {} - Excel limit exceeded for job {} (POST): {}",
            str(request_id)[:8],
            job_id,
            e.message
        )

        # Update task details with error status
        task_details["status"] = "failed"
        task_details["error"] = e.message
        task_details["error_type"] = "ExcelLimitExceededError"
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "excel_limit_exceeded",
                "message": e.message,
                "total_rows": e.total_rows,
                "excel_limit": e.excel_limit,
                "suggestions": e.recovery_suggestions,
                "request_id": str(request_id)
            }
        )
    except ExportValidationError as e:
        # Handle export validation errors specifically
        log.warning(
            "ReqID: {} - Export validation error for job {} (POST): {}",
            str(request_id)[:8],
            job_id,
            e.message
        )

        # Update task details with error status
        task_details["status"] = "failed"
        task_details["error"] = e.message
        task_details["error_type"] = "ExportValidationError"
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "export_validation_failed",
                "message": e.message,
                "details": e.context,
                "suggestions": e.recovery_suggestions,
                "request_id": str(request_id)
            }
        )
    except ExportError as e:
        # Handle other export errors
        log.error(
            "ReqID: {} - Export error for job {} (POST): {}",
            str(request_id)[:8],
            job_id,
            e.message
        )

        # Update task details with error status
        task_details["status"] = "failed"
        task_details["error"] = e.message
        task_details["error_type"] = e.error_type
        await update_request_context(request, task_details=task_details)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": e.error_type,
                "message": e.message,
                "details": e.context,
                "suggestions": e.recovery_suggestions,
                "request_id": str(request_id)
            }
        )
    except Exception as e:
        # Check if this is an export-related error that should be handled more specifically
        error_str = str(e).lower()

        # Handle Excel limit exceeded errors specifically
        if ("excel_limit_exceeded" in error_str or
            "exceeding excel limit" in error_str or
            "excel row limit" in error_str or
            "excellimitexceedederror" in error_str):
            log.warning(
                "ReqID: {} - Excel limit exceeded for job {} (POST): {}",
                str(request_id)[:8],
                job_id,
                str(e)
            )

            # Update task details with error status
            task_details["status"] = "failed"
            task_details["error"] = "Excel row limit exceeded"
            task_details["error_type"] = "ExcelLimitExceededError"
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "excel_limit_exceeded",
                    "message": "Dataset exceeds Excel row limit",
                    "details": str(e),
                    "recovery_suggestions": [
                        "Use CSV or Parquet format for large datasets",
                        "Enable period separation to split data across multiple sheets",
                        "Filter the data to reduce the number of rows",
                        "Consider using horizontal facts layout which may reduce row count"
                    ],
                    "request_id": str(request_id)
                }
            )

        # Handle other export validation errors
        elif any(keyword in error_str for keyword in ["validation failed", "limit exceeded", "format not supported"]):
            log.warning(
                "ReqID: {} - Export validation error for job {} (POST): {}",
                str(request_id)[:8],
                job_id,
                str(e)
            )

            # Update task details with error status
            task_details["status"] = "failed"
            task_details["error"] = "Export validation failed"
            task_details["error_type"] = "ExportValidationError"
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "export_validation_failed",
                    "message": "Export validation failed",
                    "details": str(e),
                    "recovery_suggestions": [
                        "Check export parameters and format",
                        "Try a different export format",
                        "Enable period separation for large datasets",
                        "Contact support if the issue persists"
                    ],
                    "request_id": str(request_id)
                }
            )

        # Handle unexpected errors with enhanced error context
        else:
            log.error(
                "ReqID: {} - Unexpected error in enhanced export (POST) for job {}: {}",
                str(request_id)[:8],
                job_id,
                repr(e),
                exc_info=True,
            )

            # Update task details with error status
            task_details["status"] = "failed"
            task_details["error"] = str(e)
            task_details["error_type"] = type(e).__name__
            await update_request_context(request, task_details=task_details)

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred during export processing",
                    "details": str(e),
                    "recovery_suggestions": [
                        "Retry the export request",
                        "Try a different export format",
                        "Enable period separation for large datasets",
                        "Contact support if the issue persists"
                    ],
                    "request_id": str(request_id)
                }
            )


@router.get(
    "/download/{file_id}",
    tags=["scripts"],
    summary="Download script result file",
    description="Download a result file generated by script execution",
    responses={
        200: {
            "description": "File downloaded successfully",
            "content": {
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary Excel file content",
                },
                "text/csv": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary CSV file content",
                },
                "application/json": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary JSON file content",
                },
                "application/octet-stream": {
                    "schema": {"type": "string", "format": "binary"},
                    "example": "Binary file content",
                },
            },
            "headers": {
                "Content-Disposition": {
                    "description": "Attachment filename for download",
                    "schema": {"type": "string"},
                    "example": "attachment; filename=assortment_optimizer_results.xlsx",
                },
                "Content-Length": {
                    "description": "Size of the file in bytes",
                    "schema": {"type": "integer"},
                    "example": 1048576,
                },
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {"detail": "Not authenticated"},
                }
            },
        },
        404: {
            "description": "File not found or expired",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "examples": {
                        "file_not_found": {
                            "summary": "File Not Found",
                            "description": "The requested file does not exist or has expired",
                            "value": {
                                "detail": "File not found: ao_12345678-1234-1234-1234-123456789abc"
                            },
                        },
                        "file_info_unavailable": {
                            "summary": "File Information Unavailable",
                            "description": "File exists but metadata is not available",
                            "value": {
                                "detail": "File information not available: ao_12345678-1234-1234-1234-123456789abc"
                            },
                        },
                    },
                }
            },
        },
        500: {
            "description": "Internal server error during file download",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {
                        "detail": "Failed to download file: I/O error reading file"
                    },
                }
            },
        },
    },
)
async def download_script_result(
    request: Request,
    file_id: str,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
    _: RequestTrackingService = Depends(get_request_tracker_service),
) -> StreamingResponse:
    """
    Download a script result file.

    This endpoint streams the content of a result file generated by a script execution.
    The file is identified by a unique file_id that was returned in the script execution response.
    After successful download, the temporary file is automatically deleted to free up storage space.

    ## Authentication
    Authentication is required to download files. Users can only download files they have permission to access.

    ## File Types
    The endpoint supports various file formats:
    - **Excel files (.xlsx)**: Assortment optimization results and other structured data
    - **CSV files (.csv)**: Tabular data exports
    - **JSON files (.json)**: Structured data responses
    - **Other formats**: Generic binary file support

    ## File Lifecycle
    - Files are automatically deleted after successful download
    - Files have a 24-hour retention period before automatic cleanup
    - Each file can only be downloaded once

    ## Usage Example
    1. Execute a script that generates a file (e.g., assortment optimizer)
    2. Extract the `result_file_id` from the script response
    3. Use this endpoint with the file ID to download the result
    4. File is automatically cleaned up after download

    ## Error Handling
    - **404 Not Found**: File doesn't exist, has expired, or has already been downloaded
    - **401 Unauthorized**: Authentication required
    - **500 Internal Server Error**: System error during file access or streaming
    """
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "task_type": "script_file_download",
        "file_id": file_id,
        "endpoint": f"/api/v1/scripts/download/{file_id}",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Get file path from temp file manager
        file_path = temp_file_manager.get_file_path(file_id)

        if not file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {file_id}",
            )

        # Get file info for headers
        file_info = temp_file_manager.get_file_info(file_id)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File information not available: {file_id}",
            )

        # Determine content type based on file extension
        content_type = "application/octet-stream"
        if file_path.suffix.lower() == ".xlsx":
            content_type = (
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
        elif file_path.suffix.lower() == ".csv":
            content_type = "text/csv"
        elif file_path.suffix.lower() == ".json":
            content_type = "application/json"

        # Create file iterator for streaming
        async def file_iterator():
            try:
                with open(file_path, "rb") as file:
                    while chunk := file.read(8192):  # 8KB chunks
                        yield chunk
            finally:
                # Clean up the file after streaming
                try:
                    temp_file_manager.cleanup_file(file_id)
                    log.info(f"Cleaned up downloaded file: {file_id}")
                except Exception as cleanup_error:
                    log.error("Failed to cleanup file {}: {}", file_id, cleanup_error)

        # Update task details with file information
        task_details.update(
            {
                "filename": file_info["filename"],
                "file_size": file_info["size_bytes"],
                "content_type": content_type,
                "download_started": datetime.now(timezone.utc).isoformat(),
            }
        )
        await update_request_context(request, task_details=task_details)

        # Create streaming response
        response = StreamingResponse(
            file_iterator(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={file_info['filename']}",
                "Content-Length": str(file_info["size_bytes"]),
            },
        )

        log.info(f"Started download for file: {file_id} ({file_info['filename']})")
        return response

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error downloading file {file_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download file: {e}",
        )


@router.get(
    "/assortment-optimizer/interface",
    response_class=HTMLResponse,
    tags=["scripts"],
    summary="Assortment Optimizer Web Interface",
    description="Serve interactive HTML interface for assortment optimization",
    responses={
        200: {
            "description": "HTML interface served successfully",
            "content": {
                "text/html": {
                    "schema": {"type": "string"},
                    "example": "<!DOCTYPE html><html>...</html>",
                }
            },
        },
        500: {
            "description": "Internal server error serving interface",
            "content": {
                "application/json": {
                    "schema": {"$ref": "#/components/schemas/ErrorResponse"},
                    "example": {"detail": "Failed to serve interface: Template error"},
                }
            },
        },
    },
)
async def get_assortment_optimizer_interface(
    request: Request,
    request_id: uuid.UUID = Depends(track_request_details),
    _: RequestTrackingService = Depends(get_request_tracker_service),
) -> HTMLResponse:
    """
    Serve HTML interface for the assortment optimization script.

    This endpoint provides a modern, responsive web interface for running the assortment optimization script.
    The interface includes form fields for all required parameters and handles the script execution and result download.

    ## Features
    - **Responsive Design**: Works on desktop, tablet, and mobile devices
    - **Form Validation**: Client-side validation for all input parameters
    - **Real-time Feedback**: Loading indicators and progress status during execution
    - **Error Handling**: User-friendly error messages and validation feedback
    - **Download Integration**: Automatic download link generation upon completion

    ## Interface Components
    - **Parameter Form**: Input fields for all required assortment optimization parameters
    - **Date Pickers**: Calendar inputs for start_date and end_date
    - **Number Inputs**: Validated numeric inputs with range checking
    - **Submit Button**: Executes the optimization with loading state
    - **Results Section**: Displays execution status and download links

    ## Authentication
    - **Interface Access**: No authentication required to view the interface
    - **Script Execution**: Authentication required when submitting the form
    - **File Download**: Authentication required for downloading results

    ## Parameter Fields
    The interface includes input fields for all assortment optimization parameters:
    - `start_date`: Date picker for analysis start date
    - `end_date`: Date picker for analysis end date
    - `json_axis_id`: Numeric input for JSON axis ID
    - `json_flt_hh_id`: Optional numeric input for filter household ID
    - `total_position_number`: Required numeric input for total positions
    - `rest_position_number`: Numeric input for REST positions (-1 for no REST)
    - `id_panel`: Numeric input for panel ID

    ## Usage Flow
    1. Access this endpoint to load the HTML interface
    2. Fill in the required parameters using the form fields
    3. Submit the form to execute the assortment optimization
    4. Monitor execution progress with real-time status updates
    5. Download the Excel results file when optimization completes

    ## Technical Details
    - **Content Type**: text/html with embedded CSS and JavaScript
    - **Framework**: Vanilla HTML/CSS/JavaScript (no external dependencies)
    - **API Integration**: Uses Fetch API to communicate with script execution endpoints
    - **File Handling**: Integrates with file download endpoint for result retrieval

    Returns:
        HTMLResponse: Complete HTML page with embedded CSS and JavaScript for the assortment optimizer interface
    """
    # Store task details for tracking (no username since this is public)
    task_details = {
        "task_type": "assortment_optimizer_interface",
        "access_type": "public",
        "endpoint": "/api/v1/scripts/assortment-optimizer/interface",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "client_ip": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent", "Unknown"),
        "referer": request.headers.get("referer", "Direct access"),
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Read HTML content from separate template file using pathlib for reliable path handling
        from pathlib import Path

        current_file = Path(__file__).resolve()
        template_path = (
            current_file.parent.parent.parent.parent
            / "templates"
            / "assortment_optimizer.html"
        )

        log.info(f"Loading assortment optimizer template from: {template_path}")

        if not template_path.exists():
            log.error(f"Template file not found: {template_path}")
            log.error(f"Current file: {current_file}")
            log.error(f"Template directory exists: {template_path.parent.exists()}")
            if template_path.parent.exists():
                log.error(
                    f"Files in template directory: {list(template_path.parent.iterdir())}"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Template file not found",
            )

        with open(template_path, "r", encoding="utf-8") as f:
            html_content = f.read()

        log.info(f"Successfully loaded template ({len(html_content)} characters)")

        return HTMLResponse(content=html_content)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        log.error("Error reading template file: {}", repr(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading interface: {e}",
        )
