# Changelog

All notable changes to the Magic Gateway project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.5.5] - 2025-08-01

### Fixed
- **PostgreSQL Connection Pool Monitoring**: Fixed critical issue where active and idle connections were always showing as 0 in the `api.connection_pool_monitoring` table
  - Updated monitoring service to use correct psycopg_pool 3.2.6 stats format
  - Fixed metrics extraction to properly calculate active connections as `pool_size - pool_available`
  - Fixed idle connections calculation to use `pool_available` directly
  - Mapped other pool metrics to correct psycopg_pool keys (`requests_num`, `requests_errors`, etc.)
  - Added comprehensive documentation of psycopg_pool stats format in code
  - Connection pool monitoring now accurately reflects actual PostgreSQL pool usage

### Enhanced
- Improved connection pool monitoring accuracy and reliability
- Added detailed logging of raw pool stats for debugging purposes
- Enhanced monitoring service with proper psycopg_pool integration

## [0.5.4] - 2025-07-31

### Fixed
- Fixed bare `except` clause in config.py to use proper exception handling
- Removed unused import `JSONResponse` from auth/dependencies.py
- Fixed f-string without placeholders in auth/dependencies.py and cpaapi_client/client.py
- Improved code quality and removed linting warnings

### Enhanced
- Better error handling in configuration management
- Cleaner import statements across modules
- Improved code maintainability and readability

## [0.5.3] - 2025-07-31

### Added
- **Token Refresh Functionality**: Automatic JWT token refresh during long-running script executions
  - New `get_current_active_user_with_refresh` authentication dependency
  - `TokenRefreshMiddleware` for handling token refresh responses
  - Proactive token refresh when tokens have less than 5 minutes remaining
  - Client-side automatic token refresh handling in both sync and async clients
  - Token rotation on refresh for enhanced security
- Token refresh example script (`examples/token_refresh_example.py`)
- Comprehensive token refresh documentation (`docs/token_refresh.md`)
- Unit tests for token refresh functionality

### Enhanced
- Scripts endpoints now use enhanced authentication with automatic token refresh
- Client libraries automatically handle server-side token refresh via response headers
- Improved user experience for long-running operations (no more unexpected logouts)
- Refactored middleware architecture with clean package structure and direct imports
- Web interface now receives user-friendly error messages for expired tokens instead of technical JWT errors

### Security
- Token rotation on refresh prevents token reuse attacks
- Refresh tokens are securely transmitted in request headers
- Enhanced logging for token refresh events

### Fixed
- Web interface now shows proper "session expired" messages instead of generic authentication errors
- HTML page token expiration now handled gracefully with user-friendly login prompts
- **Web interface automatic token refresh**: HTML page now automatically refreshes expired tokens using stored refresh tokens
- **Background token refresh**: Periodic token refresh (every 5 minutes) prevents expiration during active use
- **Seamless script execution**: Long-running operations no longer fail due to token expiration
- **Server-side proactive refresh**: Web interface now sends refresh token headers for server-side proactive token refresh
- **Updated auth endpoints**: `/api/v1/auth/me` endpoint now uses smart token refresh to prevent authentication failures
- **Enhanced token checks**: Authentication status checks in HTML page now include refresh token headers
- Fixed "Login successful! Loading interface..." message appearing when tokens were still expired
- Reduced "Web interface token expiring soon" log noise by changing to DEBUG level and enabling proactive refresh

## [0.5.0] - 2025-01-28

### Added
- Version bump to 0.5.0 for new release cycle
- Consolidated version management across all configuration files

### Changed
- Updated version numbers in pyproject.toml, compose.yml, config.py, and __init__.py
- Synchronized version information across the entire project

## [0.4.9] - 2025-01-26

### Enhanced
- Improved connection pool integration for script execution
- Enhanced script runner with connection manager dependency injection
- Refactored assortment optimizer script to use connection pool instead of direct connections
- Added comprehensive error handling for connection pool operations in scripts
- Improved monitoring and logging for script connection usage

### Fixed
- Resolved unused import warnings in various modules
- Fixed connection manager initialization checks in script execution
- Improved error propagation from async to sync context in scripts
- Enhanced connection pool monitoring for script usage tracking

### Technical Improvements
- Implemented ScriptConnectionManagers dataclass for better dependency injection
- Added connection manager preparation and validation before script execution
- Enhanced script execution context with proper connection pool integration
- Improved async/sync bridge for connection operations in scripts

## [0.4.8] - 2025-01-22

### Added
- Enhanced export functionality with automatic cluster routing based on database name
- Database utility functions for cluster detection and routing
- Improved ClickHouse cluster support for export operations
- Auto-routing for job_result database exports to cluster connection pool

### Enhanced
- ClickHouse export methods now support automatic cluster detection
- Export operations maintain backward compatibility with explicit cluster parameters
- Improved logging for connection pool usage in export operations
- Enhanced error handling and fallback mechanisms for cluster connections

### Fixed
- Cluster connection fallback logic in export operations
- Database name extraction from table names for routing decisions
- Connection pool monitoring for cluster connections

## [0.4.2] - 2025-05-20

### Changed
- Bumped version number for consistency across all files

## [0.4.1] - 2025-05-12

### Added
- Added Parquet format support for data exports
- Improved metadata handling in export files

## [0.4.0] - 2025-04-26

### Added
- Implemented true streaming Excel export that starts download immediately
- Optimized Excel generation for large datasets
- Improved memory usage during export operations
- Added consistent Arial font styling for all Excel exports
- Fixed display of Panel ID and filters in Excel exports

### Changed
- Replaced in-memory Excel generation with a more efficient streaming approach
- Improved error handling during Excel generation
- Reduced server load during export operations

## [0.3.2] - 2025-04-24

### Added
- New endpoint `/api/v1/postgres/ctlg_measures/` for retrieving catalog measures
- Support for filtering measures by support status

## [0.3.1] - 2025-04-24

### Added
- Export endpoint for job data
- Support for CSV and Excel export formats
- Info sheet in Excel exports with job and result information

## [0.3.0] - 2025-04-09

### Added
- New output format "queries_split" for PostgreSQL to ClickHouse conversion
- Improved scripts module structure

### Changed
- Moved PostgreSQL to ClickHouse conversion functionality to scripts module
- Updated client structure to better reflect server API

## [0.2.2] - 2025-04-08

### Added
- New endpoint `/api/v1/postgres/axis-info` for retrieving axis information and labels
- Support for full axis names with schema (format: schema.axis_name)
- Filtering by clientdb_schema in axis queries

### Changed
- Improved error messages for axis info endpoint

## [0.2.1] - 2025-04-07

### Added
- Support for PostgreSQL to ClickHouse conversion
- Health check endpoint
- Request tracking middleware

### Fixed
- Connection pooling issues
- Authentication token refresh mechanism

## [0.2.0] - 2025-04-01

### Added
- Initial release with basic functionality
- Support for ClickHouse and PostgreSQL queries
- LDAP authentication
- Request tracking
