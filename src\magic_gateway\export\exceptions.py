"""
Exception classes for export operations.

This module defines a hierarchy of exceptions specific to the export system,
providing detailed error context and recovery suggestions.
"""

import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional


@dataclass
class ExportError(Exception):
    """
    Base exception class for all export-related errors.
    
    Provides structured error information including context and recovery suggestions.
    """
    error_type: str
    message: str
    context: Dict[str, Any]
    request_id: str
    recovery_suggestions: List[str]
    timestamp: float = field(default_factory=time.time)
    operation: Optional[str] = None
    connection_pool_status: Optional[Dict[str, Any]] = None
    
    def __str__(self) -> str:
        return f"{self.error_type}: {self.message} (Request ID: {self.request_id})"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging and API responses."""
        return {
            "error_type": self.error_type,
            "message": self.message,
            "context": self.context,
            "request_id": self.request_id,
            "recovery_suggestions": self.recovery_suggestions,
            "timestamp": self.timestamp,
            "operation": self.operation,
            "connection_pool_status": self.connection_pool_status
        }


class ConnectionPoolExhaustedError(ExportError):
    """Raised when all connections in the pool are in use."""
    
    def __init__(
        self,
        request_id: str,
        pool_name: str,
        active_connections: int,
        max_connections: int,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="connection_pool_exhausted",
            message=f"Connection pool '{pool_name}' exhausted ({active_connections}/{max_connections} connections in use)",
            context=context or {
                "pool_name": pool_name,
                "active_connections": active_connections,
                "max_connections": max_connections
            },
            request_id=request_id,
            recovery_suggestions=[
                "Retry the request after a short delay",
                "Check for long-running queries that may be holding connections",
                "Consider increasing the connection pool size if this occurs frequently"
            ]
        )


class ConnectionTimeoutError(ExportError):
    """Raised when database connection times out."""
    
    def __init__(
        self,
        request_id: str,
        timeout_seconds: float,
        operation: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="connection_timeout",
            message=f"Database connection timeout after {timeout_seconds}s during {operation}",
            context=context or {
                "timeout_seconds": timeout_seconds,
                "operation": operation
            },
            request_id=request_id,
            recovery_suggestions=[
                "Retry the request",
                "Check database server health and network connectivity",
                "Consider increasing the connection timeout if this occurs frequently"
            ]
        )


class TableNotFoundError(ExportError):
    """Raised when the job result table is not found."""
    
    def __init__(
        self,
        request_id: str,
        job_id: int,
        table_name: str,
        database_name: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="table_not_found",
            message=f"Job result table '{database_name}.{table_name}' not found for job {job_id}",
            context=context or {
                "job_id": job_id,
                "table_name": table_name,
                "database_name": database_name
            },
            request_id=request_id,
            recovery_suggestions=[
                "Verify the job ID is correct and the job has completed successfully",
                "Check if the job results have been cleaned up or expired",
                "Ensure you have permission to access the job results"
            ]
        )


class FormatConversionError(ExportError):
    """Raised when format conversion fails."""
    
    def __init__(
        self,
        request_id: str,
        source_format: str,
        target_format: str,
        error_details: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="format_conversion_failed",
            message=f"Failed to convert from {source_format} to {target_format}: {error_details}",
            context=context or {
                "source_format": source_format,
                "target_format": target_format,
                "error_details": error_details
            },
            request_id=request_id,
            recovery_suggestions=[
                "Try exporting in a different format",
                "Check if the data contains unsupported characters or structures",
                "Retry the request as this may be a transient issue"
            ]
        )


class InsufficientResourcesError(ExportError):
    """Raised when system resources are insufficient for the export operation."""
    
    def __init__(
        self,
        request_id: str,
        resource_type: str,
        required: str,
        available: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="insufficient_resources",
            message=f"Insufficient {resource_type}: required {required}, available {available}",
            context=context or {
                "resource_type": resource_type,
                "required": required,
                "available": available
            },
            request_id=request_id,
            recovery_suggestions=[
                "Retry the request when system load is lower",
                "Consider exporting smaller datasets or using pagination",
                "Contact system administrator if this persists"
            ]
        )


class JobMetadataError(ExportError):
    """Raised when job metadata cannot be retrieved or is invalid."""
    
    def __init__(
        self,
        request_id: str,
        job_id: int,
        error_details: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="job_metadata_error",
            message=f"Failed to retrieve metadata for job {job_id}: {error_details}",
            context=context or {
                "job_id": job_id,
                "error_details": error_details
            },
            request_id=request_id,
            recovery_suggestions=[
                "Verify the job ID is correct and exists",
                "Check if you have permission to access this job",
                "Ensure the job has completed successfully"
            ]
        )


class ConnectionPoolStatusError(ExportError):
    """Raised when connection pool is in an invalid state."""
    
    def __init__(
        self,
        request_id: str,
        pool_name: str,
        status_details: str,
        pool_status: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="connection_pool_status_error",
            message=f"Connection pool '{pool_name}' is in invalid state: {status_details}",
            context=context or {
                "pool_name": pool_name,
                "status_details": status_details
            },
            request_id=request_id,
            recovery_suggestions=[
                "Wait for connection pool to recover",
                "Check database server health",
                "Contact system administrator if pool remains unhealthy"
            ],
            connection_pool_status=pool_status
        )


class ResourceCleanupError(ExportError):
    """Raised when resource cleanup fails."""
    
    def __init__(
        self,
        request_id: str,
        resource_type: str,
        resource_id: str,
        cleanup_error: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="resource_cleanup_error",
            message=f"Failed to cleanup {resource_type} '{resource_id}': {cleanup_error}",
            context=context or {
                "resource_type": resource_type,
                "resource_id": resource_id,
                "cleanup_error": cleanup_error
            },
            request_id=request_id,
            recovery_suggestions=[
                "Manual cleanup may be required",
                "Check system logs for detailed error information",
                "Contact system administrator if cleanup failures persist"
            ]
        )


class ExportValidationError(ExportError):
    """Raised when export parameters or data fail validation."""
    
    def __init__(
        self,
        request_id: str,
        validation_type: str,
        validation_details: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="export_validation_error",
            message=f"Export validation failed ({validation_type}): {validation_details}",
            context=context or {
                "validation_type": validation_type,
                "validation_details": validation_details
            },
            request_id=request_id,
            recovery_suggestions=[
                "Check export parameters are valid",
                "Verify data format and structure",
                "Review API documentation for correct usage"
            ]
        )


class ExcelLimitExceededError(ExportValidationError):
    """Raised when Excel row limits would be exceeded."""
    
    def __init__(
        self,
        request_id: str,
        total_rows: int,
        excel_limit: int = 1048576,
        context: Optional[Dict[str, Any]] = None
    ):
        # Store as direct attributes for easy access
        self.total_rows = total_rows
        self.excel_limit = excel_limit

        super().__init__(
            request_id=request_id,
            validation_type="excel_row_limit",
            validation_details=f"Dataset has {total_rows:,} rows, exceeding Excel limit of {excel_limit:,} rows",
            context=context or {
                "total_rows": total_rows,
                "excel_limit": excel_limit,
                "rows_over_limit": total_rows - excel_limit
            }
        )
        self.recovery_suggestions = [
            "Use CSV or Parquet format for large datasets",
            "Enable period separation to split data across multiple sheets",
            "Filter the data to reduce the number of rows",
            "Consider using horizontal facts layout which may reduce row count"
        ]


class MetadataRetrievalError(ExportError):
    """Raised when job metadata cannot be retrieved."""
    
    def __init__(
        self,
        request_id: str,
        job_id: int,
        error_details: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="metadata_retrieval_error",
            message=f"Failed to retrieve metadata for job {job_id}: {error_details}",
            context=context or {
                "job_id": job_id,
                "error_details": error_details
            },
            request_id=request_id,
            recovery_suggestions=[
                "Verify the job ID is correct and exists",
                "Check if the metadata table is accessible",
                "Ensure the job has completed successfully",
                "Retry the request as this may be a transient database issue"
            ]
        )


class StreamingDataError(ExportError):
    """Raised when data streaming operations fail."""
    
    def __init__(
        self,
        request_id: str,
        operation: str,
        error_details: str,
        rows_processed: Optional[int] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="streaming_data_error",
            message=f"Data streaming failed during {operation}: {error_details}",
            context=context or {
                "operation": operation,
                "error_details": error_details,
                "rows_processed": rows_processed
            },
            request_id=request_id,
            recovery_suggestions=[
                "Retry the export operation",
                "Check ClickHouse connection stability",
                "Consider reducing chunk size for large datasets",
                "Verify sufficient system memory is available"
            ]
        )


class StreamingError(ExportError):
    """Raised when streaming operations fail."""
    
    def __init__(
        self,
        request_id: str,
        stream_type: str,
        error_details: str,
        bytes_processed: Optional[int] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_type="streaming_error",
            message=f"Streaming {stream_type} failed: {error_details}",
            context=context or {
                "stream_type": stream_type,
                "error_details": error_details,
                "bytes_processed": bytes_processed
            },
            request_id=request_id,
            recovery_suggestions=[
                "Retry the export operation",
                "Check network connectivity",
                "Consider using a different export format if streaming continues to fail"
            ]
        )


class ExportErrorContext:
    """
    Context manager for tracking export operations and providing detailed error context.
    
    This class helps capture connection pool status, operation timing, and other
    contextual information when errors occur during export operations.
    """
    
    def __init__(
        self,
        request_id: str,
        operation: str,
        job_id: Optional[int] = None,
        connection_pool_adapter: Optional[Any] = None
    ) -> None:
        self.request_id = request_id
        self.operation = operation
        self.job_id = job_id
        self.connection_pool_adapter = connection_pool_adapter
        self.start_time = time.time()
        self.context: Dict[str, Any] = {}
        
    def __enter__(self) -> "ExportErrorContext":
        """Enter the context and capture initial state."""
        self.context.update({
            "operation": self.operation,
            "start_time": self.start_time,
            "job_id": self.job_id
        })
        
        # Capture connection pool status if available
        if self.connection_pool_adapter:
            try:
                pool_status = self._get_connection_pool_status()
                self.context["initial_pool_status"] = pool_status
            except Exception:
                # Don't fail the operation if we can't get pool status
                pass
                
        return self
        
    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[Any]) -> None:
        """Exit the context and enhance any exceptions with detailed context."""
        if exc_type and issubclass(exc_type, ExportError) and isinstance(exc_val, ExportError):
            # Enhance the existing ExportError with additional context
            exc_val.operation = self.operation
            exc_val.context.update(self.context)
            exc_val.context["operation_duration"] = time.time() - self.start_time
            
            # Add current connection pool status if available
            if self.connection_pool_adapter:
                try:
                    current_pool_status = self._get_connection_pool_status()
                    exc_val.connection_pool_status = current_pool_status
                except Exception:
                    pass
                    
        elif exc_type and exc_type != ExportError and exc_val:
            # Don't wrap HTTPException - let it pass through
            from fastapi import HTTPException
            if isinstance(exc_val, HTTPException):
                return  # Let HTTPException pass through unchanged

            # Convert non-ExportError exceptions to ExportError with context
            error_message = str(exc_val)

            # Create a new ExportError with full context
            export_error = ExportError(
                error_type="unexpected_error",
                message=f"Unexpected error during {self.operation}: {error_message}",
                context=self.context,
                request_id=self.request_id,
                recovery_suggestions=[
                    "Retry the operation",
                    "Check system logs for more details",
                    "Contact support if the issue persists"
                ],
                operation=self.operation
            )
            
            export_error.context["operation_duration"] = time.time() - self.start_time
            export_error.context["original_exception_type"] = exc_type.__name__
            
            # Add connection pool status if available
            if self.connection_pool_adapter:
                try:
                    current_pool_status = self._get_connection_pool_status()
                    export_error.connection_pool_status = current_pool_status
                except Exception:
                    pass
            
            # Replace the original exception
            raise export_error from exc_val
    
    def add_context(self, key: str, value: Any) -> None:
        """Add additional context information."""
        self.context[key] = value

    def get_context(self) -> Dict[str, Any]:
        """Get the current context dictionary."""
        return self.context.copy()
        
    def _get_connection_pool_status(self) -> Dict[str, Any]:
        """Get current connection pool status information."""
        if not self.connection_pool_adapter:
            return {}
            
        try:
            # Use the adapter's get_connection_pool_status method if available
            if hasattr(self.connection_pool_adapter, 'get_connection_pool_status'):
                status = self.connection_pool_adapter.get_connection_pool_status()
                return status if isinstance(status, dict) else {}
            else:
                # Fallback to basic information
                return {
                    "adapter_type": type(self.connection_pool_adapter).__name__,
                    "timestamp": time.time()
                }
        except Exception as e:
            return {
                "error": f"Failed to retrieve pool status: {str(e)}",
                "timestamp": time.time()
            }