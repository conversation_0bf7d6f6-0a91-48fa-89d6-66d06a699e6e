# magic_gateway/db/postgres_handler.py

import asyncio
from enum import Enum
import uuid
from datetime import datetime  # Ensure timezone is imported
from typing import Any, Dict, List, Optional, Tuple

from psycopg.rows import dict_row

from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import (
    PostgresException,
    QueryTrackingException,
)  # Keep QueryTrackingException? Rename to RequestTrackingException
from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import postgres_connection_manager
from magic_gateway.tracking.models import (
    ApiRequestLog,
    RequestStatus,
)  # Import new model


def format_sql_for_logging(sql: str, max_length: int = 200) -> str:
    """
    Format SQL statements for logging with intelligent truncation.

    Args:
        sql: The SQL statement to format
        max_length: Maximum length of the formatted SQL

    Returns:
        Formatted SQL string suitable for logging
    """
    if not sql:
        return ""

    # Remove excessive whitespace
    sql = sql.strip()

    # For multiline SQL, extract the first line or statement
    if "\n" in sql:
        # Get the first non-empty line that's not a comment
        lines = [line.strip() for line in sql.split("\n")]
        first_line = next(
            (line for line in lines if line and not line.startswith("--")), ""
        )

        # If it's a complex statement like CREATE TABLE, show the first line and indicate more
        if first_line and len(first_line) > 10:
            return f"{first_line[:max_length]}... [+ {len(lines) - 1} more lines]"

        # For other multiline statements, show the type and indicate more lines
        sql_type = (
            sql.strip().split(maxsplit=1)[0].upper() if sql.strip() else "UNKNOWN"
        )
        return f"{sql_type} statement... [multiline, {len(lines)} lines total]"

    # For single line SQL, truncate if needed
    if len(sql) > max_length:
        return f"{sql[:max_length]}... [truncated, {len(sql)} chars total]"

    return sql


# Define a new tracking exception if desired
class RequestTrackingException(PostgresException):
    """Exception related to request tracking persistence."""

    pass


class PostgresHandler:
    # ... (existing execute_command, execute_query methods remain mostly the same,
    #      but remove query_id parameter if it's solely for the old tracker)

    @staticmethod
    async def execute_command(
        command: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> None:
        command_type = (
            command.strip().split(maxsplit=1)[0].upper()
            if command.strip()
            else "UNKNOWN"
        )
        log_context = ""
        log.info(f"Executing PostgreSQL {command_type} command {log_context}")

        if settings.LOG_LEVEL.upper() == "DEBUG":
            # Use the new formatting function for better SQL logging
            formatted_command = format_sql_for_logging(command)
            log.debug(f"Command {log_context}: {formatted_command}")

            if params:
                # Format parameters for better readability
                param_str = str(params)
                if len(param_str) > 200:
                    param_str = f"{param_str[:200]}... [{len(param_str)} chars total]"
                log.debug(f"Params {log_context}: {param_str}")

        try:
            async with postgres_connection_manager.connection() as conn:
                timeout_ms = int(settings.MAX_QUERY_EXECUTION_TIME * 1000)
                await conn.execute(f"SET LOCAL statement_timeout = {timeout_ms}")
                sql_command = command
                await conn.execute(sql_command, params or {})
                log.info(f"PostgreSQL command executed successfully {log_context}")
        except PostgresException as e:
            log.error(f"PostgreSQL command failed {log_context}: {e}", exc_info=True)
            raise
        except Exception as e:
            log.error(
                f"Unexpected error executing PostgreSQL command {log_context}: {e}",
                exc_info=True,
            )
            if "statement timeout" in str(e).lower():
                raise PostgresException(
                    f"PostgreSQL command timed out after {settings.MAX_QUERY_EXECUTION_TIME}s {log_context}"
                ) from e
            raise PostgresException(
                f"Unexpected error during PostgreSQL command {log_context}: {e}"
            ) from e

    @staticmethod
    async def execute_query(
        query: str,
        params: Optional[Dict[str, Any]] = None,
        request_id: Optional[uuid.UUID] = None,  # Use request_id for context
        allow_write: bool = False,
        timeout_seconds: Optional[int] = None,  # Custom timeout in seconds
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        # ... (similar update for query logging, add request_id)
        query_lower_stripped = query.strip().lower()
        if not allow_write and not query_lower_stripped.startswith(
            ("select", "with", "show", "explain", "fetch")  # Added fetch
        ):
            log.error(
                f"Attempted execute_query with non-SELECT statement and allow_write=False: {query[:100]}..."
            )
            raise PostgresException(
                "execute_query is intended for SELECT statements. Use execute_command for modifications or set allow_write=True."
            )

        query_type = (
            query_lower_stripped.split(maxsplit=1)[0].upper()
            if query_lower_stripped
            else "UNKNOWN"
        )
        log_context = f"(ReqID: {str(request_id)[:8]})" if request_id else ""
        log.info(f"Executing PostgreSQL {query_type} query {log_context}")

        # Add debug logging with better formatting
        if settings.LOG_LEVEL.upper() == "DEBUG":
            # Use the new formatting function for better SQL logging
            formatted_query = format_sql_for_logging(query)
            log.debug(f"Query {log_context}: {formatted_query}")

            if params:
                # Format parameters for better readability
                param_str = str(params)
                if len(param_str) > 200:
                    param_str = f"{param_str[:200]}... [{len(param_str)} chars total]"
                log.debug(f"Params {log_context}: {param_str}")

        # ... (rest of the method, add request_id to SQL comment)
        start_time = asyncio.get_event_loop().time()
        results: List[Dict[str, Any]] = []
        column_names: List[str] = []

        try:
            async with postgres_connection_manager.connection() as conn:
                # Use custom timeout if provided, otherwise use default
                effective_timeout = timeout_seconds or settings.MAX_QUERY_EXECUTION_TIME
                timeout_ms = int(effective_timeout * 1000)
                await conn.execute(f"SET LOCAL statement_timeout = {timeout_ms}")

                # Add request_id and timeout info to SQL comment
                comment = f"-- ReqID: {request_id}"
                if timeout_seconds:
                    comment += f", Custom timeout: {timeout_seconds}s"
                sql_query = f"{comment}\n{query}" if request_id else query

                async with conn.cursor(row_factory=dict_row) as cur:
                    await cur.execute(sql_query, params or {})
                    if cur.description:
                        column_names = [col.name for col in cur.description]
                    results = await cur.fetchall()

            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            log.info(
                f"PostgreSQL query {log_context} completed in {execution_time:.3f}s, "
                f"returned {len(results)} rows."
            )
            return results, column_names
        # ... (exception handling remains similar)
        except PostgresException as e:
            log.error(f"PostgreSQL query failed {log_context}: {e}", exc_info=True)
            raise
        except Exception as e:
            log.error(
                f"Unexpected error executing PostgreSQL query {log_context}: {e}",
                exc_info=True,
            )
            if "statement timeout" in str(e).lower():
                raise PostgresException(
                    f"PostgreSQL query timed out after {settings.MAX_QUERY_EXECUTION_TIME}s {log_context}"
                ) from e
            raise PostgresException(
                f"Unexpected error during PostgreSQL query {log_context}: {e}"
            ) from e

    # --- Request Tracking Methods ---

    @staticmethod
    async def insert_request_log(log_entry: ApiRequestLog) -> None:
        """Insert a new request log entry."""
        query = """
        INSERT INTO metadata.api_request_history (
            request_id, endpoint_path, http_method, status, start_time,
            client_ip, username, status_code, end_time, duration_ms,
            error_message, task_details
        ) VALUES (
            %(request_id)s, %(endpoint_path)s, %(http_method)s, %(status)s, %(start_time)s,
            %(client_ip)s, %(username)s, %(status_code)s, %(end_time)s, %(duration_ms)s,
            %(error_message)s, %(task_details)s::jsonb
        )
        ON CONFLICT (request_id) DO NOTHING; -- Avoid error if middleware retries somehow
        """
        try:
            # Convert model to dict for params
            params = log_entry.model_dump(mode="json")  # Use model_dump for Pydantic v2
            # Ensure status is the enum value
            params["status"] = log_entry.status.value
            # Ensure task_details is serialized (though model_dump should handle it)
            if params["task_details"] is not None and not isinstance(
                params["task_details"], str
            ):
                import json

                params["task_details"] = json.dumps(params["task_details"])

            try:
                await PostgresHandler.execute_command(query, params)
                log.debug(f"Inserted request log start: {log_entry.request_id}")
            except Exception as query_e:
                # Check if the error is due to missing table
                if (
                    "relation" in str(query_e).lower()
                    and "does not exist" in str(query_e).lower()
                ):
                    log.warning(
                        "Metadata table does not exist. Request logging is disabled."
                    )
                else:
                    # Re-raise for other errors
                    raise
        except Exception as e:
            log.error(
                f"Failed to insert request log for {log_entry.request_id}: {e}",
                exc_info=True,
            )
            # Don't let logging failure break the request flow

    @staticmethod
    async def update_request_log(log_entry: ApiRequestLog) -> None:
        """Update an existing request log entry, typically at the end."""
        query = """
        UPDATE metadata.api_request_history
        SET
            status = %(status)s,
            end_time = %(end_time)s,
            duration_ms = %(duration_ms)s,
            username = COALESCE(%(username)s, username), -- Update username if provided
            status_code = %(status_code)s,
            error_message = %(error_message)s,
            task_details = COALESCE(%(task_details)s::jsonb, task_details) -- Merge/update details
        WHERE
            request_id = %(request_id)s;
        """
        try:
            params = log_entry.model_dump(mode="json")
            params["status"] = log_entry.status.value
            if params["task_details"] is not None and not isinstance(
                params["task_details"], str
            ):
                import json

                params["task_details"] = json.dumps(params["task_details"])

            try:
                await PostgresHandler.execute_command(query, params)
                log.debug(
                    f"Updated request log end: {log_entry.request_id}, Status: {log_entry.status.value}"
                )
            except Exception as query_e:
                # Check if the error is due to missing table
                if (
                    "relation" in str(query_e).lower()
                    and "does not exist" in str(query_e).lower()
                ):
                    log.warning(
                        "Metadata table does not exist. Request logging is disabled."
                    )
                else:
                    # Re-raise for other errors
                    raise
        except Exception as e:
            log.error(
                f"Failed to update request log for {log_entry.request_id}: {e}",
                exc_info=True,
            )
            # Don't let logging failure break the request flow

    @staticmethod
    async def update_request_log_partial(
        request_id: uuid.UUID, updates: Dict[str, Any]
    ) -> None:
        """Update specific fields of a request log entry (e.g., username, task_details)."""
        if not updates:
            return

        set_clauses = []
        params = {"request_id": request_id}

        for key, value in updates.items():
            # Validate key against ApiRequestLog fields if needed
            # Handle special cases like status enum and jsonb
            if key == "status" and isinstance(value, Enum):
                params[key] = value.value
            elif key == "task_details":
                # Need careful handling for JSONB updates (merge vs replace)
                # This example replaces. For merging, use jsonb_set or jsonb_insert in SQL
                params[key] = (
                    value  # Assume value is already JSON serializable dict/list
                )
                set_clauses.append(f"{key} = %(task_details)s::jsonb")
                continue  # Skip default clause generation
            else:
                params[key] = value

            set_clauses.append(f"{key} = %({key})s")

        query = f"""
        UPDATE metadata.api_request_history
        SET {", ".join(set_clauses)}
        WHERE request_id = %(request_id)s;
        """
        try:
            # Need to handle task_details serialization if it wasn't skipped above
            if (
                "task_details" in params
                and params["task_details"] is not None
                and not isinstance(params["task_details"], str)
            ):
                import json

                params["task_details"] = json.dumps(params["task_details"])

            try:
                await PostgresHandler.execute_command(query, params)
                log.debug(
                    f"Partially updated request log: {request_id}, Fields: {list(updates.keys())}"
                )
            except Exception as query_e:
                # Check if the error is due to missing table
                if (
                    "relation" in str(query_e).lower()
                    and "does not exist" in str(query_e).lower()
                ):
                    log.warning(
                        "Metadata table does not exist. Request logging is disabled."
                    )
                else:
                    # Re-raise for other errors
                    raise

        except Exception as e:
            log.error(
                f"Failed to partially update request log for {request_id}: {e}",
                exc_info=True,
            )
            # raise RequestTrackingException(f"Failed to partially update request log: {e}") from e

    @staticmethod
    async def get_request_log(request_id: uuid.UUID) -> Optional[ApiRequestLog]:
        """Retrieve a specific request log entry."""
        query = "SELECT * FROM metadata.api_request_history WHERE request_id = %(request_id)s"
        try:
            try:
                results, _ = await PostgresHandler.execute_query(
                    query, {"request_id": request_id}
                )
                if results:
                    # Convert raw dict to Pydantic model
                    return ApiRequestLog(**results[0])
                return None
            except Exception as query_e:
                # Check if the error is due to missing table
                if (
                    "relation" in str(query_e).lower()
                    and "does not exist" in str(query_e).lower()
                ):
                    log.warning(
                        "Metadata table does not exist. Request logging is disabled."
                    )
                    return None
                else:
                    # Re-raise for other errors
                    raise
        except Exception as e:
            log.error(
                f"Failed to retrieve request log {request_id}: {e}", exc_info=True
            )
            # Don't raise exception to avoid breaking the request flow
            return None

    # Method to get history (used by admin endpoint)
    @staticmethod
    async def get_request_history(
        limit: int = 100,
        offset: int = 0,
        username: Optional[str] = None,
        status: Optional[RequestStatus] = None,
        endpoint_path: Optional[str] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
    ) -> List[ApiRequestLog]:
        """Retrieve request history with filtering and pagination."""
        query = "SELECT * FROM metadata.api_request_history WHERE 1=1"
        params = {}

        if username:
            query += " AND username = %(username)s"
            params["username"] = username
        if status:
            query += " AND status = %(status)s"
            params["status"] = status.value
        if endpoint_path:
            # Use LIKE for partial matching if needed, e.g., /api/v1/%
            query += " AND endpoint_path = %(endpoint_path)s"
            params["endpoint_path"] = endpoint_path
        if from_date:
            query += " AND start_time >= %(from_date)s"
            params["from_date"] = from_date
        if to_date:
            query += " AND start_time <= %(to_date)s"
            params["to_date"] = to_date

        query += " ORDER BY start_time DESC LIMIT %(limit)s OFFSET %(offset)s"
        params["limit"] = limit
        params["offset"] = offset

        try:
            try:
                results, _ = await PostgresHandler.execute_query(query, params)
                return [ApiRequestLog(**row) for row in results]
            except Exception as query_e:
                # Check if the error is due to missing table
                if (
                    "relation" in str(query_e).lower()
                    and "does not exist" in str(query_e).lower()
                ):
                    log.warning(
                        "Metadata table does not exist. Request history is disabled."
                    )
                    return []
                else:
                    # Re-raise for other errors
                    raise
        except Exception as e:
            log.error(f"Failed to retrieve request history: {e}", exc_info=True)
            # Don't raise exception to avoid breaking the request flow
            return []

    # --- REMOVE Old log_query_execution method ---
    # @staticmethod
    # async def log_query_execution(...) -> None: ...

    @staticmethod
    async def get_column_mapping() -> List[Dict[str, str]]:
        """
        Retrieve column mappings between PostgreSQL and ClickHouse from cps.colname_map_for_clickhouse table.

        Returns:
            List of dictionaries containing 'colname_postgres' and 'colname_clickhouse' mappings.
        """
        query = "SELECT colname_postgres, colname_clickhouse FROM cps.colname_map_for_clickhouse"
        log.info("Retrieving column mappings from cps.colname_map_for_clickhouse")

        try:
            results, _ = await PostgresHandler.execute_query(query)
            log.info(f"Retrieved {len(results)} column mappings")
            return results
        except Exception as e:
            log.error(f"Failed to retrieve column mappings: {e}", exc_info=True)
            raise PostgresException(f"Failed to retrieve column mappings: {e}") from e

    @staticmethod
    async def get_object_description(
        object_name: str,
        object_type: Optional[str] = None,
        request_id: Optional[uuid.UUID] = None,
        timeout_seconds: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Retrieve DDL (Data Definition Language) statement and description of a PostgreSQL table or view.

        Args:
            object_name: Full object name including schema (e.g., 'schema.table_name')
            object_type: Optional type of object ('table', 'view', etc.)
            request_id: Optional request ID for tracking and logging
            timeout_seconds: Optional custom timeout in seconds for this request

        Returns:
            Dictionary containing object DDL and description information
        """
        # Split the object name into schema and table/view name
        parts = object_name.split(".")
        if len(parts) != 2:
            raise PostgresException(
                f"Invalid object name format: {object_name}. Expected format: 'schema.object_name'"
            )

        schema_name, table_name = parts
        log_context = f"(ReqID: {str(request_id)[:8]})" if request_id else ""
        log.info(f"Retrieving DDL for {schema_name}.{table_name} {log_context}")

        try:
            # Get current database name
            current_db_query = "SELECT current_database() as db_name"
            db_result, _ = await PostgresHandler.execute_query(
                current_db_query, request_id=request_id, timeout_seconds=timeout_seconds
            )
            current_db = db_result[0].get("db_name")

            # Query to get table/view description
            object_query = """
            SELECT
                t.table_schema,
                t.table_name,
                t.table_type,
                obj_description(format('%%s.%%s', t.table_schema, t.table_name)::regclass::oid) as table_description,
                pg_catalog.pg_get_userbyid(c.relowner) as owner,
                c.relkind as object_kind
            FROM
                information_schema.tables t
            JOIN
                pg_catalog.pg_class c ON format('%%s.%%s', t.table_schema, t.table_name)::regclass::oid = c.oid
            WHERE
                t.table_schema = %(schema)s
                AND t.table_name = %(table)s
            """

            # Query to get DDL for a table
            table_ddl_query = """
            SELECT pg_catalog.pg_get_tabledef(%(full_object_name)s::regclass::oid) as ddl;
            """

            # Query to get DDL for a view
            view_ddl_query = """
            SELECT 'CREATE VIEW ' || %(full_object_name)s || ' AS ' ||
                   pg_catalog.pg_get_viewdef(%(full_object_name)s::regclass::oid, true) as ddl;
            """

            # Execute queries
            params = {"schema": schema_name, "table": table_name}
            full_object_name = f"{schema_name}.{table_name}"
            params_with_full_name = {"full_object_name": full_object_name}

            object_result, _ = await PostgresHandler.execute_query(
                object_query,
                params,
                request_id=request_id,
                timeout_seconds=timeout_seconds,
            )

            if not object_result:
                raise PostgresException(
                    f"Object {schema_name}.{table_name} not found in current database"
                )

            # Determine if it's a table or view and get appropriate DDL
            object_kind = object_result[0].get("object_kind")
            ddl_result = None

            # Use object_type parameter if provided
            if object_type and object_type.lower() in (
                "table",
                "view",
                "materialized_view",
            ):
                if object_type.lower() == "table":
                    object_kind = "r"  # Force table type
                elif object_type.lower() in ("view", "materialized_view"):
                    object_kind = "v"  # Force view type

            try:
                if object_kind == "r":  # Regular table
                    # First try pg_get_tabledef which is more comprehensive
                    try:
                        ddl_result, _ = await PostgresHandler.execute_query(
                            table_ddl_query,
                            params_with_full_name,
                            request_id=request_id,
                            timeout_seconds=timeout_seconds,
                        )
                    except Exception:
                        # If pg_get_tabledef is not available, fall back to a simpler approach
                        ddl_query = f"""SELECT 'CREATE TABLE {full_object_name} (' ||
                            string_agg(column_definition, ', ') || ')' as ddl
                            FROM (
                                SELECT
                                    column_name || ' ' ||
                                    data_type ||
                                    CASE
                                        WHEN character_maximum_length IS NOT NULL THEN '(' || character_maximum_length || ')'
                                        WHEN numeric_precision IS NOT NULL AND numeric_scale IS NOT NULL THEN
                                            '(' || numeric_precision || ',' || numeric_scale || ')'
                                        ELSE ''
                                    END ||
                                    CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
                                    CASE WHEN column_default IS NOT NULL THEN ' DEFAULT ' || column_default ELSE '' END
                                    as column_definition
                                FROM
                                    information_schema.columns
                                WHERE
                                    table_schema = %(schema)s AND table_name = %(table)s
                                ORDER BY
                                    ordinal_position
                            ) t"""
                        ddl_result, _ = await PostgresHandler.execute_query(
                            ddl_query,
                            params,
                            request_id=request_id,
                            timeout_seconds=timeout_seconds,
                        )
                elif object_kind in ("v", "m"):  # View or materialized view
                    if object_kind == "m":  # Materialized view
                        # For materialized views, use a different query
                        mat_view_ddl_query = """
                        SELECT 'CREATE MATERIALIZED VIEW ' || %(full_object_name)s || ' AS ' ||
                               pg_catalog.pg_get_viewdef(%(full_object_name)s::regclass::oid, true) as ddl;
                        """
                        ddl_result, _ = await PostgresHandler.execute_query(
                            mat_view_ddl_query,
                            params_with_full_name,
                            request_id=request_id,
                            timeout_seconds=timeout_seconds,
                        )
                    else:  # Regular view
                        ddl_result, _ = await PostgresHandler.execute_query(
                            view_ddl_query,
                            params_with_full_name,
                            request_id=request_id,
                            timeout_seconds=timeout_seconds,
                        )
                else:  # Other object types
                    ddl_result = [
                        {"ddl": f"-- DDL not available for object type: {object_kind}"}
                    ]
            except Exception as e:
                log.warning(f"Could not retrieve DDL: {e}")
                ddl_result = [{"ddl": f"-- Error retrieving DDL: {str(e)}"}]

            # Combine results
            result = {
                "database": current_db,
                "schema_name": schema_name,  # Changed from 'schema' to match the model
                "name": table_name,
                "type": object_result[0].get("table_type"),
                "ddl": ddl_result[0].get("ddl")
                if ddl_result
                else "-- DDL not available",
                "status": "completed",
            }

            # Process DDL to remove newlines if requested
            if isinstance(result["ddl"], str):
                # Replace all newlines with spaces, and normalize whitespace
                result["ddl"] = " ".join(result["ddl"].split())

            log.info(f"Retrieved DDL for {current_db}.{schema_name}.{table_name}")
            return result

        except Exception as e:
            log.error(f"Failed to retrieve object DDL: {e}", exc_info=True)
            raise PostgresException(f"Failed to retrieve object DDL: {e}") from e
