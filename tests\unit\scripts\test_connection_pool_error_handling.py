"""
Unit tests for connection pool error handling in scripts.

Tests comprehensive error handling for connection pool operations including:
- ClickHouse and PostgreSQL connection timeout errors
- Connection pool exhaustion scenarios
- Retry logic for transient connection failures
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, Mock, patch
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

from clickhouse_driver.errors import <PERSON>rror as ClickHouseDriverError
from clickhouse_driver.errors import NetworkError as ClickHouseNetworkError
from clickhouse_driver.errors import ServerException as ClickHouseServerException
from psycopg.errors import OperationalError as PsycopgOperationalError
from psycopg_pool import PoolTimeout

from magic_gateway.core.exceptions import (
    ScriptExecutionException,
    ClickHouseException,
    PostgresException,
    ConnectionPoolException,
    ConnectionPoolExhaustedException,
    ConnectionTimeoutException,
    TransientConnectionException,
)
from magic_gateway.scripts.async_bridge import (
    execute_clickhouse_command_async,
    execute_clickhouse_query_async,
    execute_postgres_query_async,
    execute_clickhouse_command,
    execute_clickhouse_query,
    execute_postgres_query,
    is_transient_error,
    retry_on_transient_error,
    AsyncBridge,
)


class TestTransientErrorDetection:
    """Test transient error detection logic."""

    def test_clickhouse_network_error_is_transient(self):
        """Test that ClickHouse network errors are detected as transient."""
        error = ClickHouseNetworkError("Connection refused")
        assert is_transient_error(error) is True

    def test_clickhouse_driver_error_connection_refused_is_transient(self):
        """Test that ClickHouse connection refused errors are transient."""
        error = ClickHouseDriverError("Connection refused")
        assert is_transient_error(error) is True

    def test_clickhouse_driver_error_timeout_is_transient(self):
        """Test that ClickHouse timeout errors are transient."""
        error = ClickHouseDriverError("Connection timed out")
        assert is_transient_error(error) is True

    def test_clickhouse_server_error_is_not_transient(self):
        """Test that ClickHouse server errors are not transient."""
        error = ClickHouseServerException("Syntax error")
        assert is_transient_error(error) is False

    def test_postgres_operational_error_connection_refused_is_transient(self):
        """Test that PostgreSQL connection refused errors are transient."""
        error = PsycopgOperationalError("connection refused")
        assert is_transient_error(error) is True

    def test_postgres_operational_error_timeout_is_transient(self):
        """Test that PostgreSQL timeout errors are transient."""
        error = PsycopgOperationalError("connection timed out")
        assert is_transient_error(error) is True

    def test_pool_timeout_is_transient(self):
        """Test that pool timeout errors are transient."""
        error = PoolTimeout("Pool timeout")
        assert is_transient_error(error) is True

    def test_generic_error_is_not_transient(self):
        """Test that generic errors are not transient."""
        error = ValueError("Some value error")
        assert is_transient_error(error) is False


class TestRetryDecorator:
    """Test retry decorator functionality."""

    @pytest.mark.asyncio
    async def test_retry_succeeds_on_first_attempt(self):
        """Test that retry decorator doesn't interfere with successful operations."""

        @retry_on_transient_error(max_retries=2)
        async def successful_operation():
            return "success"

        result = await successful_operation()
        assert result == "success"

    @pytest.mark.asyncio
    async def test_retry_succeeds_after_transient_error(self):
        """Test that retry decorator retries transient errors and eventually succeeds."""
        call_count = 0

        @retry_on_transient_error(max_retries=2, base_delay=0.01)
        async def operation_with_transient_error():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ClickHouseNetworkError("Connection refused")
            return "success"

        result = await operation_with_transient_error()
        assert result == "success"
        assert call_count == 2

    @pytest.mark.asyncio
    async def test_retry_fails_after_max_attempts(self):
        """Test that retry decorator fails after max attempts with transient errors."""
        call_count = 0

        @retry_on_transient_error(max_retries=2, base_delay=0.01)
        async def always_failing_operation():
            nonlocal call_count
            call_count += 1
            raise ClickHouseNetworkError("Connection refused")

        with pytest.raises(TransientConnectionException) as exc_info:
            await always_failing_operation()

        assert call_count == 3  # Initial attempt + 2 retries
        assert "Operation failed after 3 attempts" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_retry_does_not_retry_non_transient_errors(self):
        """Test that retry decorator doesn't retry non-transient errors."""
        call_count = 0

        @retry_on_transient_error(max_retries=2)
        async def operation_with_non_transient_error():
            nonlocal call_count
            call_count += 1
            raise ClickHouseServerException("Syntax error")

        with pytest.raises(ClickHouseServerException):
            await operation_with_non_transient_error()

        assert call_count == 1  # Only one attempt


class TestClickHouseErrorHandling:
    """Test ClickHouse connection error handling."""

    @pytest.mark.asyncio
    async def test_clickhouse_command_pool_timeout(self):
        """Test ClickHouse command handling of pool timeout."""
        mock_manager = Mock()
        mock_manager.connection.side_effect = PoolTimeout("Pool timeout")

        with pytest.raises(ConnectionTimeoutException) as exc_info:
            await execute_clickhouse_command_async(mock_manager, "SELECT 1")

        assert "Timeout acquiring ClickHouse connection" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_clickhouse_command_server_exception(self):
        """Test ClickHouse command handling of server exceptions."""
        mock_manager = Mock()
        mock_client = Mock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_client

        # Mock the executor to raise a server exception
        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor.side_effect = (
                ClickHouseServerException("Syntax error")
            )

            with pytest.raises(ClickHouseException) as exc_info:
                await execute_clickhouse_command_async(mock_manager, "INVALID SQL")

            assert "ClickHouse server error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_clickhouse_command_network_error_retries(self):
        """Test ClickHouse command retries on network errors."""
        mock_manager = Mock()
        mock_client = Mock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_client

        call_count = 0

        def mock_executor(*args):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise ClickHouseNetworkError("Connection refused")
            return "success"

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor.side_effect = mock_executor

            result = await execute_clickhouse_command_async(mock_manager, "SELECT 1")
            assert result == "success"
            assert call_count == 3  # Initial + 2 retries

    @pytest.mark.asyncio
    async def test_clickhouse_query_connection_pool_exhausted(self):
        """Test ClickHouse query handling of connection pool exhaustion."""
        mock_manager = Mock()
        mock_manager.connection.side_effect = ClickHouseDriverError(
            "too many connections"
        )

        with pytest.raises(ConnectionPoolExhaustedException) as exc_info:
            await execute_clickhouse_query_async(mock_manager, "SELECT 1")

        assert "ClickHouse connection pool exhausted" in str(exc_info.value)

    def test_clickhouse_command_sync_wrapper(self):
        """Test synchronous wrapper for ClickHouse command execution."""
        mock_manager = Mock()
        mock_client = Mock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_client

        with patch("asyncio.run") as mock_run:
            mock_run.return_value = "success"

            result = execute_clickhouse_command(mock_manager, "SELECT 1")
            assert result == "success"
            mock_run.assert_called_once()


class TestPostgreSQLErrorHandling:
    """Test PostgreSQL connection error handling."""

    @pytest.mark.asyncio
    async def test_postgres_query_pool_timeout(self):
        """Test PostgreSQL query handling of pool timeout."""
        mock_manager = Mock()
        mock_manager.connection.side_effect = PoolTimeout("Pool timeout")

        with pytest.raises(ConnectionTimeoutException) as exc_info:
            await execute_postgres_query_async(mock_manager, "SELECT 1")

        assert "Timeout acquiring PostgreSQL connection" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_postgres_query_operational_error_transient(self):
        """Test PostgreSQL query handling of transient operational errors."""
        mock_manager = Mock()
        mock_conn = AsyncMock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_conn

        call_count = 0

        async def mock_execute(*args):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise PsycopgOperationalError("connection refused")
            mock_cursor = Mock()
            mock_cursor.fetchall.return_value = [("result",)]
            return mock_cursor

        mock_conn.execute.side_effect = mock_execute

        result = await execute_postgres_query_async(mock_manager, "SELECT 1")
        assert result == [("result",)]
        assert call_count == 3  # Initial + 2 retries

    @pytest.mark.asyncio
    async def test_postgres_query_connection_limit_error(self):
        """Test PostgreSQL query handling of connection limit errors."""
        mock_manager = Mock()
        mock_manager.connection.side_effect = PsycopgOperationalError(
            "too many connections"
        )

        with pytest.raises(ConnectionPoolExhaustedException) as exc_info:
            await execute_postgres_query_async(mock_manager, "SELECT 1")

        assert "PostgreSQL connection pool exhausted" in str(exc_info.value)

    def test_postgres_query_sync_wrapper(self):
        """Test synchronous wrapper for PostgreSQL query execution."""
        mock_manager = Mock()

        with patch("asyncio.run") as mock_run:
            mock_run.return_value = [("result",)]

            result = execute_postgres_query(mock_manager, "SELECT 1")
            assert result == [("result",)]
            mock_run.assert_called_once()


class TestAsyncBridge:
    """Test AsyncBridge class error handling."""

    def test_async_bridge_missing_connection_manager(self):
        """Test AsyncBridge handling of missing connection managers."""
        bridge = AsyncBridge({})

        with pytest.raises(ScriptExecutionException) as exc_info:
            bridge.clickhouse_command("SELECT 1")

        assert "Connection manager 'clickhouse_primary' not available" in str(
            exc_info.value
        )
        assert "Available managers: []" in str(exc_info.value)

    def test_async_bridge_available_managers_listed(self):
        """Test AsyncBridge lists available managers in error messages."""
        managers = {"postgres": Mock(), "clickhouse_cluster": Mock()}
        bridge = AsyncBridge(managers)

        with pytest.raises(ScriptExecutionException) as exc_info:
            bridge.clickhouse_command("SELECT 1", manager_key="nonexistent")

        assert "Connection manager 'nonexistent' not available" in str(exc_info.value)
        assert "postgres" in str(exc_info.value)
        assert "clickhouse_cluster" in str(exc_info.value)

    def test_async_bridge_context_manager(self):
        """Test AsyncBridge context manager functionality."""
        with AsyncBridge() as bridge:
            assert bridge._executor is not None

        # Executor should be shut down after exiting context
        assert (
            bridge._executor is not None
        )  # Reference remains but executor is shut down


class TestErrorPropagation:
    """Test error propagation through the async/sync bridge."""

    def test_connection_timeout_propagates_correctly(self):
        """Test that ConnectionTimeoutException propagates correctly through sync wrapper."""
        mock_manager = Mock()
        mock_manager.connection.side_effect = PoolTimeout("Pool timeout")

        with pytest.raises(ConnectionTimeoutException):
            execute_clickhouse_command(mock_manager, "SELECT 1")

    def test_connection_pool_exhausted_propagates_correctly(self):
        """Test that ConnectionPoolExhaustedException propagates correctly through sync wrapper."""
        mock_manager = Mock()
        mock_manager.connection.side_effect = ClickHouseDriverError(
            "too many connections"
        )

        with pytest.raises(ConnectionPoolExhaustedException):
            execute_clickhouse_command(mock_manager, "SELECT 1")

    def test_transient_connection_exception_propagates_correctly(self):
        """Test that TransientConnectionException propagates correctly through sync wrapper."""
        mock_manager = Mock()
        mock_client = Mock()
        mock_manager.connection.return_value.__aenter__.return_value = mock_client

        # Mock persistent network errors
        with patch("asyncio.run") as mock_run:
            mock_run.side_effect = TransientConnectionException(
                "Network error persisted after retries"
            )

            with pytest.raises(TransientConnectionException):
                execute_clickhouse_command(mock_manager, "SELECT 1")
