# magic_gateway/api/v1/endpoints/admin.py

import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone  # Ensure timezone imported

from fastapi import APIRouter, Depends, HTTPException, status, Query

# Use new dependencies and models
from magic_gateway.api.deps import (
    get_request_tracker_service,
    check_database_connections,
    check_connection_pools,
)
from magic_gateway.tracking.service import RequestTrackingService
from magic_gateway.tracking.models import (
    RequestStatusResponse,
    RequestStatus,
    DatabaseType,
)  # Import new models
from magic_gateway.api.v1.models import HealthCheck  # Keep HealthCheck

from magic_gateway.auth.dependencies import require_admin
from magic_gateway.core.config import settings
from magic_gateway.core.logging_config import log
from magic_gateway.monitoring.service import connection_pool_monitoring_service
from magic_gateway.db.connection_manager import clickhouse_registry

# Import DB handlers ONLY if needed for cancellation logic here
from magic_gateway.db.clickhouse_handler import <PERSON>lick<PERSON>ouse<PERSON>and<PERSON>
from magic_gateway.db.postgres_handler import <PERSON>gres<PERSON><PERSON><PERSON>
from magic_gateway.core.exceptions import <PERSON>lickHouseException, PostgresException


router = APIRouter()


# Enhanced health check with connection pool monitoring
@router.get("/health", response_model=HealthCheck)
async def health_check(
    db_connections: Dict[str, Dict[str, Any]] = Depends(check_database_connections),
    pool_status: Dict[str, Dict[str, Any]] = Depends(check_connection_pools),
) -> Any:
    import time

    if not hasattr(health_check, "start_time"):
        health_check.start_time = time.time()  # Simple uptime
    uptime = time.time() - health_check.start_time

    # Check if required databases are connected (primary connections)
    required_dbs = ["postgres", "clickhouse", "logs"]

    # Add debug logging for connection status
    for db in required_dbs:
        connected = db_connections.get(db, {}).get("connected", False)
        message = db_connections.get(db, {}).get("message", "No message")
        log.debug(
            f"Health Check: {db} connection status: {connected}, message: {message}"
        )

    required_connected = all(
        db_connections.get(db, {}).get("connected", False) for db in required_dbs
    )

    # Check if any connection pools are in critical state
    any_pool_critical = any(
        pool["status"] == "critical" for pool in pool_status.values()
    )

    # Check cluster status if configured
    cluster_status = "healthy"
    if settings.has_clickhouse_cluster:
        cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
        cluster_db_key = f"clickhouse_{cluster_name}"
        cluster_pool_key = f"clickhouse_{cluster_name}"

        # Check if cluster database is connected
        if (
            cluster_db_key in db_connections
            and not db_connections[cluster_db_key]["connected"]
        ):
            cluster_status = "degraded"

        # Check if cluster pool is in critical state
        if (
            cluster_pool_key in pool_status
            and pool_status[cluster_pool_key]["status"] == "critical"
        ):
            cluster_status = "warning"

    # Determine overall status
    # Special case: If we're using a cluster and the cluster is connected but primary isn't
    if (
        settings.has_clickhouse_cluster
        and not db_connections.get("clickhouse", {}).get("connected", False)
        and db_connections.get(
            f"clickhouse_{settings.CLICKHOUSE_CLUSTER_NAME or 'default_cluster'}", {}
        ).get("connected", False)
    ):
        log.info(
            "Health Check: Primary ClickHouse unavailable but cluster is available, considering system operational"
        )

        # Check if other required databases are connected
        other_required_dbs = ["postgres", "logs"]
        other_required_connected = all(
            db_connections.get(db, {}).get("connected", False)
            for db in other_required_dbs
        )

        if not other_required_connected:
            status = "degraded"  # Other primary connections are still required
        elif any_pool_critical:
            status = "warning"  # Critical pool utilization
        else:
            status = "healthy"  # Cluster is available, other systems operational
    else:
        # Standard check
        if not required_connected:
            status = "degraded"  # Primary connections are required
        elif any_pool_critical:
            status = "warning"  # Critical pool utilization
        elif cluster_status != "healthy":
            status = cluster_status  # Cluster issues but primary connections are fine
        else:
            status = "healthy"  # All systems operational

    return HealthCheck(
        status=status,
        version=settings.APP_VERSION,
        timestamp=datetime.now(timezone.utc),
        databases=db_connections,
        connection_pools=pool_status,
        uptime=uptime,
    )


# --- Connection Pool Monitoring Endpoint ---
@router.get(
    "/connection-pools",
    dependencies=[Depends(require_admin)],
)
async def get_connection_pool_metrics() -> Dict[str, Any]:
    """Get detailed connection pool metrics (admin only)."""
    # Ensure we have the latest metrics
    await connection_pool_monitoring_service.collect_and_store_metrics()

    # Get the last metrics for each pool
    result = {}
    for pool_type, metrics in connection_pool_monitoring_service._last_metrics.items():
        result[pool_type] = metrics.model_dump()

    # Add cluster information if available
    if settings.has_clickhouse_cluster:
        cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
        if clickhouse_registry.initialized:
            # Add available clusters information
            result["available_clusters"] = clickhouse_registry.get_available_clusters()

    return {
        "timestamp": datetime.now(timezone.utc),
        "pools": result,
        "cluster_enabled": settings.has_clickhouse_cluster
        if hasattr(settings, "has_clickhouse_cluster")
        else False,
    }


# --- NEW Request History Endpoint ---
@router.get(
    "/requests/history",
    response_model=List[RequestStatusResponse],
    dependencies=[Depends(require_admin)],
)
async def get_request_history(
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    username: Optional[str] = Query(None),
    status: Optional[RequestStatus] = Query(
        None, description="Filter by request status"
    ),
    endpoint_path: Optional[str] = Query(
        None, description="Filter by endpoint path (exact match)"
    ),
    from_date: Optional[datetime] = Query(None, description="ISO 8601 format"),
    to_date: Optional[datetime] = Query(None, description="ISO 8601 format"),
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Any:
    """Get API request execution history (admin only)."""
    try:
        history = await tracker.get_request_history(
            limit=limit,
            offset=offset,
            username=username,
            status=status,
            endpoint_path=endpoint_path,
            from_date=from_date,
            to_date=to_date,
        )
        return history
    except Exception as e:
        log.error(f"Error getting request history: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get request history: {e}",
        )


# --- NEW Request Status Endpoint ---
@router.get(
    "/requests/{request_id}/status",
    response_model=RequestStatusResponse,
    dependencies=[
        Depends(require_admin)
    ],  # Or allow users to check their own requests?
)
async def get_single_request_status(
    request_id: uuid.UUID,
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Any:
    """Get the status and details of a specific API request (admin only)."""
    status_info = await tracker.get_request_status(request_id)
    if not status_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Request with ID {request_id} not found in history.",
        )
    return status_info


# --- NEW Cancellation Endpoint ---
@router.post(
    "/requests/{request_id}/cancel",
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(require_admin)],  # Or allow users to cancel their own
)
async def cancel_request_task(
    request_id: uuid.UUID,
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Dict[str, Any]:
    """
    Attempt to cancel the long-running task associated with a request ID.
    (Admin only, or requires user check).
    """
    log.info(f"Attempting cancellation for request: {request_id}")
    status_info = await tracker.get_request_status(request_id)

    if not status_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Request {request_id} not found.",
        )

    if status_info.status not in [RequestStatus.PROCESSING, RequestStatus.RECEIVED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Request {request_id} is not in a cancellable state (Status: {status_info.status.value}).",
        )

    if not status_info.task_details:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Request {request_id} has no associated task details for cancellation.",
        )

    db_type = status_info.task_details.get("db_type")
    db_query_id = status_info.task_details.get(
        "db_query_id"
    )  # Could be CH query_id or PG PID

    if not db_type or not db_query_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Incomplete task details for request {request_id}. Cannot determine cancellation target.",
        )

    cancel_success = False
    cancel_error = None

    try:
        if db_type == DatabaseType.CLICKHOUSE.value:
            log.info(
                f"Attempting to cancel ClickHouse query_id: {db_query_id} for request {request_id}"
            )
            # Note: ClickHouse KILL QUERY might return success even if query already finished.
            # It requires specific privileges.
            await ClickHouseHandler.execute_command(  # Use command, KILL QUERY doesn't return rows typically
                command="KILL QUERY WHERE query_id = %(qid)s SYNC",  # Use SYNC for confirmation
                params={"qid": db_query_id},
                request_id=request_id,  # Pass context
            )
            log.info(f"Sent KILL QUERY command for ClickHouse query_id: {db_query_id}")
            cancel_success = True  # Assume sent is success for now

        elif db_type == DatabaseType.POSTGRES.value:
            # Ensure db_query_id is likely an integer PID
            try:
                pid = int(db_query_id)
                log.info(
                    f"Attempting to cancel PostgreSQL PID: {pid} for request {request_id}"
                )
                # pg_cancel_backend returns bool
                result, _ = await PostgresHandler.execute_query(
                    query="SELECT pg_cancel_backend(%(pid)s)",
                    params={"pid": pid},
                    request_id=request_id,  # Pass context
                    allow_write=True,  # pg_cancel_backend is technically not SELECT only
                )
                if result and result[0].get("pg_cancel_backend") is True:
                    log.info(f"Successfully canceled PostgreSQL PID: {pid}")
                    cancel_success = True
                else:
                    log.warning(
                        f"pg_cancel_backend returned false or unexpected result for PID: {pid}"
                    )
                    cancel_error = f"Failed to cancel PostgreSQL PID {pid} (may have already finished)."
            except ValueError:
                cancel_error = (
                    f"Invalid PostgreSQL PID format in task details: {db_query_id}"
                )
            except PostgresException as pg_err:
                log.error(
                    f"Error executing pg_cancel_backend for PID {db_query_id}: {pg_err}"
                )
                cancel_error = f"Error canceling PostgreSQL PID {db_query_id}: {pg_err}"

        # Add other task types (e.g., script cancellation) here if needed

        else:
            cancel_error = f"Unsupported task type for cancellation: {db_type}"

    except (ClickHouseException, PostgresException) as db_err:
        log.error(
            f"Database error during cancellation attempt for request {request_id}: {db_err}",
            exc_info=True,
        )
        cancel_error = f"Database error during cancellation: {db_err}"
    except Exception as e:
        log.error(
            f"Unexpected error during cancellation attempt for request {request_id}: {e}",
            exc_info=True,
        )
        cancel_error = f"Unexpected error during cancellation: {e}"

    # Update tracking log status *after* attempting cancellation
    if cancel_success:
        await tracker.cancel_request_task(request_id, reason="Canceled via API")
        return {
            "detail": f"Cancellation signal sent for task associated with request {request_id}."
        }
    else:
        # Don't mark as canceled in tracker if attempt failed
        final_error = cancel_error or "Cancellation failed for unknown reason."
        log.error(f"Cancellation failed for request {request_id}: {final_error}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,  # Or 400 if user error?
            detail=f"Failed to cancel task for request {request_id}: {final_error}",
        )


# --- Remove old query listing/history endpoints ---
# @router.get("/queries", ...) ... REMOVE
# @router.get("/query-history", ...) ... REMOVE
