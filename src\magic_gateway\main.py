# main.py (example)
import os
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware
from starlette.staticfiles import StaticFiles

from magic_gateway.api.v1.routes import api_router
from magic_gateway.core.config import settings
from magic_gateway.core.lifespan import lifespan
from magic_gateway.core.logging_config import setup_logging
from magic_gateway.api.middleware.request_tracking import RequestTrackingMiddleware
from magic_gateway.api.middleware.token_refresh import TokenRefreshMiddleware

# Setup logging first
setup_logging()

# Create FastAPI app with lifespan context manager
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    debug=settings.DEBUG,
    lifespan=lifespan,  # Use the lifespan manager
)

# --- Add Middleware ---
# CORS middleware should generally come first
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["Content-Disposition"],
    )

# Add the Token Refresh Middleware before Request Tracking
app.add_middleware(TokenRefreshMiddleware)

# Add the Request Tracking Middleware *after* CORS but potentially before others
# depending on what needs to be tracked.
app.add_middleware(RequestTrackingMiddleware)

# --- Include Routers ---
app.include_router(api_router, prefix="/api/v1")

# --- Mount static files ---
# Get the directory of the current file
current_dir = os.path.dirname(os.path.abspath(__file__))
static_dir = os.path.join(current_dir, "static")

# Create the static directory if it doesn't exist
os.makedirs(static_dir, exist_ok=True)

# Mount the static directory
app.mount("/static", StaticFiles(directory=static_dir), name="static")


# --- Root endpoint ---
@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.APP_NAME} v{settings.APP_VERSION}"}


# --- Run with Uvicorn (if running directly) ---
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,  # Enable reload only in debug
        log_level=settings.LOG_LEVEL.lower(),
    )
