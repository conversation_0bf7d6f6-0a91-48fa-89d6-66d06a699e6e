"""Unit tests for authentication."""

import pytest
from jose import jwt

from magic_gateway.auth.jwt import (
    create_access_token,
    create_refresh_token,
    decode_token,
    verify_access_token,
    verify_refresh_token,
)
from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import JWTException


def test_create_access_token():
    """Test creating an access token."""
    # Arrange
    data = {"sub": "test_user", "is_admin": False}
    
    # Act
    token = create_access_token(data)
    
    # Assert
    assert token is not None
    assert isinstance(token, str)
    
    # Decode token
    payload = jwt.decode(
        token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
    )
    
    assert payload["sub"] == "test_user"
    assert payload["is_admin"] is False
    assert payload["type"] == "access"
    assert "exp" in payload
    assert "iat" in payload


def test_create_refresh_token():
    """Test creating a refresh token."""
    # Arrange
    data = {"sub": "test_user", "is_admin": False}
    
    # Act
    token = create_refresh_token(data)
    
    # Assert
    assert token is not None
    assert isinstance(token, str)
    
    # Decode token
    payload = jwt.decode(
        token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
    )
    
    assert payload["sub"] == "test_user"
    assert payload["is_admin"] is False
    assert payload["type"] == "refresh"
    assert "exp" in payload
    assert "iat" in payload


def test_verify_access_token():
    """Test verifying an access token."""
    # Arrange
    data = {"sub": "test_user", "is_admin": False}
    token = create_access_token(data)
    
    # Act
    payload = verify_access_token(token)
    
    # Assert
    assert payload["sub"] == "test_user"
    assert payload["is_admin"] is False
    assert payload["type"] == "access"


def test_verify_refresh_token():
    """Test verifying a refresh token."""
    # Arrange
    data = {"sub": "test_user", "is_admin": False}
    token = create_refresh_token(data)
    
    # Act
    payload = verify_refresh_token(token)
    
    # Assert
    assert payload["sub"] == "test_user"
    assert payload["is_admin"] is False
    assert payload["type"] == "refresh"


def test_verify_access_token_with_refresh_token():
    """Test verifying an access token with a refresh token."""
    # Arrange
    data = {"sub": "test_user", "is_admin": False}
    token = create_refresh_token(data)
    
    # Act & Assert
    with pytest.raises(JWTException):
        verify_access_token(token)


def test_verify_refresh_token_with_access_token():
    """Test verifying a refresh token with an access token."""
    # Arrange
    data = {"sub": "test_user", "is_admin": False}
    token = create_access_token(data)
    
    # Act & Assert
    with pytest.raises(JWTException):
        verify_refresh_token(token)
