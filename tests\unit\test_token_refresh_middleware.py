"""Tests for token refresh middleware."""

import pytest
import json
from unittest.mock import <PERSON><PERSON>, AsyncMock
from fastapi import Request, Response
from fastapi.responses import JSONResponse

from magic_gateway.api.middleware.token_refresh import TokenRefreshMiddleware


class TestTokenRefreshMiddleware:
    """Test cases for token refresh middleware."""

    @pytest.fixture
    def middleware(self):
        """Create a token refresh middleware instance."""
        return TokenRefreshMiddleware(app=Mock())

    @pytest.fixture
    def mock_request(self):
        """Create a mock request object."""
        request = Mock(spec=Request)
        request.state = Mock()
        return request

    @pytest.fixture
    def mock_response(self):
        """Create a mock JSON response."""
        response_data = {"message": "success", "data": {"key": "value"}}
        response = JSONResponse(content=response_data)
        return response

    @pytest.mark.asyncio
    async def test_no_token_refresh(self, middleware, mock_request, mock_response):
        """Test middleware when no token refresh occurs."""
        # Mock call_next to return the response
        async def mock_call_next(request):
            return mock_response
        
        result = await middleware.dispatch(mock_request, mock_call_next)
        
        # Response should be unchanged
        assert result == mock_response
        assert "X-New-Access-Token" not in result.headers
        assert "X-New-Refresh-Token" not in result.headers

    @pytest.mark.asyncio
    async def test_token_refresh_headers_added(self, middleware, mock_request, mock_response):
        """Test middleware adds token refresh headers when tokens are refreshed."""
        # Set new tokens in request state
        mock_request.state.new_access_token = "new_access_token_123"
        mock_request.state.new_refresh_token = "new_refresh_token_456"
        
        # Mock call_next to return the response
        async def mock_call_next(request):
            return mock_response
        
        result = await middleware.dispatch(mock_request, mock_call_next)
        
        # Headers should be added
        assert result.headers["X-New-Access-Token"] == "new_access_token_123"
        assert result.headers["X-New-Refresh-Token"] == "new_refresh_token_456"

    @pytest.mark.asyncio
    async def test_json_response_body_updated(self, middleware, mock_request):
        """Test middleware updates JSON response body with token info."""
        # Set new tokens in request state
        mock_request.state.new_access_token = "new_access_token_123"
        mock_request.state.new_refresh_token = "new_refresh_token_456"
        
        # Create a JSON response
        original_data = {"message": "success", "data": {"key": "value"}}
        response = JSONResponse(content=original_data)
        
        # Mock call_next to return the response
        async def mock_call_next(request):
            return response
        
        result = await middleware.dispatch(mock_request, mock_call_next)
        
        # Headers should be added
        assert result.headers["X-New-Access-Token"] == "new_access_token_123"
        assert result.headers["X-New-Refresh-Token"] == "new_refresh_token_456"
        
        # Response body should include token info
        response_data = json.loads(result.body.decode())
        assert response_data["_token_refreshed"] is True
        assert response_data["_new_access_token"] == "new_access_token_123"
        assert response_data["_new_refresh_token"] == "new_refresh_token_456"
        
        # Original data should still be present
        assert response_data["message"] == "success"
        assert response_data["data"]["key"] == "value"

    @pytest.mark.asyncio
    async def test_non_json_response_unchanged(self, middleware, mock_request):
        """Test middleware doesn't modify non-JSON responses."""
        # Set new tokens in request state
        mock_request.state.new_access_token = "new_access_token_123"
        mock_request.state.new_refresh_token = "new_refresh_token_456"
        
        # Create a plain text response
        from fastapi.responses import PlainTextResponse
        response = PlainTextResponse("Plain text response")
        
        # Mock call_next to return the response
        async def mock_call_next(request):
            return response
        
        result = await middleware.dispatch(mock_request, mock_call_next)
        
        # Headers should be added
        assert result.headers["X-New-Access-Token"] == "new_access_token_123"
        assert result.headers["X-New-Refresh-Token"] == "new_refresh_token_456"
        
        # Response body should be unchanged
        assert result.body == b"Plain text response"

    @pytest.mark.asyncio
    async def test_middleware_handles_exceptions(self, middleware, mock_request, mock_response):
        """Test middleware handles exceptions gracefully."""
        # Set new tokens in request state
        mock_request.state.new_access_token = "new_access_token_123"
        mock_request.state.new_refresh_token = "new_refresh_token_456"
        
        # Mock call_next to raise an exception
        async def mock_call_next(request):
            raise Exception("Test exception")
        
        # Middleware should propagate the exception
        with pytest.raises(Exception, match="Test exception"):
            await middleware.dispatch(mock_request, mock_call_next)

    @pytest.mark.asyncio
    async def test_partial_token_state(self, middleware, mock_request, mock_response):
        """Test middleware when only one token is set in state."""
        # Set only access token (missing refresh token)
        mock_request.state.new_access_token = "new_access_token_123"
        
        # Mock call_next to return the response
        async def mock_call_next(request):
            return mock_response
        
        result = await middleware.dispatch(mock_request, mock_call_next)
        
        # No headers should be added when both tokens are not present
        assert "X-New-Access-Token" not in result.headers
        assert "X-New-Refresh-Token" not in result.headers