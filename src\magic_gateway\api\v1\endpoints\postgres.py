# --- START OF FILE magic_gateway/api/v1/endpoints/postgres.py ---

import uuid
from typing import Any, Dict, List, Optional

# Import Request for injection
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query

# Import new dependencies and models
from magic_gateway.api.deps import (
    get_request_tracker_service,
    track_request_details,
    update_request_context,
)
from magic_gateway.tracking.service import RequestTrackingService
from magic_gateway.tracking.models import (
    QueryRequest,  # Keep request models
    CommandRequest,
    RequestStatusResponse,  # Use new status response model
    DatabaseType,  # Keep this if needed for task_details
)

# Import API models if needed
# Define a simplified QueryResult for consistent response
from pydantic import BaseModel, Field

from magic_gateway.auth.dependencies import get_current_active_user
from magic_gateway.core.exceptions import PostgresException, ForbiddenException
from magic_gateway.core.logging_config import log
from magic_gateway.db.postgres_handler import <PERSON>gresHand<PERSON>

# Removed old QueryTracker dependencies

router = APIRouter()


# Define a consistent QueryResult model if desired
class QueryResult(BaseModel):
    request_id: uuid.UUID
    rows: List[Dict[str, Any]]
    columns: List[str]
    row_count: int
    database_type: DatabaseType = DatabaseType.POSTGRES
    status: str = "completed"


# Model for axis info request
class AxisInfoRequest(BaseModel):
    axis_name: str = Field(
        ...,
        description="The full name of the axis to retrieve information for (format: schema.axis_name)",
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


# Model for a single axis label value
class AxisLabelValue(BaseModel):
    position_number: int
    value_group_name: Optional[str] = None


# Model for axis info response
class AxisInfoResponse(BaseModel):
    request_id: uuid.UUID
    axis_id: int
    axis_name: str
    axis_positions: int
    labels: List[AxisLabelValue] = []
    status: str = "completed"


# Model for axis label values request
class AxisLabelValuesRequest(BaseModel):
    axis_id: int = Field(
        ..., description="The ID of the axis to retrieve label values for"
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


# Model for a single axis label value
class AxisLabelValue(BaseModel):
    position_number: int
    value_group_name: Optional[str] = None


# Model for axis label values response
class AxisLabelValuesResponse(BaseModel):
    request_id: uuid.UUID
    axis_id: int
    labels: List[AxisLabelValue]
    status: str = "completed"


# Model for object description request
class ObjectDescriptionRequest(BaseModel):
    object_name: str = Field(
        ..., description="Full object name including schema (e.g., 'schema.table_name')"
    )
    object_type: Optional[str] = Field(
        None,
        description="Optional type of object ('table', 'view', 'materialized_view')",
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


# Model for object description response
class ObjectDescriptionResponse(BaseModel):
    request_id: uuid.UUID
    database: str
    schema_name: str  # Renamed from 'schema' to avoid shadowing BaseModel attribute
    name: str
    type: str
    ddl: str
    status: str = "completed"


@router.post("/command")
async def execute_postgres_command(
    request: Request,  # Inject Request
    command_request: CommandRequest,
    request_id: uuid.UUID = Depends(track_request_details),  # Get request_id
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Any:
    """
    Execute a command (INSERT, UPDATE, DELETE, DDL) against PostgreSQL.
    Requires appropriate permissions based on the command.
    """
    # Update tracker with username once authenticated
    await update_request_context(request, username=current_user["username"])

    # Add command preview to task details (optional)
    task_details = {
        "db_type": DatabaseType.POSTGRES.value,
        "task_subtype": "command",
        "command_preview": command_request.command[:200],
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Security: Add checks here if certain commands need admin privileges
        # e.g., if command_request.command.strip().upper().startswith(("DROP", "TRUNCATE", "ALTER")) and not current_user.get("is_admin"):
        #    raise ForbiddenException("Admin privileges required for this command type.")

        # Execute the command, passing request_id for logging context
        await PostgresHandler.execute_command(
            command=command_request.command,
            params=command_request.params,
            request_id=request_id,  # Pass request_id
        )

        # Middleware handles completion logging (status 200 OK)
        return {
            "status": "success",
            "message": "Command executed successfully",
            "request_id": request_id,
        }
    except ForbiddenException as e:
        # Let middleware handle logging this as 403
        raise e
    except PostgresException as e:
        # Middleware will log failure details based on exception (likely 400)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware will catch and log this as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error executing PostgreSQL command: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Command execution failed: {e}",
        )


@router.post("/query", response_model=QueryResult)
async def execute_postgres_query(
    request: Request,  # Inject Request
    query_request: QueryRequest,
    request_id: uuid.UUID = Depends(track_request_details),  # Get request_id
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Any:
    """Execute a read-only query (SELECT) against PostgreSQL."""
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # For potential cancellation, store the backend PID if possible.
    # This requires modification in PostgresHandler to fetch and return/store the PID.
    # For now, store query preview and type.
    task_details = {
        "db_type": DatabaseType.POSTGRES.value,
        "task_subtype": "query",
        "query_preview": query_request.query[:200],
        # "db_query_id": pid # Placeholder - PID needs to be captured during execution
    }
    await update_request_context(request, task_details=task_details)

    try:
        # --- Get PID during execution (Conceptual change in Handler needed) ---
        # We need to modify `PostgresHandler.execute_query` or wrap it
        # to get the connection PID and store it in request.state
        # Example (Conceptual - requires Handler changes):
        # async def get_pid_and_execute(*args, **kwargs):
        #     async with postgres_connection_manager.connection() as conn:
        #         pid = conn.info.backend_pid
        #         request.state.current_pg_pid = pid # Store PID in state
        #         # Update task_details in DB *now* if cancellation is critical
        #         task_details["db_query_id"] = str(pid)
        #         await tracker.add_task_details(request_id, task_details)
        #         # Execute the actual query using the same connection
        #         return await PostgresHandler._execute_query_on_connection(conn, *args, **kwargs)
        # rows, columns = await get_pid_and_execute(...)

        # --- Current Implementation (No automatic PID capture) ---
        rows, columns = await PostgresHandler.execute_query(
            query=query_request.query,
            params=query_request.params,
            request_id=request_id,  # Pass request_id for logging
            allow_write=query_request.allow_write,  # Pass allow_write flag
        )

        # If PID was captured and stored in state earlier:
        # pid = getattr(request.state, 'current_pg_pid', None)
        # if pid and not task_details.get("db_query_id"): # Check if not already updated
        #    task_details["db_query_id"] = str(pid)
        #    await update_request_context(request, task_details=task_details)

        # Middleware handles completion logging (status 200 OK)
        return QueryResult(
            request_id=request_id,
            rows=rows,
            columns=columns,
            row_count=len(rows),
            # database_type already set in model default
        )
    except PostgresException as e:
        # Includes allow_write=False check failure from handler
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Query execution error: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Query execution failed: {e}",
        )


@router.post("/ctlg_measures/", response_model=QueryResult)
async def get_ctlg_measures(
    # request: Request,  # Inject Request
    request_id: uuid.UUID = Depends(track_request_details),
    is_supported: Optional[bool] = Query(
        None, description="Check if the feature is supported"
    ),
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Any:
    measures_query = """SELECT * FROM metadata.ctlg_measures"""
    if is_supported is not None:
        measures_query += f'\nWHERE "is_supported" = {is_supported}'

    try:
        rows, columns = await PostgresHandler.execute_query(
            query=measures_query,
            request_id=request_id,  # Pass request_id for logging
            allow_write=False,  # Pass allow_write flag
        )

        # If PID was captured and stored in state earlier:
        # pid = getattr(request.state, 'current_pg_pid', None)
        # if pid and not task_details.get("db_query_id"): # Check if not already updated
        #    task_details["db_query_id"] = str(pid)
        #    await update_request_context(request, task_details=task_details)

        # Middleware handles completion logging (status 200 OK)

        return QueryResult(
            request_id=request_id,
            rows=rows,
            columns=columns,
            row_count=len(rows),
            # database_type already set in model default
        )
    except PostgresException as e:
        # Includes allow_write=False check failure from handler
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        # print(e)
        # log.error(
        #    f"ReqID: {str(request_id)[:8]} - Query execution error: {e}", exc_info=True
        # )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Query execution failed: {e}",
        )


# --- Remove old status and cancel endpoints based on query_id ---
# @router.get("/query/{query_id}", response_model=QueryStatusResponse) ... REMOVED
# @router.delete("/query/{query_id}") ... REMOVED


@router.post("/object-ddl", response_model=ObjectDescriptionResponse)
async def get_object_ddl(
    request: Request,
    description_request: ObjectDescriptionRequest,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> Any:
    """Retrieve DDL (Data Definition Language) statement and description of a PostgreSQL table or view."""
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "db_type": DatabaseType.POSTGRES.value,
        "task_subtype": "object_ddl",
        "object_name": description_request.object_name,
        "object_type": description_request.object_type,
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Get object DDL with custom timeout if provided
        custom_timeout = description_request.timeout_seconds
        if custom_timeout:
            log.info(
                f"Using custom timeout of {custom_timeout}s for object DDL retrieval"
            )
            task_details["custom_timeout"] = custom_timeout
            await update_request_context(request, task_details=task_details)

        result = await PostgresHandler.get_object_description(
            object_name=description_request.object_name,
            object_type=description_request.object_type,
            request_id=request_id,  # Pass request_id for tracking
            timeout_seconds=custom_timeout,  # Pass custom timeout if provided
        )

        # Add request_id to the response
        result["request_id"] = request_id

        return ObjectDescriptionResponse(**result)
    except PostgresException as e:
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error retrieving object description: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve object description: {e}",
        )


@router.delete("/cancel-request/{request_id}", response_model=Dict[str, Any])
async def cancel_postgres_request(
    request_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> Any:
    """Cancel a running PostgreSQL request by its request ID."""
    try:
        # Log the cancellation attempt
        log.info(f"Attempting to cancel PostgreSQL request with ID: {request_id}")

        # Execute a PostgreSQL command to cancel any query associated with this request ID
        cancel_query = """
        SELECT pg_cancel_backend(pid)
        FROM pg_stat_activity
        WHERE query LIKE %(request_pattern)s
        """

        params = {"request_pattern": f"%-- ReqID: {request_id}%"}

        # Execute the cancellation query
        result, _ = await PostgresHandler.execute_query(
            cancel_query,
            params,
            allow_write=True,  # Allow this query to modify state
        )

        # Check if any query was cancelled
        cancelled_count = sum(
            1 for row in result if row.get("pg_cancel_backend") is True
        )

        if cancelled_count > 0:
            log.info(
                f"Successfully cancelled {cancelled_count} PostgreSQL queries for request ID: {request_id}"
            )
            return {
                "status": "success",
                "message": f"Cancelled {cancelled_count} queries for request ID: {request_id}",
            }
        else:
            log.warning(
                f"No active PostgreSQL queries found for request ID: {request_id}"
            )
            return {
                "status": "warning",
                "message": f"No active queries found for request ID: {request_id}",
            }

    except PostgresException as e:
        log.error(f"Failed to cancel PostgreSQL request {request_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to cancel request: {e}",
        )
    except Exception as e:
        log.error(
            f"Unexpected error cancelling PostgreSQL request {request_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error cancelling request: {e}",
        )


@router.post("/axis-info", response_model=AxisInfoResponse)
async def get_axis_info(
    request: Request,
    info_request: AxisInfoRequest,
    request_id: uuid.UUID = Depends(track_request_details),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> Any:
    """Retrieve axis information and labels from the metadata.axis_data table."""
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # Store task details
    task_details = {
        "db_type": DatabaseType.POSTGRES.value,
        "task_subtype": "axis_info",
        "axis_name": info_request.axis_name,
    }
    await update_request_context(request, task_details=task_details)

    try:
        # Get custom timeout if provided
        custom_timeout = info_request.timeout_seconds
        if custom_timeout:
            log.info(
                f"Using custom timeout of {custom_timeout}s for axis info retrieval"
            )
            task_details["custom_timeout"] = custom_timeout
            await update_request_context(request, task_details=task_details)

        # Parse the full axis name to extract schema and axis name
        full_axis_name = info_request.axis_name
        parts = full_axis_name.split(".")

        if len(parts) != 2:
            raise PostgresException(
                f"Invalid axis name format: {full_axis_name}. Expected format: schema.axis_name"
            )

        schema_name, axis_name = parts

        # Construct a single query to get both axis information and labels
        query = f"""
        WITH axis_info AS (
            SELECT
                id_axis,
                '{full_axis_name}' as axis_name,
                count(distinct position_number) as axis_positions
            FROM metadata.axis_data
            WHERE id_axis in (
                select distinct id_axis
                FROM metadata.ctlg_axis
                WHERE axis_name = %(axis_name)s
                AND clientdb_schema = %(schema_name)s
            )
            GROUP BY id_axis
        )
        SELECT
            a.id_axis,
            a.axis_name,
            a.axis_positions,
            d.position_number,
            d.value_group_name,
            d.feature  -- Include feature column as fallback for empty value_group_name
        FROM axis_info a
        JOIN metadata.axis_data d ON a.id_axis = d.id_axis
        ORDER BY d.position_number
        """

        params = {"axis_name": axis_name, "schema_name": schema_name}

        # Execute the query
        results, _ = await PostgresHandler.execute_query(
            query=query,
            params=params,
            request_id=request_id,
            timeout_seconds=custom_timeout,
        )

        if not results or len(results) == 0:
            raise PostgresException(
                f"No axis found with name: {full_axis_name} (schema: {schema_name}, axis: {axis_name})"
            )

        # Extract axis information from the first row
        first_row = results[0]
        axis_id = first_row["id_axis"]
        axis_name = first_row["axis_name"]
        axis_positions = first_row["axis_positions"]

        # Check if axis_id is None (no matching axis found)
        if axis_id is None:
            raise PostgresException(
                f"No axis found with name: {full_axis_name} (schema: {schema_name}, axis: {axis_name})"
            )

        # Convert the results to dictionaries for serialization
        labels = []
        for row in results:
            # Use feature as fallback when value_group_name is None or empty
            value_group_name = row["value_group_name"]
            feature = row.get(
                "feature"
            )  # Use get() to handle case where feature column might not exist

            # If value_group_name is None or empty string, use feature as fallback
            if (value_group_name is None or value_group_name == "") and feature:
                value_group_name = feature
                log.debug(
                    f"ReqID: {str(request_id)[:8]} - Using feature '{feature}' as fallback for position {row['position_number']}"
                )

            labels.append(
                {
                    "position_number": row["position_number"],
                    "value_group_name": value_group_name,
                }
            )

        # Log if we found any NULL value_group_name values after fallback
        null_labels = [label for label in labels if label["value_group_name"] is None]
        if null_labels:
            log.warning(
                f"ReqID: {str(request_id)[:8]} - Found {len(null_labels)} axis labels with NULL value_group_name after feature fallback"
            )

        # Return the response
        return AxisInfoResponse(
            request_id=request_id,
            axis_id=axis_id,
            axis_name=info_request.axis_name,
            axis_positions=axis_positions,
            labels=labels,
        )
    except PostgresException as e:
        # Middleware logs as 400
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware logs as 500
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error retrieving axis info: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve axis info: {e}",
        )


# --- END OF FILE magic_gateway/api/v1/endpoints/postgres.py ---
