"""
Connection Pool Adapter for export operations.

This module provides a unified interface for selecting and using ClickHouse connection
pools based on table routing logic, with built-in fallback mechanisms.
"""

import asyncio
import time
import uuid
from typing import AsyncGenerator, Optional
from urllib.parse import urlencode

import aiohttp

from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import (
    ClickHouseConnectionManager,
    ClickHouseConnectionRegistry,
    clickhouse_registry,
)
from magic_gateway.export.exceptions import (
    ConnectionPoolExhaustedError,
    ConnectionTimeoutError,
    ExportError,
    ExportErrorContext,
)
from magic_gateway.export.models import ConnectionSelection
from magic_gateway.utils.db_utils import get_cluster_for_table


class ConnectionPoolAdapter:
    """
    Adapter for ClickHouse connection pool selection and management.
    
    This class provides a unified interface for selecting the optimal connection
    pool based on table routing logic and handles fallback scenarios gracefully.
    """

    def __init__(self, registry: Optional[ClickHouseConnectionRegistry] = None):
        """
        Initialize the connection pool adapter.
        
        Args:
            registry: Optional registry instance. If None, uses the global registry.
        """
        self.registry = registry or clickhouse_registry

    async def get_optimal_connection(
        self, 
        table_name: str,
        request_id: Optional[str] = None
    ) -> ConnectionSelection:
        """
        Select optimal connection pool based on table routing logic.
        
        This method analyzes the table name to determine which cluster should be used,
        applies the routing logic, and provides fallback to the primary connection
        if the cluster connection is not available.
        
        Args:
            table_name: Fully qualified table name (e.g., "database.table")
            request_id: Optional request ID for error tracking
            
        Returns:
            ConnectionSelection containing the selected manager and metadata
            
        Raises:
            ExportError: If no suitable connection can be found
        """
        if not self.registry.initialized:
            raise ExportError(
                error_type="registry_not_initialized",
                message="ClickHouse connection registry is not initialized",
                context={"table_name": table_name},
                request_id=request_id or str(uuid.uuid4()),
                recovery_suggestions=[
                    "Wait for the application to fully initialize",
                    "Check application startup logs for initialization errors"
                ]
            )

        # Determine target cluster based on table routing
        target_cluster = get_cluster_for_table(table_name)
        
        # Try to get the target cluster connection first
        if target_cluster:
            try:
                cluster_manager = self.registry.get_manager(target_cluster)
                
                # Validate that the cluster manager is properly initialized
                if cluster_manager.initialized:
                    log.debug(
                        f"Selected cluster connection '{target_cluster}' for table '{table_name}'"
                    )
                    return ConnectionSelection(
                        manager=cluster_manager,
                        cluster_name=target_cluster,
                        is_fallback=False,
                        routing_reason=f"Table routing: database '{table_name.split('.')[0] if '.' in table_name else 'unknown'}' routes to cluster '{target_cluster}'"
                    )
                else:
                    log.warning(
                        f"Cluster connection '{target_cluster}' is not initialized, falling back to primary"
                    )
                    # Mark that cluster was not initialized for better error messaging
                    self._cluster_not_initialized = True
            except Exception as e:
                log.warning(
                    f"Failed to get cluster connection '{target_cluster}' for table '{table_name}': {e}. "
                    f"Falling back to primary connection."
                )

        # Fallback to primary connection
        try:
            primary_manager = self.registry.get_manager("primary")
            
            if not primary_manager.initialized:
                # If we're here because no cluster was specified, this is a primary connection issue
                # If we're here because cluster failed, this is still a primary connection issue
                raise ExportError(
                    error_type="primary_connection_unavailable",
                    message="Primary ClickHouse connection is not initialized",
                    context={
                        "table_name": table_name,
                        "target_cluster": target_cluster,
                        "available_clusters": self.registry.get_available_clusters()
                    },
                    request_id=request_id or str(uuid.uuid4()),
                    recovery_suggestions=[
                        "Check ClickHouse server connectivity",
                        "Verify ClickHouse configuration settings",
                        "Check application logs for connection errors"
                    ]
                )
            
            fallback_reason = (
                f"Fallback to primary: cluster '{target_cluster}' unavailable"
                if target_cluster
                else f"Primary connection: no cluster routing for table '{table_name}'"
            )
            
            # If we got here because cluster was not initialized, update the reason
            if target_cluster and hasattr(self, '_cluster_not_initialized'):
                fallback_reason = f"Fallback to primary: cluster '{target_cluster}' not initialized"
                delattr(self, '_cluster_not_initialized')
            
            log.debug(f"Selected primary connection for table '{table_name}' ({fallback_reason})")
            
            return ConnectionSelection(
                manager=primary_manager,
                cluster_name=None,
                is_fallback=bool(target_cluster),
                routing_reason=fallback_reason
            )
            
        except ExportError:
            # Re-raise ExportError exceptions as-is
            raise
        except Exception as e:
            available_clusters = self.registry.get_available_clusters()
            raise ExportError(
                error_type="no_connection_available",
                message=f"No ClickHouse connection available for table '{table_name}'",
                context={
                    "table_name": table_name,
                    "target_cluster": target_cluster,
                    "available_clusters": available_clusters,
                    "error": str(e)
                },
                request_id=request_id or str(uuid.uuid4()),
                recovery_suggestions=[
                    "Check ClickHouse server connectivity",
                    "Verify connection pool configuration",
                    "Check application startup logs for initialization errors"
                ]
            ) from e

    async def validate_connection_health(
        self, 
        connection_manager: ClickHouseConnectionManager,
        request_id: Optional[str] = None
    ) -> bool:
        """
        Validate that a connection manager is healthy and ready for use.
        
        Args:
            connection_manager: The connection manager to validate
            request_id: Optional request ID for error tracking
            
        Returns:
            True if the connection is healthy, False otherwise
        """
        try:
            # Check if the manager is initialized
            if not connection_manager.initialized:
                log.warning("Connection manager is not initialized")
                return False
            
            # Test the connection with a simple query
            async with connection_manager.connection() as conn:
                # Use a lightweight query to test connectivity
                result = conn.execute("SELECT 1 as health_check")
                if result and len(result) > 0 and result[0][0] == 1:
                    log.debug("Connection health check passed")
                    return True
                else:
                    log.warning("Connection health check returned unexpected result")
                    return False
                    
        except Exception as e:
            log.warning(f"Connection health check failed: {e}")
            return False

    def get_available_clusters(self) -> list[str]:
        """
        Get list of available cluster names from the registry.
        
        Returns:
            List of available cluster names
        """
        if not self.registry.initialized:
            return []
        return self.registry.get_available_clusters()

    def get_connection_pool_status(self) -> dict:
        """
        Get detailed status information about all connection pools.
        
        Returns:
            Dictionary containing detailed pool status information
        """
        status = {
            "registry_initialized": self.registry.initialized,
            "available_clusters": [],
            "pool_details": {},
            "timestamp": time.time()
        }
        
        if not self.registry.initialized:
            return status
            
        try:
            available_clusters = self.registry.get_available_clusters()
            status["available_clusters"] = available_clusters
            
            for cluster_name in available_clusters:
                try:
                    manager = self.registry.get_manager(cluster_name)
                    pool_info = {
                        "cluster_name": cluster_name,
                        "initialized": manager.initialized,
                        "is_primary": cluster_name == "primary"
                    }
                    
                    # Try to get additional pool information if available
                    if hasattr(manager, 'pool') and manager.pool:
                        pool_info.update({
                            "pool_size": getattr(manager.pool, 'size', 'unknown'),
                            "pool_maxsize": getattr(manager.pool, 'maxsize', 'unknown'),
                            "pool_freesize": getattr(manager.pool, 'freesize', 'unknown')
                        })
                    
                    status["pool_details"][cluster_name] = pool_info
                    
                except Exception as e:
                    status["pool_details"][cluster_name] = {
                        "cluster_name": cluster_name,
                        "error": str(e),
                        "status": "error"
                    }
                    
        except Exception as e:
            status["error"] = str(e)
            
        return status

    async def get_connection_with_status_logging(
        self,
        table_name: str,
        request_id: str,
        operation: str
    ) -> ConnectionSelection:
        """
        Get optimal connection with comprehensive status logging.
        
        This method wraps get_optimal_connection with detailed logging
        and error context for better debugging and monitoring.
        
        Args:
            table_name: Fully qualified table name
            request_id: Request ID for tracking
            operation: Description of the operation being performed
            
        Returns:
            ConnectionSelection with detailed logging
            
        Raises:
            ExportError: Enhanced with connection pool status information
        """
        with ExportErrorContext(request_id, operation, connection_pool_adapter=self) as ctx:
            ctx.add_context("table_name", table_name)
            
            # Log initial connection pool status
            pool_status = self.get_connection_pool_status()
            log.debug(
                f"Connection pool status for {operation} (request {request_id}): "
                f"Registry initialized: {pool_status['registry_initialized']}, "
                f"Available clusters: {pool_status['available_clusters']}"
            )
            
            try:
                connection_selection = await self.get_optimal_connection(table_name, request_id)
                
                # Log successful connection selection
                log.info(
                    f"Successfully selected connection for {operation} (request {request_id}): "
                    f"{connection_selection.routing_reason}"
                )
                
                ctx.add_context("connection_selection", {
                    "cluster_name": connection_selection.cluster_name,
                    "is_fallback": connection_selection.is_fallback,
                    "routing_reason": connection_selection.routing_reason
                })
                
                return connection_selection
                
            except ExportError as e:
                # Enhance the error with current pool status
                current_pool_status = self.get_connection_pool_status()
                e.connection_pool_status = current_pool_status
                
                log.error(
                    f"Failed to get connection for {operation} (request {request_id}): {e}. "
                    f"Pool status: {current_pool_status}"
                )
                
                raise

    async def export_to_parquet_stream(
        self,
        query: str,
        connection_manager: ClickHouseConnectionManager,
        query_id: str,
        request_id: Optional[str] = None
    ) -> AsyncGenerator[bytes, None]:
        """
        Stream parquet data directly from ClickHouse using connection pool configuration.
        
        This method provides direct streaming of parquet data without creating
        temporary files, optimizing memory usage for large datasets.
        
        Implementation Note:
        Currently uses ClickHouse HTTP interface for FORMAT Parquet streaming because:
        1. The clickhouse-driver library doesn't support FORMAT clauses with execute_iter
        2. HTTP interface is the recommended approach for parquet streaming
        3. We still leverage the connection pool for configuration and health checks
        
        Future Enhancement:
        If clickhouse-driver adds native parquet streaming support, this method
        could be updated to use the connection pool's native client directly.
        
        Args:
            query: SQL query to execute
            connection_manager: Connection manager to use for the query
            query_id: Unique identifier for query tracking
            request_id: Optional request ID for error tracking
            
        Yields:
            Bytes of parquet data
            
        Raises:
            ConnectionPoolExhaustedError: If connection pool is exhausted
            ConnectionTimeoutError: If connection times out
            ExportError: If the parquet streaming fails
            ExportError: For other export-related errors
        """
        if not connection_manager.initialized:
            raise ExportError(
                error_type="connection_not_initialized",
                message="Connection manager is not initialized",
                context={
                    "query_id": query_id,
                    "cluster_name": getattr(connection_manager, 'cluster_name', 'unknown')
                },
                request_id=request_id or str(uuid.uuid4()),
                recovery_suggestions=[
                    "Wait for connection initialization to complete",
                    "Check connection manager startup logs"
                ]
            )

        try:
            # Validate connection health before use
            is_healthy = await self.validate_connection_health(connection_manager, request_id)
            if not is_healthy:
                raise ExportError(
                    error_type="connection_unhealthy",
                    message="Connection failed health check",
                    context={
                        "query_id": query_id,
                        "cluster_name": getattr(connection_manager, 'cluster_name', 'unknown')
                    },
                    request_id=request_id or str(uuid.uuid4()),
                    recovery_suggestions=[
                        "Retry the request",
                        "Check ClickHouse server health"
                    ]
                )

            log.debug(f"Starting parquet stream export for query {query_id}")
            
            # Use ClickHouse HTTP interface for parquet streaming while leveraging connection pool
            # This approach combines connection pool management with proper parquet streaming
            from magic_gateway.core.config import settings
            
            try:
                # Prepare the query with FORMAT Parquet
                parquet_query = f"{query.rstrip(';')} FORMAT Parquet"
                
                # Get connection details from the connection manager
                # This ensures we use the same connection configuration as the pool
                cluster_name = getattr(connection_manager, 'cluster_name', 'primary')
                
                # Extract connection details from the connection manager
                # This ensures consistency with the connection pool configuration
                if cluster_name == "primary":
                    host = connection_manager.host if hasattr(connection_manager, 'host') else settings.CLICKHOUSE_HOST
                    http_port = connection_manager.http_port if hasattr(connection_manager, 'http_port') else settings.CLICKHOUSE_HTTP_PORT
                    user = connection_manager.user if hasattr(connection_manager, 'user') else settings.CLICKHOUSE_USER
                    password = connection_manager.password if hasattr(connection_manager, 'password') else settings.CLICKHOUSE_PASSWORD
                    database = connection_manager.database if hasattr(connection_manager, 'database') else settings.CLICKHOUSE_DATABASE
                else:
                    host = connection_manager.host if hasattr(connection_manager, 'host') else settings.CLICKHOUSE_CLUSTER_HOST
                    http_port = connection_manager.http_port if hasattr(connection_manager, 'http_port') else (settings.CLICKHOUSE_CLUSTER_HTTP_PORT or settings.CLICKHOUSE_HTTP_PORT)
                    user = connection_manager.user if hasattr(connection_manager, 'user') else settings.CLICKHOUSE_CLUSTER_USER
                    password = connection_manager.password if hasattr(connection_manager, 'password') else settings.CLICKHOUSE_CLUSTER_PASSWORD
                    database = connection_manager.database if hasattr(connection_manager, 'database') else settings.CLICKHOUSE_CLUSTER_DATABASE
                
                # Prepare URL parameters
                url_params = {
                    "database": database,
                    "max_execution_time": settings.MAX_QUERY_EXECUTION_TIME,
                }
                
                if query_id:
                    url_params["query_id"] = query_id
                
                # Construct ClickHouse HTTP URL
                query_string = urlencode(url_params)
                clickhouse_url = f"http://{host}:{http_port}/?{query_string}"
                
                # Prepare authentication
                auth = aiohttp.BasicAuth(user, password)
                
                # Set up comprehensive timeout configuration
                # Add buffer time to account for HTTP overhead and response processing
                total_timeout = settings.MAX_QUERY_EXECUTION_TIME + 60  # Extra buffer for HTTP processing
                timeout = aiohttp.ClientTimeout(
                    total=total_timeout,
                    connect=30,  # Connection establishment timeout
                    sock_read=300,  # Socket read timeout (for streaming data)
                    sock_connect=10  # Socket connection timeout
                )
                
                log.debug(
                    f"Configured HTTP timeout for query {query_id}: "
                    f"total={total_timeout}s, connect=30s, sock_read=300s"
                )
                
                log.debug(f"Making HTTP request for parquet streaming to: {clickhouse_url} (using connection pool config for {cluster_name})")
                
                # Validate connection pool health before streaming
                # This ensures the connection pool is available and healthy
                try:
                    async with connection_manager.connection() as conn:
                        # Quick health check using the connection pool
                        # This validates that the connection configuration is correct
                        pass
                    log.debug(f"Connection pool health check passed for {cluster_name}")
                except Exception as pool_error:
                    log.warning(f"Connection pool health check failed for {cluster_name}: {pool_error}")
                    # Continue with HTTP streaming as fallback, but log the issue
                
                # Stream the parquet data via HTTP using connection pool configuration
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(
                        clickhouse_url,
                        data=parquet_query,
                        auth=auth,
                        headers={"Content-Type": "text/plain"}
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            error_details = await self._extract_clickhouse_http_error(
                                response.status, error_text, query_id, query
                            )
                            raise ExportError(
                                error_type=error_details["error_type"],
                                message=error_details["message"],
                                context={
                                    "query_id": query_id,
                                    "status_code": response.status,
                                    "error_response": error_text,
                                    "cluster_name": cluster_name,
                                    "connection_url": f"{host}:{http_port}",
                                    **error_details["context"]
                                },
                                request_id=request_id or str(uuid.uuid4()),
                                recovery_suggestions=error_details["recovery_suggestions"]
                            )
                        
                        # Stream the response data in chunks with intelligent chunk sizing
                        chunk_count = 0
                        chunk_size = self._calculate_optimal_chunk_size(query_id)
                        
                        log.debug(f"Using chunk size {chunk_size} bytes for query {query_id}")
                        
                        async for chunk in response.content.iter_chunked(chunk_size):
                            if chunk:
                                chunk_count += 1
                                yield chunk
                                
                                # Log progress for large datasets
                                if chunk_count % 5000 == 0:
                                    log.debug(f"Streamed {chunk_count} chunks for query {query_id}")
                        
                        log.debug(f"Streamed {chunk_count} chunks for query {query_id}")
                
                log.debug(f"Completed parquet stream export for query {query_id}")
                
            except asyncio.TimeoutError as timeout_error:
                total_timeout = settings.MAX_QUERY_EXECUTION_TIME + 60
                log.error(f"Query timeout for {query_id}: {timeout_error}")
                raise ExportError(
                    error_type="query_timeout",
                    message=f"Query execution timed out after {total_timeout} seconds",
                    context={
                        "query_id": query_id,
                        "timeout_seconds": total_timeout,
                        "max_query_time": settings.MAX_QUERY_EXECUTION_TIME,
                        "http_buffer_time": 60,
                        "cluster_name": cluster_name,
                        "connection_url": f"{host}:{http_port}",
                        "query": query[:200] + "..." if len(query) > 200 else query
                    },
                    request_id=request_id or str(uuid.uuid4()),
                    recovery_suggestions=[
                        "Try reducing the dataset size or adding filters to the query",
                        "Check if the table exists and has data",
                        "Verify ClickHouse server performance and resources",
                        f"Consider increasing MAX_QUERY_EXECUTION_TIME (currently {settings.MAX_QUERY_EXECUTION_TIME}s) if this is expected"
                    ]
                ) from timeout_error
            except aiohttp.ClientError as http_error:
                error_details = await self._extract_detailed_http_error(http_error, query_id)
                log.error(f"HTTP client error for {query_id}: {error_details['message']}")
                raise ExportError(
                    error_type=error_details["error_type"],
                    message=error_details["message"],
                    context={
                        "query_id": query_id,
                        "error": str(http_error),
                        "cluster_name": cluster_name,
                        "connection_url": f"{host}:{http_port}",
                        **error_details["context"]
                    },
                    request_id=request_id or str(uuid.uuid4()),
                    recovery_suggestions=error_details["recovery_suggestions"]
                ) from http_error
            except Exception as query_error:
                error_details = await self._extract_detailed_query_error(query_error, query_id, query)
                log.error(f"Query execution failed for {query_id}: {error_details['message']}")
                raise ExportError(
                    error_type=error_details["error_type"],
                    message=error_details["message"],
                    context={
                        "query_id": query_id,
                        "query": query[:200] + "..." if len(query) > 200 else query,
                        "cluster_name": cluster_name,
                        **error_details["context"]
                    },
                    request_id=request_id or str(uuid.uuid4()),
                    recovery_suggestions=error_details["recovery_suggestions"]
                ) from query_error

        except ExportError:
            # Re-raise export errors as-is
            raise
        except Exception as e:
            error_msg = str(e).lower()
            
            # Classify the error and raise appropriate exception
            if "pool" in error_msg and ("exhausted" in error_msg or "timeout" in error_msg):
                raise ConnectionPoolExhaustedError(
                    request_id=request_id or str(uuid.uuid4()),
                    pool_name=getattr(connection_manager, 'cluster_name', 'unknown'),
                    active_connections=0,  # We don't have access to this info
                    max_connections=0,     # We don't have access to this info
                    context={"query_id": query_id, "error": str(e)}
                )
            elif "timeout" in error_msg or "connection" in error_msg:
                raise ConnectionTimeoutError(
                    request_id=request_id or str(uuid.uuid4()),
                    timeout_seconds=30.0,  # Default timeout
                    operation="parquet_export",
                    context={"query_id": query_id, "error": str(e)}
                )
            else:
                raise ExportError(
                    error_type="unexpected_export_error",
                    message=f"Unexpected error during parquet export: {str(e)}",
                    context={
                        "query_id": query_id,
                        "error": str(e),
                        "error_type": type(e).__name__
                    },
                    request_id=request_id or str(uuid.uuid4()),
                    recovery_suggestions=[
                        "Retry the request",
                        "Check system resources and connectivity",
                        "Contact system administrator if the issue persists"
                    ]
                ) from e

    async def _extract_clickhouse_http_error(self, status_code: int, error_text: str, query_id: str, query: str) -> dict:
        """
        Extract detailed error information from ClickHouse HTTP response errors.
        
        Args:
            status_code: HTTP status code from ClickHouse
            error_text: Error response text from ClickHouse
            query_id: Query ID for context
            query: The SQL query that failed
            
        Returns:
            Dictionary with error_type, message, context, and recovery_suggestions
        """
        error_text_lower = error_text.lower() if error_text else ""
        
        # Handle empty error responses
        if not error_text or len(error_text.strip()) < 3:
            return {
                "error_type": "clickhouse_empty_error",
                "message": f"ClickHouse returned HTTP {status_code} with empty or minimal error message",
                "context": {
                    "raw_error": error_text,
                    "error_length": len(error_text) if error_text else 0,
                    "possible_causes": [
                        "Table does not exist",
                        "Query was cancelled or timed out",
                        "Server internal error",
                        "Connection was dropped during execution"
                    ]
                },
                "recovery_suggestions": [
                    "Verify the table exists and is accessible",
                    "Check ClickHouse server logs for detailed error information",
                    "Try a simpler query to test basic connectivity",
                    "Verify database permissions and user access"
                ]
            }
        
        # Analyze specific HTTP status codes
        if status_code == 404:
            return {
                "error_type": "table_not_found",
                "message": f"Table or database not found: {error_text}",
                "context": {
                    "error_details": error_text,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Verify the table name and database are correct",
                    "Check if the table has been dropped or renamed",
                    "Ensure the job completed successfully and created the expected table",
                    "Verify database permissions"
                ]
            }
        elif status_code == 403:
            return {
                "error_type": "permission_denied",
                "message": f"Access denied to ClickHouse resource: {error_text}",
                "context": {
                    "error_details": error_text,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Verify user has read permissions on the target table",
                    "Check database-level permissions",
                    "Contact administrator to grant necessary access",
                    "Ensure the user credentials are correct"
                ]
            }
        elif status_code == 408 or status_code == 504:
            return {
                "error_type": "query_timeout",
                "message": f"Query execution timed out (HTTP {status_code}): {error_text}",
                "context": {
                    "error_details": error_text,
                    "timeout_status": status_code,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Try reducing the dataset size or adding filters",
                    "Check if the query is optimized and uses appropriate indexes",
                    "Verify ClickHouse server has sufficient resources",
                    "Consider increasing query timeout settings"
                ]
            }
        elif status_code == 500:
            return {
                "error_type": "server_internal_error",
                "message": f"ClickHouse server internal error: {error_text}",
                "context": {
                    "error_details": error_text,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Retry the request as this may be a transient server issue",
                    "Check ClickHouse server logs for detailed error information",
                    "Verify server health and resource availability",
                    "Contact system administrator if the issue persists"
                ]
            }
        elif status_code == 400:
            # Analyze 400 errors for specific issues
            if "table" in error_text_lower and ("not exist" in error_text_lower or "doesn't exist" in error_text_lower):
                return {
                    "error_type": "table_not_found",
                    "message": f"Table does not exist: {error_text}",
                    "context": {
                        "error_details": error_text,
                        "query_snippet": query[:100] + "..." if len(query) > 100 else query
                    },
                    "recovery_suggestions": [
                        "Verify the table name and database are correct",
                        "Check if the table has been dropped or renamed",
                        "Ensure the job completed successfully",
                        "Verify database permissions"
                    ]
                }
            elif "syntax" in error_text_lower or "parse" in error_text_lower:
                return {
                    "error_type": "query_syntax_error",
                    "message": f"Query syntax error: {error_text}",
                    "context": {
                        "error_details": error_text,
                        "query_snippet": query[:200] + "..." if len(query) > 200 else query
                    },
                    "recovery_suggestions": [
                        "Review query syntax for ClickHouse compatibility",
                        "Check for typos in table names, column names, or SQL keywords",
                        "Verify the query structure matches ClickHouse SQL dialect",
                        "Test the query in ClickHouse client directly"
                    ]
                }
            else:
                return {
                    "error_type": "bad_request",
                    "message": f"Bad request to ClickHouse: {error_text}",
                    "context": {
                        "error_details": error_text,
                        "query_snippet": query[:100] + "..." if len(query) > 100 else query
                    },
                    "recovery_suggestions": [
                        "Check query syntax and parameters",
                        "Verify table and column names are correct",
                        "Review the error message for specific issues",
                        "Test with a simpler query to isolate the problem"
                    ]
                }
        else:
            return {
                "error_type": "clickhouse_http_error",
                "message": f"ClickHouse HTTP error {status_code}: {error_text}",
                "context": {
                    "error_details": error_text,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Check ClickHouse server logs for detailed error information",
                    "Verify query syntax and table existence",
                    "Retry the request as this may be a transient issue",
                    "Contact system administrator if the issue persists"
                ]
            }

    async def _extract_detailed_http_error(self, http_error: aiohttp.ClientError, query_id: str) -> dict:
        """
        Extract detailed error information from aiohttp ClientError.
        
        Args:
            http_error: The aiohttp ClientError that occurred
            query_id: Query ID for context
            
        Returns:
            Dictionary with error_type, message, context, and recovery_suggestions
        """
        error_str = str(http_error).lower()
        
        if isinstance(http_error, aiohttp.ClientTimeout):
            return {
                "error_type": "http_timeout",
                "message": f"HTTP request timed out while executing query",
                "context": {
                    "timeout_type": "http_client_timeout",
                    "error_details": str(http_error)
                },
                "recovery_suggestions": [
                    "Retry the request as this may be a temporary network issue",
                    "Check ClickHouse server load and performance",
                    "Verify network connectivity between client and server",
                    "Consider increasing HTTP timeout settings"
                ]
            }
        elif isinstance(http_error, aiohttp.ClientConnectorError):
            return {
                "error_type": "http_connection_failed",
                "message": f"Failed to establish HTTP connection to ClickHouse server",
                "context": {
                    "connection_error": str(http_error),
                    "error_details": getattr(http_error, 'strerror', 'Unknown connection error')
                },
                "recovery_suggestions": [
                    "Verify ClickHouse HTTP port configuration and server status",
                    "Check network connectivity and firewall settings",
                    "Ensure ClickHouse server is running and accepting connections",
                    "Verify connection pool configuration"
                ]
            }
        elif isinstance(http_error, aiohttp.ClientResponseError):
            return {
                "error_type": "http_response_error",
                "message": f"ClickHouse HTTP server returned error status {http_error.status}",
                "context": {
                    "status_code": http_error.status,
                    "response_message": getattr(http_error, 'message', 'Unknown response error'),
                    "response_headers": dict(getattr(http_error, 'headers', {}))
                },
                "recovery_suggestions": [
                    "Check query syntax and table existence",
                    "Verify database permissions and user access",
                    "Review ClickHouse server logs for detailed error information",
                    "Ensure the requested database and table are accessible"
                ]
            }
        else:
            return {
                "error_type": "http_client_error",
                "message": f"HTTP client error during ClickHouse communication: {str(http_error)}",
                "context": {
                    "error_type": type(http_error).__name__,
                    "error_details": str(http_error)
                },
                "recovery_suggestions": [
                    "Retry the request as this may be a transient issue",
                    "Check ClickHouse server health and connectivity",
                    "Verify HTTP client configuration",
                    "Review system logs for additional error details"
                ]
            }

    async def _extract_detailed_query_error(self, query_error: Exception, query_id: str, query: str) -> dict:
        """
        Extract detailed error information from query execution errors.
        
        Args:
            query_error: The exception that occurred during query execution
            query_id: Query ID for context
            query: The SQL query that failed
            
        Returns:
            Dictionary with error_type, message, context, and recovery_suggestions
        """
        error_str = str(query_error).lower()
        error_message = str(query_error).strip()
        
        # Handle empty or minimal error messages
        if not error_message or len(error_message) < 3:
            return {
                "error_type": "query_execution_failed",
                "message": f"Query execution failed with empty or minimal error response from ClickHouse",
                "context": {
                    "error_details": f"Original error: '{error_message}' (length: {len(error_message)})",
                    "error_type": type(query_error).__name__,
                    "possible_causes": [
                        "Table does not exist",
                        "Connection was dropped during execution",
                        "Server returned non-standard error response",
                        "Query was cancelled or timed out"
                    ]
                },
                "recovery_suggestions": [
                    "Verify the table exists and is accessible",
                    "Check ClickHouse server logs for detailed error information",
                    "Try executing a simpler query to test connectivity",
                    "Verify database permissions and user access"
                ]
            }
        
        # Analyze error content for specific issues
        if "table" in error_str and ("not exist" in error_str or "doesn't exist" in error_str or "unknown table" in error_str):
            return {
                "error_type": "table_not_found",
                "message": f"Table referenced in query does not exist: {error_message}",
                "context": {
                    "error_details": error_message,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Verify the table name and database are correct",
                    "Check if the table has been dropped or renamed",
                    "Ensure you have permission to access the table",
                    "Verify the job completed successfully and created the expected table"
                ]
            }
        elif "permission" in error_str or "access denied" in error_str or "not allowed" in error_str:
            return {
                "error_type": "permission_denied",
                "message": f"Insufficient permissions to execute query: {error_message}",
                "context": {
                    "error_details": error_message,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Verify user has read permissions on the target table",
                    "Check database-level permissions",
                    "Contact administrator to grant necessary access",
                    "Ensure the user account is properly configured"
                ]
            }
        elif "timeout" in error_str or "cancelled" in error_str:
            return {
                "error_type": "query_timeout",
                "message": f"Query execution was cancelled or timed out: {error_message}",
                "context": {
                    "error_details": error_message,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Try reducing the dataset size or adding filters",
                    "Check if the query is optimized and uses appropriate indexes",
                    "Verify ClickHouse server has sufficient resources",
                    "Consider increasing query timeout settings"
                ]
            }
        elif "memory" in error_str or "out of memory" in error_str:
            return {
                "error_type": "insufficient_memory",
                "message": f"Query execution failed due to memory constraints: {error_message}",
                "context": {
                    "error_details": error_message,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Try reducing the dataset size or adding LIMIT clause",
                    "Use more selective WHERE conditions to filter data",
                    "Check ClickHouse server memory configuration",
                    "Consider processing data in smaller chunks"
                ]
            }
        elif "syntax" in error_str or "parse" in error_str:
            return {
                "error_type": "query_syntax_error",
                "message": f"Query contains syntax errors: {error_message}",
                "context": {
                    "error_details": error_message,
                    "query_snippet": query[:200] + "..." if len(query) > 200 else query
                },
                "recovery_suggestions": [
                    "Review query syntax for ClickHouse compatibility",
                    "Check for typos in table names, column names, or SQL keywords",
                    "Verify the query structure matches ClickHouse SQL dialect",
                    "Test the query in ClickHouse client directly"
                ]
            }
        else:
            return {
                "error_type": "query_execution_failed",
                "message": f"Query execution failed: {error_message}",
                "context": {
                    "error_details": error_message,
                    "error_type": type(query_error).__name__,
                    "query_snippet": query[:100] + "..." if len(query) > 100 else query
                },
                "recovery_suggestions": [
                    "Check query syntax and table existence",
                    "Verify database permissions and connectivity",
                    "Review ClickHouse server logs for detailed error information",
                    "Retry the request as this may be a transient issue"
                ]
            }

    def _calculate_optimal_chunk_size(self, query_id: str) -> int:
        """
        Calculate optimal chunk size based on query characteristics and system resources.
        
        For large datasets (indicated by query_id patterns or previous performance),
        use larger chunks to reduce overhead and improve throughput.
        
        Args:
            query_id: Query identifier to analyze for size hints
            
        Returns:
            Optimal chunk size in bytes
        """
        # Import settings for unified chunk size configuration
        from magic_gateway.core.config import settings
        
        # Use unified chunk sizes from configuration
        default_chunk_size = int(settings.CHUNK_SIZE_DEFAULT_BYTES)  # 64KB
        large_chunk_size = int(settings.CHUNK_SIZE_LARGE_BYTES)      # 512KB
        huge_chunk_size = int(settings.CHUNK_SIZE_HUGE_BYTES)        # 2MB
        
        # Analyze query_id for size hints
        query_id_lower = query_id.lower()
        
        # Check for patterns that indicate large datasets
        large_dataset_indicators = [
            "export_",  # Export operations are typically large
            "job_",     # Job results can be large
            "bulk_",    # Bulk operations
            "report_"   # Reports are often large
        ]
        
        # Check for patterns that indicate very large datasets
        huge_dataset_indicators = [
            "million",  # Queries mentioning millions of rows
            "large",    # Explicitly marked as large
            "huge",     # Explicitly marked as huge
            "batch"     # Batch processing
        ]
        
        # Determine chunk size based on indicators
        if any(indicator in query_id_lower for indicator in huge_dataset_indicators):
            log.debug(f"Using huge chunk size ({huge_chunk_size:,} bytes / {huge_chunk_size//1024}KB) for query {query_id}")
            return huge_chunk_size
        elif any(indicator in query_id_lower for indicator in large_dataset_indicators):
            log.debug(f"Using large chunk size ({large_chunk_size:,} bytes / {large_chunk_size//1024}KB) for query {query_id}")
            return large_chunk_size
        else:
            log.debug(f"Using default chunk size ({default_chunk_size:,} bytes / {default_chunk_size//1024}KB) for query {query_id}")
            return default_chunk_size

    def _extract_connection_details(self, connection_manager: ClickHouseConnectionManager) -> dict:
        """
        Extract connection details from a connection manager for HTTP interface usage.
        
        This method provides a bridge between the connection pool configuration
        and the HTTP interface requirements for parquet streaming.
        
        Args:
            connection_manager: The ClickHouse connection manager
            
        Returns:
            Dictionary containing connection details for HTTP interface
        """
        cluster_name = getattr(connection_manager, 'cluster_name', 'primary')
        
        return {
            'cluster_name': cluster_name,
            'host': getattr(connection_manager, 'host', None),
            'http_port': getattr(connection_manager, 'http_port', None),
            'user': getattr(connection_manager, 'user', None),
            'password': getattr(connection_manager, 'password', None),
            'database': getattr(connection_manager, 'database', None),
            'connection_info': getattr(connection_manager, 'connection_info', f'ClickHouse ({cluster_name})')
        }

    async def export_to_parquet_stream_with_fallback(
        self,
        query: str,
        table_name: str,
        query_id: str,
        request_id: Optional[str] = None,
        max_retries: int = 2
    ) -> AsyncGenerator[bytes, None]:
        """
        Stream parquet data with automatic connection fallback.
        
        This method attempts to use the optimal connection first, then falls back
        to alternative connections if the primary choice fails.
        
        Args:
            query: SQL query to execute
            table_name: Table name for connection routing
            query_id: Unique identifier for query tracking
            request_id: Optional request ID for error tracking
            max_retries: Maximum number of fallback attempts
            
        Yields:
            Bytes of parquet data
            
        Raises:
            ExportError: If all connection attempts fail
        """
        req_id = request_id or str(uuid.uuid4())
        operation = f"parquet_stream_export_{query_id}"
        
        with ExportErrorContext(req_id, operation, connection_pool_adapter=self) as ctx:
            ctx.add_context("query_id", query_id)
            ctx.add_context("table_name", table_name)
            ctx.add_context("max_retries", max_retries)
            
            last_error = None
            attempts = 0
            
            while attempts <= max_retries:
                try:
                    # Get optimal connection for this attempt with status logging
                    connection_selection = await self.get_connection_with_status_logging(
                        table_name, req_id, f"{operation}_attempt_{attempts + 1}"
                    )
                    
                    ctx.add_context(f"attempt_{attempts + 1}_connection", {
                        "cluster_name": connection_selection.cluster_name,
                        "is_fallback": connection_selection.is_fallback,
                        "routing_reason": connection_selection.routing_reason
                    })
                    
                    log.info(
                        f"Attempt {attempts + 1}/{max_retries + 1}: Using {connection_selection.routing_reason} "
                        f"for query {query_id} (request {req_id})"
                    )
                    
                    # Try streaming with the selected connection
                    chunk_count = 0
                    async for chunk in self.export_to_parquet_stream(
                        query=query,
                        connection_manager=connection_selection.manager,
                        query_id=query_id,
                        request_id=req_id
                    ):
                        chunk_count += 1
                        yield chunk
                    
                    # If we get here, streaming was successful
                    log.info(
                        f"Successfully completed streaming for query {query_id} "
                        f"(request {req_id}) with {chunk_count} chunks"
                    )
                    ctx.add_context("success", True)
                    ctx.add_context("chunks_streamed", chunk_count)
                    return
                    
                except (ConnectionPoolExhaustedError, ConnectionTimeoutError) as e:
                    last_error = e
                    attempts += 1
                    
                    # Log detailed connection pool status on connection errors
                    pool_status = self.get_connection_pool_status()
                    log.warning(
                        f"Connection attempt {attempts} failed for query {query_id} "
                        f"(request {req_id}): {e}. Pool status: {pool_status}"
                    )
                    
                    ctx.add_context(f"attempt_{attempts}_error", {
                        "error_type": type(e).__name__,
                        "error_message": str(e),
                        "pool_status": pool_status
                    })
                    
                    if attempts <= max_retries:
                        retry_delay = 0.5 * attempts
                        log.info(
                            f"Retrying query {query_id} (request {req_id}) "
                            f"after {retry_delay}s delay..."
                        )
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        log.error(
                            f"All connection attempts failed for query {query_id} "
                            f"(request {req_id}) after {attempts} attempts"
                        )
                        break
                        
                except ExportError as e:
                    # For other export errors, don't retry as they're likely not connection-related
                    log.error(f"Export error for query {query_id} (request {req_id}): {e}")
                    ctx.add_context("export_error", {
                        "error_type": e.error_type,
                        "error_message": e.message
                    })
                    raise
                    
                except Exception as e:
                    last_error = e
                    attempts += 1
                    
                    log.warning(
                        f"Unexpected error on attempt {attempts} for query {query_id} "
                        f"(request {req_id}): {e}"
                    )
                    
                    ctx.add_context(f"attempt_{attempts}_unexpected_error", {
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    })
                    
                    if attempts <= max_retries:
                        retry_delay = 0.5 * attempts
                        log.info(f"Retrying after {retry_delay}s delay...")
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        break
            
            # If we get here, all attempts failed
            final_pool_status = self.get_connection_pool_status()
            ctx.add_context("final_pool_status", final_pool_status)
            ctx.add_context("total_attempts", attempts)
            
            raise ExportError(
                error_type="streaming_failed_all_connections",
                message=f"Failed to stream parquet data after {attempts} attempts",
                context={
                    "query_id": query_id,
                    "table_name": table_name,
                    "attempts": attempts,
                    "last_error": str(last_error) if last_error else "Unknown error",
                    "final_pool_status": final_pool_status
                },
                request_id=req_id,
                recovery_suggestions=[
                    "Check ClickHouse server connectivity and health",
                    "Verify connection pool configuration",
                    "Check system resources and load",
                    "Retry the request after a delay"
                ],
                connection_pool_status=final_pool_status
            ) from last_error