# Templates Directory

This directory contains HTML templates used by the MagicGateway application.

## Files

### assortment_optimizer.html
- **Purpose**: Web interface for the assortment optimization script
- **Used by**: `/api/v1/scripts/assortment-optimizer/interface` endpoint
- **Features**: 
  - Responsive design with mobile support
  - Real-time form validation
  - Authentication integration
  - Download link management
  - Error handling and retry logic

## Usage

Templates are loaded by the FastAPI endpoints using Python's file I/O operations. The HTML files contain embedded CSS and JavaScript for a complete, self-contained interface.

## Development

When modifying templates:
1. Test the interface in multiple browsers
2. Verify mobile responsiveness
3. Check form validation behavior
4. Test authentication flow
5. Verify download functionality

## File Structure

```
templates/
├── README.md                    # This documentation
└── assortment_optimizer.html    # Assortment optimizer interface
```

## Benefits of Separate Templates

- **Maintainability**: HTML/CSS/JS code is easier to edit in dedicated files
- **Syntax Highlighting**: IDEs provide proper syntax highlighting for HTML files
- **Version Control**: Changes to templates are easier to track in git
- **Reusability**: Templates can be shared across multiple endpoints if needed
- **Performance**: Templates are cached by the OS file system