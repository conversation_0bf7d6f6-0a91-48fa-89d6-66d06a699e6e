"""
Excel streaming writer for memory-efficient Excel file generation.

This module implements memory-efficient Excel file generation using xmlwriter
with direct streaming from SQLAlchemy results for both vertical and horizontal layouts.
"""

import asyncio
import gc
import logging
import math
import os
import tempfile
import time
import uuid
from typing import Any, AsyncGenerator, Dict, List, Optional, Tuple

import xlsxwriter
from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import ClickHouseConnectionManager
from magic_gateway.db.clickhouse_handler import <PERSON>lick<PERSON>ouse<PERSON>andler
from magic_gateway.utils.temp_file_manager import temp_file_manager
from magic_gateway.export.exceptions import (
    ExcelLimitExceededError,
    ExportErrorContext,
    FormatConversionError,
    StreamingDataError,
)
from magic_gateway.export.models import ExcelLayout, JobMetadata
from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator


class ExcelStreamingWriter:
    """
    Memory-efficient Excel file generation using xmlwriter.

    This class implements streaming Excel export with direct processing from
    SQLAlchemy results to minimize memory usage for large datasets.
    """

    # Excel limits and constants
    EXCEL_MAX_ROWS = 1048576  # Excel row limit per sheet
    EXCEL_MAX_COLS = 16384  # Excel column limit per sheet
    CHUNK_SIZE = 500000  # Default chunk size for processing
    PROGRESS_LOG_INTERVAL = 1000000  # Log progress every 100K rows

    def __init__(self):
        """Initialize the Excel streaming writer."""
        self.workbook: Optional[xlsxwriter.Workbook] = None
        self.worksheets: Dict[str, xlsxwriter.worksheet.Worksheet] = {}
        self.current_rows: Dict[str, int] = {}
        self.sheet_counter: Dict[str, int] = {}

    async def write_streaming_excel_from_sqlalchemy(
        self,
        connection_manager: ClickHouseConnectionManager,
        query: str,
        output_path: str,
        layout: ExcelLayout,
        metadata: JobMetadata,
        chunk_size: int = CHUNK_SIZE,
        separate_periods: bool = False,
        job_info: Optional[Any] = None,
        request_id: Optional[str] = None,
    ) -> None:
        """
        Write Excel file using streaming from SQLAlchemy results.

        Args:
            connection_manager: ClickHouse connection manager
            query: SQL query to execute
            output_path: Path to write the Excel file
            layout: Excel layout (vertical or horizontal)
            metadata: Job metadata for optimization
            chunk_size: Size of chunks to process
            separate_periods: Whether to separate periods into different sheets
            job_info: Job information for info sheet
            request_id: Request ID for error tracking

        Raises:
            ExcelLimitExceededError: If data exceeds Excel limits
            FormatConversionError: If conversion fails
            StreamingDataError: If streaming fails
        """
        request_id = request_id or str(uuid.uuid4())

        with ExportErrorContext(
            request_id, "write_streaming_excel", metadata.job_id
        ) as ctx:
            ctx.add_context("output_path", output_path)
            ctx.add_context("layout", layout.value)
            ctx.add_context("separate_periods", separate_periods)
            ctx.add_context("chunk_size", chunk_size)

            # Validate Excel limits before starting
            await self._validate_excel_limits(metadata, layout, separate_periods, ctx)

            try:
                # Create workbook with optimization options
                self.workbook = xlsxwriter.Workbook(
                    output_path,
                    {
                        "constant_memory": True,  # Enable constant memory mode
                        "tmpdir": str(temp_file_manager.base_directory),
                        "default_date_format": "yyyy-mm-dd",
                        "remove_timezone": True,
                        "nan_inf_to_errors": True,
                        "strings_to_formulas": False,
                        "strings_to_urls": False,
                    },
                )

                log.info(
                    f"Created Excel workbook with constant memory mode: {output_path}"
                )

                # Stream data and write to worksheets
                if separate_periods:
                    await self._write_periods_to_separate_sheets(
                        connection_manager, query, layout, metadata, chunk_size, ctx
                    )
                else:
                    await self._write_data_to_single_sheet(
                        connection_manager, query, layout, metadata, chunk_size, ctx
                    )

                # Add info sheet as the last sheet if job_info is provided
                if job_info:
                    self._add_info_sheet(job_info, os.path.basename(output_path))

                # Log summary before closing
                summary = self._get_sheet_summary()
                log.info(
                    f"Excel export summary: {summary['total_sheets']} sheets, "
                    f"{summary['total_data_rows']} total data rows"
                )

                # Close workbook to finalize file
                self.workbook.close()
                log.info(f"Excel file written successfully: {output_path}")

            except Exception as e:
                # Clean up workbook if it exists
                if self.workbook:
                    try:
                        self.workbook.close()
                    except Exception:
                        pass

                # Remove partial file
                if os.path.exists(output_path):
                    try:
                        os.remove(output_path)
                    except Exception:
                        pass

                if isinstance(
                    e,
                    (
                        ExcelLimitExceededError,
                        FormatConversionError,
                        StreamingDataError,
                    ),
                ):
                    raise
                else:
                    raise FormatConversionError(
                        f"Failed to write Excel file: {str(e)}",
                        request_id=request_id,
                        context=ctx.get_context(),
                    )
            finally:
                # Clean up references
                self.workbook = None
                self.worksheets.clear()
                self.current_rows.clear()
                self.sheet_counter.clear()
                gc.collect()

    async def _validate_excel_limits(
        self,
        metadata: JobMetadata,
        layout: ExcelLayout,
        separate_periods: bool,
        ctx: ExportErrorContext,
    ) -> None:
        """
        Validate that the data will fit within Excel limits.

        This method uses the same logic as MetadataAnalyzer.validate_excel_limits()
        to ensure consistent validation across the system.

        Args:
            metadata: Job metadata
            layout: Excel layout
            separate_periods: Whether periods are separated
            ctx: Error context for tracking

        Raises:
            ExcelLimitExceededError: If data exceeds Excel limits
        """
        # Calculate effective row count based on layout and options
        # This matches the logic in MetadataAnalyzer.validate_excel_limits()
        if layout == ExcelLayout.HORIZONTAL and len(metadata.facts_list) > 1:
            # Horizontal layout reduces rows by pivoting facts to columns
            effective_rows = metadata.total_rows // len(metadata.facts_list)
            log.debug(
                f"Excel validation - Horizontal layout: {metadata.total_rows} rows reduced to ~{effective_rows} rows"
            )
        else:
            effective_rows = metadata.total_rows

        # Check if period separation is enabled
        if separate_periods and len(metadata.available_periods) > 1:
            # With period separation, each period goes to a separate sheet
            max_rows_per_sheet = max(
                effective_rows // len(metadata.available_periods),
                effective_rows // len(metadata.available_periods)
                + (effective_rows % len(metadata.available_periods)),
            )
            log.debug(
                f"Excel validation - Period separation: max {max_rows_per_sheet} rows per sheet"
            )
        else:
            # All data in one sheet
            max_rows_per_sheet = effective_rows

        # Validate against Excel limit (subtract 1 for header row)
        excel_limit_with_header = self.EXCEL_MAX_ROWS - 1
        if max_rows_per_sheet > excel_limit_with_header:
            raise ExcelLimitExceededError(
                request_id=ctx.request_id,
                total_rows=max_rows_per_sheet,
                excel_limit=excel_limit_with_header,
                context={
                    **ctx.get_context(),
                    "validation_type": "excel_streaming_validation",
                    "original_rows": metadata.total_rows,
                    "effective_rows": effective_rows,
                    "max_rows_per_sheet": max_rows_per_sheet,
                    "horizontal_layout": layout == ExcelLayout.HORIZONTAL,
                    "separate_periods": separate_periods,
                    "periods_count": len(metadata.available_periods),
                    "facts_count": len(metadata.facts_list),
                },
            )

        log.debug(
            f"Excel streaming validation passed for job {metadata.job_id}: "
            f"{max_rows_per_sheet:,} rows per sheet (limit: {excel_limit_with_header:,})"
        )

        # Validate column limits for horizontal layout
        if layout == ExcelLayout.HORIZONTAL and metadata.facts_list:
            # Extract axes information from job_info
            axes_info = metadata.job_info.get("axes", {}) if metadata.job_info else {}

            # Base columns (axes + period) + facts columns
            estimated_cols = len(axes_info) + 1 + len(metadata.facts_list)

            if estimated_cols > self.EXCEL_MAX_COLS:
                # For column limits, we'll use a custom error since ExcelLimitExceededError is for rows
                raise FormatConversionError(
                    request_id=ctx.request_id,
                    source_format="clickhouse",
                    target_format="excel",
                    error_details=f"Column limit exceeded: {estimated_cols} > {self.EXCEL_MAX_COLS}",
                    context={
                        **ctx.get_context(),
                        "validation_type": "column_limit",
                        "estimated_columns": estimated_cols,
                        "excel_column_limit": self.EXCEL_MAX_COLS,
                        "facts_count": len(metadata.facts_list),
                        "axes_count": len(axes_info),
                    },
                )

    async def _write_periods_to_separate_sheets(
        self,
        connection_manager: ClickHouseConnectionManager,
        base_query: str,
        layout: ExcelLayout,
        metadata: JobMetadata,
        chunk_size: int,
        ctx: ExportErrorContext,
    ) -> None:
        """
        Write data with periods separated into different sheets for both layouts.

        This method handles period separation uniformly for both vertical and horizontal
        layouts. The ClickHouse pivot query handles the transformation when needed.

        Args:
            connection_manager: ClickHouse connection manager
            base_query: Base SQL query
            layout: Excel layout (vertical or horizontal)
            metadata: Job metadata
            chunk_size: Chunk size for processing
            ctx: Error context
        """
        log.info(
            f"Creating separate sheets for {len(metadata.available_periods)} periods using {layout.value} layout"
        )

        for period in metadata.available_periods:
            sheet_name = f"Period_{period}"

            # Modify query to filter by period - works for both layouts
            period_query = self._add_period_filter(base_query, period)

            # Create worksheet for this period with validation
            worksheet = self._create_worksheet_with_validation(sheet_name)

            log.info(f"Processing period '{period}' with {layout.value} layout")

            # Stream data for this period - same process for both layouts
            await self._stream_to_excel_worksheet(
                connection_manager,
                period_query,
                worksheet,
                sheet_name,
                layout,
                metadata,
                chunk_size,
                ctx,
            )

            log.debug(
                f"Completed period '{period}' with {self.current_rows[sheet_name] - 1} data rows"
            )

    async def _write_data_to_single_sheet(
        self,
        connection_manager: ClickHouseConnectionManager,
        query: str,
        layout: ExcelLayout,
        metadata: JobMetadata,
        chunk_size: int,
        ctx: ExportErrorContext,
    ) -> None:
        """
        Write all data to a single sheet for both vertical and horizontal layouts.

        Args:
            connection_manager: ClickHouse connection manager
            query: SQL query
            layout: Excel layout (vertical or horizontal)
            metadata: Job metadata
            chunk_size: Chunk size for processing
            ctx: Error context
        """
        sheet_name = "Data"
        worksheet = self._create_worksheet_with_validation(sheet_name)

        log.info(f"Writing all data to single sheet using {layout.value} layout")

        await self._stream_to_excel_worksheet(
            connection_manager,
            query,
            worksheet,
            sheet_name,
            layout,
            metadata,
            chunk_size,
            ctx,
        )

        log.debug(
            f"Completed single sheet with {self.current_rows[sheet_name] - 1} data rows"
        )

    async def _stream_to_excel_worksheet(
        self,
        connection_manager: ClickHouseConnectionManager,
        query: str,
        worksheet: xlsxwriter.worksheet.Worksheet,
        sheet_name: str,
        layout: ExcelLayout,
        metadata: JobMetadata,
        chunk_size: int,
        ctx: ExportErrorContext,
    ) -> None:
        """
        Stream data from ClickHouse to Excel worksheet using pagination.

        Args:
            connection_manager: ClickHouse connection manager
            query: SQL query to execute
            worksheet: Excel worksheet to write to
            sheet_name: Name of the sheet
            layout: Excel layout
            metadata: Job metadata
            chunk_size: Chunk size for processing
            ctx: Error context
        """
        headers_written = False
        total_rows_processed = 0
        offset = 0
        columns = None  # Cache column names after first retrieval

        try:
            async with connection_manager.connection() as conn:
                while True:
                    # Add LIMIT and OFFSET to the query for pagination
                    paginated_query = f"{query} LIMIT {chunk_size} OFFSET {offset}"

                    # Execute query in thread pool since ClickHouse driver is synchronous
                    result = await asyncio.get_event_loop().run_in_executor(
                        None, conn.execute, paginated_query
                    )

                    if not result:
                        # No more data
                        break

                    # Get column names - only retrieve once and cache
                    if columns is None:
                        # Try to get column names from query result first
                        if hasattr(conn, "last_query") and hasattr(
                            conn.last_query, "columns_with_types"
                        ):
                            columns = [
                                col[0] for col in conn.last_query.columns_with_types
                            ]
                            log.debug(
                                f"Excel streamer retrieved column names from query result: {len(columns)} columns"
                            )
                        else:
                            # Get actual column names from table schema
                            log.info(
                                f"Excel streamer retrieving actual column names from table schema for {metadata.table_name}"
                            )
                            columns = await self._get_actual_column_names(
                                metadata.table_name, ctx.request_id
                            )
                            log.debug(
                                f"Excel streamer retrieved actual column names from schema: {len(columns)} columns"
                            )

                    # Convert result to list of dictionaries
                    chunk_data = []
                    for row in result:
                        row_dict = dict(zip(columns, row))
                        chunk_data.append(row_dict)

                    if not headers_written and chunk_data:
                        # Write headers on first chunk
                        headers = list(chunk_data[0].keys())
                        await self._write_headers(worksheet, headers, layout)
                        headers_written = True
                        self.current_rows[sheet_name] = 1  # Header row

                    # Write data rows
                    for row_data in chunk_data:
                        await self._write_data_row(
                            worksheet, row_data, self.current_rows[sheet_name], layout
                        )
                        self.current_rows[sheet_name] += 1
                        total_rows_processed += 1

                        # Check Excel row limit
                        if self.current_rows[sheet_name] >= self.EXCEL_MAX_ROWS:
                            raise ExcelLimitExceededError(
                                request_id=ctx.request_id,
                                total_rows=self.current_rows[sheet_name],
                                excel_limit=self.EXCEL_MAX_ROWS,
                                context={
                                    **ctx.get_context(),
                                    "sheet_name": sheet_name,
                                    "current_row": self.current_rows[sheet_name],
                                },
                            )

                    # Log progress
                    if total_rows_processed % self.PROGRESS_LOG_INTERVAL == 0:
                        log.info(
                            f"Processed {total_rows_processed} rows for sheet '{sheet_name}'"
                        )

                    # Force garbage collection periodically
                    if total_rows_processed % (chunk_size * 10) == 0:
                        gc.collect()

                    # If we got fewer rows than chunk_size, we're done
                    if len(result) < chunk_size:
                        break

                    offset += chunk_size

                log.info(
                    f"Completed sheet '{sheet_name}' with {total_rows_processed} rows"
                )

        except Exception as e:
            if isinstance(e, ExcelLimitExceededError):
                raise
            else:
                raise StreamingDataError(
                    request_id=ctx.request_id,
                    operation="excel_streaming",
                    error_details=f"Failed to stream data to Excel worksheet: {str(e)}",
                    context=ctx.get_context(),
                )

    async def _write_headers(
        self,
        worksheet: xlsxwriter.worksheet.Worksheet,
        headers: List[str],
        layout: ExcelLayout,
    ) -> None:
        """
        Write headers to the worksheet for both vertical and horizontal layouts.

        The layout parameter is provided for future extensibility, but both layouts
        are handled uniformly since ClickHouse pivot query handles the transformation.

        Args:
            worksheet: Excel worksheet
            headers: List of header names
            layout: Excel layout (vertical or horizontal)
        """
        # Create header format
        header_format = self.workbook.add_format(
            {
                "bold": True,
                "bg_color": "#D8E4BC",
                "border": 1,
                "font_name": "Arial",
                "font_size": 10,
            }
        )

        # Write headers uniformly for both layouts
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # Auto-fit column widths (approximate)
        for col, header in enumerate(headers):
            # Set wider columns for fact columns in horizontal layout
            if layout == ExcelLayout.HORIZONTAL and any(
                fact_indicator in header.lower()
                for fact_indicator in ["fact", "value", "kpi", "metric"]
            ):
                worksheet.set_column(col, col, max(len(header) + 4, 15))
            else:
                worksheet.set_column(col, col, max(len(header) + 2, 10))

    async def _write_data_row(
        self,
        worksheet: xlsxwriter.worksheet.Worksheet,
        row_data: Dict[str, Any],
        row_num: int,
        layout: ExcelLayout,
    ) -> None:
        """
        Write a single data row to the worksheet for both vertical and horizontal layouts.

        Both layouts are handled uniformly since ClickHouse pivot query handles
        the transformation from vertical to horizontal format when needed.

        Args:
            worksheet: Excel worksheet
            row_data: Dictionary of row data
            row_num: Row number to write to
            layout: Excel layout (vertical or horizontal)
        """
        col = 0
        for key, value in row_data.items():
            # Clean value for Excel
            clean_value = self._clean_excel_value(value)

            # Apply number formatting for numeric values in horizontal layout
            if layout == ExcelLayout.HORIZONTAL and isinstance(
                clean_value, (int, float)
            ):
                # Create number format for better readability
                if not hasattr(self, "_number_format"):
                    self._number_format = self.workbook.add_format(
                        {"num_format": "#,##0.00"}
                    )
                worksheet.write(row_num, col, clean_value, self._number_format)
            else:
                worksheet.write(row_num, col, clean_value)

            col += 1

    def _clean_excel_value(self, value: Any) -> Any:
        """
        Clean a value for Excel export by handling NaN, INF, NULL, and NA values.

        Args:
            value: The value to clean

        Returns:
            Cleaned value safe for Excel export
        """
        if value is None:
            return ""

        # Handle numeric NaN and INF values
        if isinstance(value, (int, float)):
            if math.isnan(value) or math.isinf(value):
                return ""

        # Handle string representations of NULL/NA/NaN
        str_value = str(value).strip().lower()
        if str_value in ("null", "na", "nan", "none", ""):
            return ""

        return value

    def _sanitize_sheet_name(self, name: str) -> str:
        """
        Sanitize sheet name for Excel compatibility.

        Args:
            name: Original sheet name

        Returns:
            Sanitized sheet name
        """
        # Excel sheet name restrictions
        invalid_chars = ["\\", "/", "*", "[", "]", ":", "?"]
        sanitized = name

        for char in invalid_chars:
            sanitized = sanitized.replace(char, "_")

        # Limit to 31 characters (Excel limit)
        if len(sanitized) > 31:
            sanitized = sanitized[:31]

        return sanitized

    def _add_period_filter(self, base_query: str, period: str) -> str:
        """
        Add period filter to the base query.

        Args:
            base_query: Base SQL query
            period: Period to filter by

        Returns:
            Modified query with period filter
        """
        escaped_period = period.replace("'", "''")  # Escape single quotes

        if "WHERE" in base_query.upper():
            return f"{base_query} AND period_name = '{escaped_period}'"
        else:
            # Need to insert WHERE clause before ORDER BY if it exists
            query_upper = base_query.upper()
            order_by_index = query_upper.find("ORDER BY")
            group_by_index = query_upper.find("GROUP BY")

            if group_by_index != -1:
                before_group = base_query[:group_by_index].strip()
                group_clause = base_query[group_by_index:]
                return f"{before_group} WHERE period_name = '{escaped_period}' {group_clause}"
            elif order_by_index != -1:
                # Insert WHERE clause before ORDER BY or GROUP BY
                before_order = base_query[:order_by_index].strip()
                order_clause = base_query[order_by_index:]
                return f"{before_order} WHERE period_name = '{escaped_period}' {order_clause}"
            else:
                # No ORDER BY, add WHERE at the end
                return f"{base_query} WHERE period_name = '{escaped_period}'"

    def _add_info_sheet(self, job_info: Any, filename: str) -> None:
        """
        Add info sheet using the existing InfoSheetGenerator.

        This preserves the existing info sheet functionality and ensures it's
        added as the last sheet in the workbook for both vertical and horizontal layouts.

        Args:
            job_info: Job information to include
            filename: Export filename
        """
        try:
            log.debug("Adding info sheet using InfoSheetGenerator")
            info_generator = InfoSheetGenerator(self.workbook)
            info_generator.create_info_sheet(job_info, filename)
            log.info("Info sheet added successfully as the last sheet")
        except Exception as e:
            log.warning(f"Failed to add info sheet: {str(e)}")
            # Don't fail the entire export if info sheet fails
            # This ensures robustness while preserving the functionality

    async def _get_actual_column_names(
        self, table_name: str, request_id: Optional[str] = None
    ) -> List[str]:
        """
        Get actual column names from the ClickHouse table schema.

        This method queries the ClickHouse system.columns table to retrieve the real
        column names and types from the dataset source instead of using hardcoded defaults.

        Args:
            table_name: Fully qualified table name (database.table)
            request_id: Optional request ID for tracking

        Returns:
            List of actual column names from the table schema

        Raises:
            StreamingDataError: If column names cannot be retrieved
        """
        try:
            log.info(
                f"Excel streamer retrieving actual column names for table {table_name}"
            )

            # Use the ClickHouseHandler to get table schema
            schema_info = await ClickHouseHandler.get_table_schema(
                table_name=table_name,
                query_id=request_id,
            )

            if not schema_info:
                raise StreamingDataError(
                    error_type="schema_retrieval_failed",
                    message=f"No schema information found for table {table_name}",
                    context=ExportErrorContext(
                        table_name=table_name,
                        request_id=request_id,
                        operation="get_column_names_excel",
                    ),
                )

            # Extract column names in order
            column_names = [col["name"] for col in schema_info]

            log.info(
                f"Excel streamer retrieved {len(column_names)} actual column names for {table_name}"
            )
            return column_names

        except Exception as e:
            log.error(
                f"Excel streamer failed to retrieve actual column names for {table_name}: {e}",
                exc_info=True,
            )
            # Fall back to default columns as last resort
            log.warning(
                f"Excel streamer falling back to default column names for {table_name}"
            )
            return self._get_default_columns(0)  # Will generate generic names

    def _get_default_columns(self, num_columns: int) -> List[str]:
        """
        Get default column names when column information is not available.

        Args:
            num_columns: Number of columns in the result

        Returns:
            List of default column names
        """
        if num_columns == 0:
            return []

        # Common column structure for job data
        default_columns = [
            "job_id",
            "period_name",
            "axis_1",
            "axis_2",
            "axis_3",
            "axis_4",
            "axis_5",
            "position_1",
            "position_2",
            "position_3",
            "position_4",
            "position_5",
            "Fact",
            "Value",
        ]

        # If we have more columns than defaults, add generic names
        if num_columns > len(default_columns):
            for i in range(len(default_columns), num_columns):
                default_columns.append(f"column_{i + 1}")

        # Return only the number of columns we need
        return default_columns[:num_columns]

    def _create_worksheet_with_validation(
        self, sheet_name: str
    ) -> xlsxwriter.worksheet.Worksheet:
        """
        Create a worksheet with name validation and conflict resolution.

        Args:
            sheet_name: Desired sheet name

        Returns:
            Created worksheet
        """
        # Sanitize the sheet name
        sanitized_name = self._sanitize_sheet_name(sheet_name)

        # Handle name conflicts by adding a counter
        original_name = sanitized_name
        counter = 1

        while sanitized_name in self.worksheets:
            # Truncate original name if needed to make room for counter
            max_base_length = 28  # 31 - 3 characters for counter like "_01"
            base_name = (
                original_name[:max_base_length]
                if len(original_name) > max_base_length
                else original_name
            )
            sanitized_name = f"{base_name}_{counter:02d}"
            counter += 1

        # Create the worksheet
        worksheet = self.workbook.add_worksheet(sanitized_name)
        self.worksheets[sanitized_name] = worksheet
        self.current_rows[sanitized_name] = 0

        log.debug(f"Created worksheet: '{sanitized_name}'")
        return worksheet

    def _get_sheet_summary(self) -> Dict[str, Any]:
        """
        Get summary information about created sheets.

        Returns:
            Dictionary with sheet summary information
        """
        return {
            "total_sheets": len(self.worksheets),
            "sheet_names": list(self.worksheets.keys()),
            "total_rows_by_sheet": dict(self.current_rows),
            "total_data_rows": sum(
                max(0, rows - 1) for rows in self.current_rows.values()
            ),  # -1 for header
        }
