# magic_gateway/api/v1/endpoints/clickhouse.py

import uuid
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel  # Added Request
# BackgroundTasks no longer needed for simple tracking, middleware handles end log
# from fastapi import BackgroundTasks

# Import new dependencies and models
from magic_gateway.api.deps import (
    get_request_tracker_service,
    track_request_details,
    update_request_context,
)
from magic_gateway.tracking.service import RequestTrackingService
from magic_gateway.tracking.models import (
    QueryRequest,  # Keep request models
    CommandRequest,
    DatabaseType,  # Keep this if needed for task_details
)

# Import API models if needed (e.g. for response shaping, though not strictly required here)
# from magic_gateway.api.v1.models import QueryResult  # Define this if needed

from magic_gateway.auth.dependencies import get_current_active_user
from magic_gateway.core.exceptions import ClickHouseException
from magic_gateway.core.logging_config import log
from magic_gateway.db.clickhouse_handler import ClickHouseHandler

router = APIRouter()


# Define a consistent QueryResult model if desired
class QueryResult(BaseModel):
    request_id: uuid.UUID
    rows: List[Dict[str, Any]]
    columns: List[str]
    row_count: int
    database_type: DatabaseType = DatabaseType.POSTGRES
    status: str = "completed"
    cluster: Optional[str] = None  # Add cluster field to response


@router.post("/command", status_code=status.HTTP_200_OK)
async def execute_clickhouse_command(
    request: Request,  # Inject Request
    command_request: CommandRequest,
    request_id: uuid.UUID = Depends(track_request_details),  # Get request_id
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Dict[str, Any]:
    """
    Execute a command against ClickHouse.

    The optional 'cluster' parameter in the request body can be used to specify
    which ClickHouse server to target. If not provided, the primary server is used.

    Parameters:
    - command: str - SQL command to execute
    - params: Optional[Dict[str, Any]] - Command parameters
    - cluster: Optional[str] - Name of the ClickHouse cluster to target (e.g., "default_cluster")

    Returns:
    - Dict with status, message, request_id and cluster information

    Raises:
    - 400 Bad Request: If the command is invalid or the cluster name is unknown
    - 500 Internal Server Error: For unexpected errors during execution
    """
    # Update tracker with username once authenticated
    await update_request_context(request, username=current_user["username"])

    # Add cluster information to task details if provided
    task_details = {}
    if command_request.cluster:
        task_details["cluster"] = command_request.cluster

    if task_details:
        await update_request_context(request, task_details=task_details)

    try:
        # Execute the command, passing request_id and cluster for logging context
        await ClickHouseHandler.execute_command(
            command=command_request.command,
            params=command_request.params,
            query_id=request_id,  # Pass request_id
            cluster=command_request.cluster,  # Pass cluster parameter
        )

        # No need to explicitly call tracker.complete_query, middleware handles it
        return {
            "status": "success",
            "message": "Command executed successfully",
            "request_id": request_id,
            "cluster": command_request.cluster,  # Include cluster in response
        }
    except ClickHouseException as e:
        # Check if this is a cluster-related error
        if "Unknown ClickHouse cluster" in str(e):
            # Get available clusters from registry
            from magic_gateway.db.connection_manager import clickhouse_registry

            # Get available clusters if the method exists
            available_clusters = []
            if hasattr(clickhouse_registry, "get_available_clusters"):
                available_clusters = clickhouse_registry.get_available_clusters()

            # Return a more informative error
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": str(e),
                    "error_code": "INVALID_CLUSTER",
                    "available_clusters": available_clusters,
                },
            )
        # Other ClickHouse errors
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Middleware will catch and log this
        log.error(
            f"ReqID: {str(request_id)[:8]} - Error executing ClickHouse command: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error executing command: {e}",
        )


@router.post("/query", response_model=QueryResult)
async def execute_clickhouse_query(
    request: Request,  # Inject Request
    query_request: QueryRequest,
    # background_tasks: BackgroundTasks, # Removed
    request_id: uuid.UUID = Depends(track_request_details),  # Get request_id
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> Any:
    """
    Execute a read-only query against ClickHouse.

    The optional 'cluster' parameter in the request body can be used to specify
    which ClickHouse server to target. If not provided, the primary server is used.

    Parameters:
    - query: str - SQL query to execute
    - params: Optional[Dict[str, Any]] - Query parameters
    - cluster: Optional[str] - Name of the ClickHouse cluster to target (e.g., "default_cluster")
    - allow_write: bool - Set to True to allow write operations (default: False)

    Returns:
    - QueryResult object with rows, columns, and metadata

    Raises:
    - 400 Bad Request: If the query is invalid or the cluster name is unknown
    - 500 Internal Server Error: For unexpected errors during execution
    """
    # Update tracker with username
    await update_request_context(request, username=current_user["username"])

    # For potential cancellation, store the DB-specific query ID
    # ClickHouse uses query_id setting. We use request_id as the ClickHouse query_id.
    clickhouse_query_id = str(request_id)

    # Create task details with cluster information if provided
    task_details = {
        "db_type": DatabaseType.CLICKHOUSE.value,
        "db_query_id": clickhouse_query_id,
        "query_preview": query_request.query[:200],  # Optional preview
    }

    # Add cluster information if provided
    if query_request.cluster:
        task_details["cluster"] = query_request.cluster

    await update_request_context(request, task_details=task_details)

    try:
        allow_write = getattr(query_request, "allow_write", False)

        # Pass request_id as the ClickHouse query_id for cancellation/logging
        rows, columns = await ClickHouseHandler.execute_query(
            query=query_request.query,
            params=query_request.params,
            query_id=clickhouse_query_id,  # Pass request_id as CH query_id
            allow_write=allow_write,
            cluster=query_request.cluster,  # Pass cluster parameter
        )

        # Create response with cluster information if provided
        result = QueryResult(
            request_id=request_id,
            rows=rows,
            columns=columns,
            row_count=len(rows),
            database_type=DatabaseType.CLICKHOUSE,
            status="completed",
            cluster=query_request.cluster,  # Include cluster in response
        )

        # Middleware handles completion logging
        return result
    except ClickHouseException as e:
        # Check if this is a cluster-related error
        if "Unknown ClickHouse cluster" in str(e):
            # Get available clusters from registry
            from magic_gateway.db.connection_manager import clickhouse_registry

            # Get available clusters if the method exists
            available_clusters = []
            if hasattr(clickhouse_registry, "get_available_clusters"):
                available_clusters = clickhouse_registry.get_available_clusters()

            # Return a more informative error
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": str(e),
                    "error_code": "INVALID_CLUSTER",
                    "available_clusters": available_clusters,
                },
            )
        # Other ClickHouse errors
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        log.error(
            f"ReqID: {str(request_id)[:8]} - Query execution error: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Query execution failed: {e}",
        )


# --- Remove old status and cancel endpoints based on query_id ---
# @router.get("/query/{query_id}", response_model=QueryStatusResponse) ... REMOVE
# @router.delete("/query/{query_id}") ... REMOVE
