-- <PERSON><PERSON><PERSON> to create the necessary tables in the logs database
-- Run this script as a user with appropriate permissions on the logs database

-- Create the api schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS api;

-- Create the request_status_enum type if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'request_status_enum') THEN
        CREATE TYPE request_status_enum AS ENUM (
            'received',
            'authenticating',
            'processing',
            'completed',
            'failed',
            'canceled'
        );
    END IF;
END$$;

-- Create the api_request_history table if it doesn't exist
CREATE TABLE IF NOT EXISTS api.api_request_history (
    request_id UUID PRIMARY KEY,
    endpoint_path VARCHAR(255) NOT NULL,
    http_method VARCHAR(10) NOT NULL,
    status request_status_enum NOT NULL DEFAULT 'received',
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,
    client_ip VARCHAR(50),
    username VARCHAR(100),
    status_code INTEGER,
    error_message TEXT,
    task_details JSONB
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_api_request_history_start_time ON api.api_request_history(start_time DESC);
CREATE INDEX IF NOT EXISTS idx_api_request_history_username ON api.api_request_history(username);
CREATE INDEX IF NOT EXISTS idx_api_request_history_status ON api.api_request_history(status);
CREATE INDEX IF NOT EXISTS idx_api_request_history_endpoint ON api.api_request_history(endpoint_path);

-- Grant permissions to the application user
GRANT USAGE ON SCHEMA api TO "msr.shinyproxy.svc";
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA api TO "msr.shinyproxy.svc";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA api TO "msr.shinyproxy.svc";
