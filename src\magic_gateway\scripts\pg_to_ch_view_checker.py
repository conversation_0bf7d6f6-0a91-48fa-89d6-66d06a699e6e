#!/usr/bin/env python
"""
Script for checking conversion from PostgreSQL view to ClickHouse view.

This script performs the following steps:
1. Get DDL from PostgreSQL
2. Convert to ClickHouse SQL
3. Count rows in PostgreSQL view
4. Count rows in ClickHouse view
5. Return result in a concise format
"""

import asyncio
import uuid
from typing import Dict, Any, Optional

from magic_gateway.db.postgres_handler import <PERSON>gresHandler
from magic_gateway.db.clickhouse_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from magic_gateway.core.logging_config import log
from magic_gateway.core.config import settings
from magic_gateway.utils import get_formatted_column_mapping
from pg_parser.pg_ast_parser import PgAstParser

from magic_gateway.utils.utils import get_axis_type_from_view_name


async def check_pg_to_ch_view_conversion(
    pg_view_name: str,
    id_panel: Optional[int] = None,
    request_id: Optional[uuid.UUID] = None,
    timeout_seconds: Optional[int] = None,
    task_details: Optional[Dict[str, Any]] = None,
    update_task_details_callback: Optional[callable] = None,
) -> Dict[str, Any]:
    """
    Check conversion from PostgreSQL view to ClickHouse view.

    Args:
        pg_view_name: Full name of PostgreSQL view (schema.view_name)
        request_id: Optional request ID for tracking
        timeout_seconds: Optional timeout in seconds
        task_details: Optional dictionary to track query IDs and other task information
        update_task_details_callback: Optional callback function to update task details during execution

    Returns:
        Dictionary with results of the check
    """
    log_context = f"(ReqID: {str(request_id)[:8]})" if request_id else ""
    log.info(
        f"Starting PostgreSQL to ClickHouse view check for {pg_view_name} {log_context}"
    )

    result = {
        "pg_view_name": pg_view_name,
        "pg_ddl": "",
        "ch_sql": "",
        "pg_row_count": 0,
        "ch_row_count": 0,
        "row_count_match": False,
        "status": "failed",
        "error": None,
    }

    try:
        # Step 1: Get DDL from PostgreSQL
        log.info(f"Getting DDL for {pg_view_name} {log_context}")
        pg_object = await PostgresHandler.get_object_description(
            object_name=pg_view_name,
            object_type="view",
            request_id=request_id,
            timeout_seconds=timeout_seconds,
        )

        # Track the query ID in task_details if provided and update immediately
        if task_details is not None and request_id is not None:
            pg_ddl_query_id = str(request_id) + "_pg_ddl"
            if "query_ids" not in task_details:
                task_details["query_ids"] = []
            task_details["query_ids"].append(pg_ddl_query_id)
            # Update the request context with the query ID immediately
            log.debug(
                f"Added query ID to task details: {pg_ddl_query_id} {log_context}"
            )

            # Call the callback if provided
            if update_task_details_callback:
                await update_task_details_callback()

        if not pg_object or not pg_object.get("ddl"):
            result["error"] = f"Failed to retrieve DDL for {pg_view_name}"
            return result

        pg_ddl = pg_object.get("ddl", "")
        result["pg_ddl"] = pg_ddl

        # Step 2: Convert to ClickHouse SQL
        log.info(f"Converting {pg_view_name} to ClickHouse SQL {log_context}")

        # Get table mapping from settings
        table_mapping = settings.get_table_mapping(id_panel)
        log.info(f"Using table mapping from settings: {table_mapping} {log_context}")

        # Get column mapping from database
        log.info(f"Getting column mapping from database {log_context}")
        table_mapping = (
            {"art_product_groups_pet": f"pet.purchases_{id_panel or 1}"}
            if get_axis_type_from_view_name(pg_view_name) in ("axsm", "fltm")
            else settings.get_table_mapping(id_panel)
        )

        raw_column_mapping = await get_formatted_column_mapping()

        # Convert the formatted mapping to a simple dict for pg_parser
        # The pg_parser library expects a flat dictionary, not a nested one
        column_mapping = {}
        if isinstance(raw_column_mapping, dict) and "global" in raw_column_mapping:
            column_mapping = raw_column_mapping["global"]
            log.info(
                f"Using {len(column_mapping)} column mappings from database {log_context}"
            )
        else:
            log.warning(
                f"No column mappings found in database, using empty mapping {log_context}"
            )

        # Convert PostgreSQL view to ClickHouse using the new PgAstParser class
        parser = PgAstParser(
            sql_string=pg_ddl,
            pretty_format=True,
            dialect="clickhouse",  # Use ClickHouse dialect for output
            table_mapping=table_mapping,
            column_mapping=column_mapping,
            parenthesize_where=True,
        )

        # Process the SQL and get the result
        parser_result = parser.process()

        # Extract CTE and query parts
        cte_part = parser_result.get("cte")
        query_parts = parser_result.get("queries", [])

        if not query_parts:
            result["error"] = f"Failed to convert {pg_view_name} to ClickHouse SQL"
            return result

        # Combine parts into a complete ClickHouse view creation statement
        queries_sql = query_parts[0] if query_parts else ""
        for i in range(1, len(query_parts)):
            queries_sql += f"\nUNION ALL\n{query_parts[i]}"

        # Add CTE if it exists
        if cte_part:
            ch_sql = f"WITH {cte_part}\n{queries_sql}"
        else:
            ch_sql = queries_sql

        result["ch_sql"] = ch_sql

        # Step 3: Count rows in PostgreSQL view
        log.info(f"Counting rows in PostgreSQL view {pg_view_name} {log_context}")
        pg_count_query = f"SELECT COUNT(*) as row_count FROM {pg_view_name}"
        pg_count_result, _ = await PostgresHandler.execute_query(
            query=pg_count_query, request_id=request_id, timeout_seconds=timeout_seconds
        )

        # Track the query ID in task_details if provided and update immediately
        if task_details is not None and request_id is not None:
            pg_count_query_id = str(request_id) + "_pg_count"
            if "query_ids" not in task_details:
                task_details["query_ids"] = []
            task_details["query_ids"].append(pg_count_query_id)
            # Update the request context with the query ID immediately
            log.debug(
                f"Added query ID to task details: {pg_count_query_id} {log_context}"
            )

            # Call the callback if provided
            if update_task_details_callback:
                await update_task_details_callback()

        if pg_count_result and len(pg_count_result) > 0:
            pg_row_count = pg_count_result[0].get("row_count", 0)
            result["pg_row_count"] = pg_row_count
        else:
            result["error"] = f"Failed to count rows in PostgreSQL view {pg_view_name}"
            return result

        # Step 4: Count rows in ClickHouse using the converted query
        log.info(f"Counting rows in equivalent ClickHouse query {log_context}")
        # For simplicity, just use the main query part without CTE for counting
        # This avoids complex WITH syntax issues in ClickHouse
        ch_count_query = f"SELECT COUNT(*) as row_count FROM ({ch_sql})"

        try:
            # Generate a unique query ID for ClickHouse
            ch_query_id = str(request_id) + "_ch_count" if request_id else None

            # Track the query ID in task_details if provided and update immediately
            if task_details is not None and ch_query_id is not None:
                if "query_ids" not in task_details:
                    task_details["query_ids"] = []
                task_details["query_ids"].append(ch_query_id)
                # Update the request context with the query ID immediately
                log.debug(
                    f"Added query ID to task details: {ch_query_id} {log_context}"
                )

                # Call the callback if provided
                if update_task_details_callback:
                    await update_task_details_callback()

            ch_count_result, _ = await ClickHouseHandler.execute_query(
                query=ch_count_query,
                query_id=ch_query_id,
                allow_write=False,
            )

            if ch_count_result and len(ch_count_result) > 0:
                ch_row_count = ch_count_result[0].get("row_count", 0)
                result["ch_row_count"] = ch_row_count
            else:
                result["error"] = "Failed to count rows in ClickHouse query"
                return result
        except Exception as e:
            result["error"] = f"Error executing ClickHouse query: {str(e)}"
            log.error(
                f"Error executing ClickHouse query {log_context}: {e}", exc_info=True
            )
            return result

        # Step 5: Compare row counts
        result["row_count_match"] = result["pg_row_count"] == result["ch_row_count"]
        result["status"] = "success"

        log.info(
            f"PostgreSQL to ClickHouse view check completed for {pg_view_name} {log_context}"
        )
        return result

    except Exception as e:
        log.error(
            f"Error during PostgreSQL to ClickHouse view check {log_context}: {e}",
            exc_info=True,
        )
        result["error"] = str(e)
        return result


if __name__ == "__main__":
    # This allows the script to be run directly for testing
    import sys

    if len(sys.argv) < 2:
        print("Usage: python pg_to_ch_view_checker.py <pg_view_name>")
        sys.exit(1)

    pg_view_name = sys.argv[1]

    # Run the async function
    async def run_check():
        result = await check_pg_to_ch_view_conversion(pg_view_name)
        print("\n=== PostgreSQL to ClickHouse View Check Results ===")
        print(f"PostgreSQL View: {result['pg_view_name']}")
        print(f"PostgreSQL Row Count: {result['pg_row_count']}")
        print(f"ClickHouse Row Count: {result['ch_row_count']}")
        print(f"Row Count Match: {result['row_count_match']}")
        print(f"Status: {result['status']}")
        if result["error"]:
            print(f"Error: {result['error']}")

    asyncio.run(run_check())
