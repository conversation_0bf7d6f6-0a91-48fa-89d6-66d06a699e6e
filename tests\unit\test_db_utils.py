"""
Unit tests for database utility functions.
"""

import pytest

from src.magic_gateway.utils.db_utils import (
    get_database_from_table,
    should_use_cluster_for_database,
    get_cluster_for_table,
)


class TestDatabaseUtils:
    """Tests for database utility functions."""

    @pytest.mark.parametrize(
        "table_name,expected_database",
        [
            ("job_result.table_name", "job_result"),
            ("kpi_results.result_table_123", "kpi_results"),
            ("database.schema.table", "database"),
            ("table_without_database", ""),
            ("", ""),
            (None, ""),  # Test with None value
        ],
    )
    def test_get_database_from_table(self, table_name, expected_database):
        """Test extracting database name from table name."""
        result = get_database_from_table(table_name)
        assert result == expected_database

    @pytest.mark.parametrize(
        "database_name,should_use_cluster",
        [
            ("job_result", True),
            ("JOB_RESULT", True),  # Case insensitive
            ("Job_Result", True),  # Mixed case
            ("kpi_results", False),
            ("other_database", False),
            ("", False),
            (None, False),  # Test with None value
        ],
    )
    def test_should_use_cluster_for_database(self, database_name, should_use_cluster):
        """Test determining if cluster should be used based on database name."""
        result = should_use_cluster_for_database(database_name)
        assert result == should_use_cluster

    @pytest.mark.parametrize(
        "table_name,explicit_cluster,expected_cluster",
        [
            ("job_result.table_name", None, "default_cluster"),
            ("job_result.table_name", "custom_cluster", "custom_cluster"),
            ("kpi_results.result_table_123", None, None),
            ("kpi_results.result_table_123", "custom_cluster", "custom_cluster"),
            ("table_without_database", None, None),
            ("", None, None),
            (None, None, None),
        ],
    )
    def test_get_cluster_for_table(
        self, table_name, explicit_cluster, expected_cluster
    ):
        """Test determining cluster for table with various inputs."""
        result = get_cluster_for_table(table_name, explicit_cluster)
        assert result == expected_cluster
