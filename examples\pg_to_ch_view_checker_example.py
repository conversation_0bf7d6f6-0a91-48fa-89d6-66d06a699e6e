#!/usr/bin/env python
"""
Example script demonstrating the PostgreSQL to ClickHouse view checker API endpoint.
"""

import json
import sys
import os
import logging
import requests

# Add the parent directory to the path so we can import the api_client
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from examples.api_client import MagicGatewayClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def print_json(data):
    """Print JSON data in a readable format."""
    print(json.dumps(data, indent=2))


class PgToChViewCheckerExample:
    """Example class for PostgreSQL to ClickHouse view checker."""

    def __init__(self):
        """Initialize the example."""
        self.client = MagicGatewayClient()
        # Use default credentials from environment or hardcoded test credentials
        self.client.authenticate(username="rnvaga", password="!Newupgrade1")

    def check_view_conversion(
        self,
        pg_view_name,
        timeout_seconds=None,
    ):
        """Check conversion from PostgreSQL view to ClickHouse view."""
        print(f"\n=== Checking conversion for {pg_view_name} ===")

        checker_url = f"{self.client.base_url}/api/v1/scripts/pg-to-ch-view-checker"
        checker_data = {
            "pg_view_name": pg_view_name,
        }

        # Add timeout if provided
        if timeout_seconds:
            checker_data["timeout_seconds"] = timeout_seconds
            print(f"Using custom timeout of {timeout_seconds} seconds")

        try:
            print(f"Sending request to check {pg_view_name}...")
            # Add timeout to prevent hanging indefinitely
            try:
                response = self.client._make_authenticated_request(
                    "POST", checker_url, json=checker_data, timeout=300
                )
                print("Response received.")
                result = response.json()
                print("Check successful!")
            except requests.exceptions.Timeout:
                print(
                    "Request timed out after 300 seconds (5 minutes). The server might be processing a complex view."
                )
                return None

            print("\n=== Check Results ===")
            print(f"PostgreSQL View: {result['pg_view_name']}")
            print(f"PostgreSQL Row Count: {result['pg_row_count']}")
            print(f"ClickHouse Row Count: {result['ch_row_count']}")
            print(f"Row Count Match: {result['row_count_match']}")
            print(f"Status: {result['status']}")

            if result["error"]:
                print(f"Error: {result['error']}")

            print("\n=== PostgreSQL DDL ===")
            print(result["pg_ddl"])

            print("\n=== ClickHouse SQL ===")
            print(result["ch_sql"])

            return result
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to check view conversion: {e}")
            print(f"Error: {e}")
            return None


def main():
    """Run the example."""
    try:
        # Initialize the example
        print("Initializing PgToChViewChecker example...")
        print(
            "Note: This example requires a running MagicGateway server with the PostgreSQL to ClickHouse view checker endpoint implemented."
        )
        print(
            "The example will timeout after 300 seconds (5 minutes) if the server takes too long to respond."
        )
        print(
            "\nYou can modify the script to use different PostgreSQL views or adjust the timeout."
        )

        example = PgToChViewCheckerExample()

        # Check if authentication was successful
        if not example.client.access_token:
            print("\nAuthentication failed. Please check your credentials.")
            print(
                "This example requires a running MagicGateway server and valid credentials."
            )
            print(
                "You can modify the script to use your own credentials or set up test users."
            )
            return

        print("\nAuthentication successful!")

        # Example: Check a view with a timeout
        print("\nRunning Example: Check a view with 60s timeout")
        example.check_view_conversion(
            "cmt_international_cluster_1.axsm_pg_detergents_ecom_v1",
            timeout_seconds=60,  # Use a longer timeout for complex views
        )

    except Exception as e:
        print(f"\nAn error occurred: {e}")
        print("\nThis example requires:")
        print("1. A running MagicGateway server")
        print("2. Valid authentication credentials")
        print("3. The PostgreSQL to ClickHouse view checker endpoint to be implemented")


if __name__ == "__main__":
    print("Starting PgToChViewChecker example script...")
    main()
    print("Example script completed.")
