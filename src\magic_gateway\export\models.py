"""
Data models for the export optimization system.

This module defines the core data structures used throughout the export pipeline,
including context objects, configuration options, and result types.
"""

import uuid
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from magic_gateway.db.connection_manager import ClickHouseConnectionManager


class ExportFormat(str, Enum):
    """Supported export formats with enhanced layout support."""
    CSV = "csv"
    EXCEL = "excel"
    PARQUET = "parquet"


class ExcelLayout(str, Enum):
    """Excel layout options for data presentation."""
    VERTICAL = "vertical"      # Data as stored in database
    HORIZONTAL = "horizontal"  # Facts as columns


class TempFileStrategy(str, Enum):
    """Strategy for managing temporary files during export."""
    MEMORY_ONLY = "memory"
    SINGLE_TEMP = "single_temp"
    PERIOD_CHUNKS = "period_chunks"


@dataclass
class JobMetadata:
    """Comprehensive job metadata for export optimization."""
    job_id: int
    table_name: str
    database_name: str
    total_rows: int
    available_periods: List[str]
    facts_list: List[str]
    job_info: Dict[str, Any]  # Changed from axes_info to job_info
    estimated_size_mb: float
    created_at: datetime


@dataclass
class SizeEstimate:
    """Export size estimates for different formats."""
    csv_size_mb: float
    excel_vertical_size_mb: float
    excel_horizontal_size_mb: float
    parquet_size_mb: float
    rows_per_period: Dict[str, int]


@dataclass
class OptimizationStrategy:
    """Strategy for optimizing export based on metadata."""
    use_streaming: bool
    chunk_size: int
    period_chunking: bool
    memory_limit_mb: int
    temp_file_strategy: TempFileStrategy


@dataclass
class ExportOptions:
    """Enhanced export options with new format and layout support."""
    format: ExportFormat
    separate_periods: bool = False
    horizontal_facts: bool = False
    excel_layout: ExcelLayout = ExcelLayout.VERTICAL
    compression: bool = True
    timeout_seconds: Optional[int] = None


@dataclass
class ConversionOptions:
    """Options for format conversion pipeline."""
    separate_periods: bool = False
    horizontal_facts: bool = False
    excel_layout: ExcelLayout = ExcelLayout.VERTICAL
    compression: bool = True
    metadata: Optional[JobMetadata] = None


@dataclass
class ExportContext:
    """
    Context object containing all information needed for an export operation.
    
    This serves as the primary data structure passed between components
    in the export pipeline.
    """
    job_id: int
    request_id: uuid.UUID
    table_name: str
    database_name: str
    format: ExportFormat
    options: ExportOptions
    metadata: Optional[JobMetadata] = None
    size_estimate: Optional[SizeEstimate] = None
    optimization_strategy: Optional[OptimizationStrategy] = None
    connection_manager: Optional[ClickHouseConnectionManager] = None


@dataclass
class ConnectionSelection:
    """
    Result of connection pool selection process.
    
    Contains the selected connection manager along with metadata
    about the selection decision.
    """
    manager: ClickHouseConnectionManager
    cluster_name: Optional[str]
    is_fallback: bool
    routing_reason: str