"""Middleware package for MagicGateway API.

This package contains all middleware components for the MagicGateway application:
- RequestTrackingMiddleware: Tracks and logs all API requests
- TokenRefreshMiddleware: Handles automatic JWT token refresh
"""

from .request_tracking import RequestTrackingMiddleware, set_request_tracker_instance
from .token_refresh import TokenRefreshMiddleware

__all__ = [
    "RequestTrackingMiddleware", 
    "TokenRefreshMiddleware", 
    "set_request_tracker_instance"
]