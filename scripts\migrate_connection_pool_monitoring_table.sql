-- Migration script to fix the pool_type column length issue
-- This script updates the existing connection_pool_monitoring table to allow longer pool_type values

-- Connect to the logs database and run this script as a user with appropriate permissions

-- Alter the pool_type column to allow longer values
ALTER TABLE api.connection_pool_monitoring 
ALTER COLUMN pool_type TYPE VARCHAR(50);

-- Update the comment to reflect the new allowed values
COMMENT ON COLUMN api.connection_pool_monitoring.pool_type IS 'Pool type identifier: postgres, clickhouse, logs, or clickhouse_cluster_name';

-- Verify the change
SELECT column_name, data_type, character_maximum_length 
FROM information_schema.columns 
WHERE table_schema = 'api' 
  AND table_name = 'connection_pool_monitoring' 
  AND column_name = 'pool_type';