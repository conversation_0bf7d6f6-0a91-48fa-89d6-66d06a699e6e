"""Service for monitoring connection pools and other system metrics."""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any

from magic_gateway.core.config import settings
from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import (
    postgres_connection_manager,
    logs_connection_manager,
    clickhouse_registry,
)
from magic_gateway.db.logs_handler import LogsHandler
from magic_gateway.monitoring.models import ConnectionPoolMetrics, ConnectionPoolStatus


class ConnectionPoolMonitoringService:
    """Service for monitoring connection pools."""

    def __init__(self):
        """Initialize the monitoring service."""
        self._running = False
        self._task: Optional[asyncio.Task] = None
        self._metrics_history: Dict[str, List[ConnectionPoolMetrics]] = {
            "postgres": [],
            "clickhouse": [],
            "logs": [],
        }
        self._max_history_items = 100  # Keep last 100 metrics in memory
        self._collection_interval = 60  # Collect metrics every 60 seconds by default
        self._last_metrics: Dict[str, ConnectionPoolMetrics] = {}
        self._acquisition_times: Dict[str, List[float]] = {
            "postgres": [],
            "clickhouse": [],
            "logs": [],
        }
        self._usage_times: Dict[str, List[float]] = {
            "postgres": [],
            "clickhouse": [],
            "logs": [],
        }
        self._max_timing_samples = 1000  # Maximum number of timing samples to keep

        # Script-specific tracking
        self._script_acquisition_times: Dict[str, Dict[str, List[float]]] = {
            "postgres": {},
            "clickhouse": {},
            "logs": {},
        }
        self._script_usage_times: Dict[str, Dict[str, List[float]]] = {
            "postgres": {},
            "clickhouse": {},
            "logs": {},
        }
        self._script_connection_counts: Dict[str, Dict[str, int]] = {
            "postgres": {},
            "clickhouse": {},
            "logs": {},
        }
        self._max_script_timing_samples = (
            100  # Maximum script timing samples per script
        )

        # Add cluster-specific tracking if configured
        if (
            hasattr(settings, "has_clickhouse_cluster")
            and settings.has_clickhouse_cluster
        ):
            cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
            pool_type = f"clickhouse_{cluster_name}"
            self._metrics_history[pool_type] = []
            self._acquisition_times[pool_type] = []
            self._usage_times[pool_type] = []
            # Script-specific tracking for cluster
            self._script_acquisition_times[pool_type] = {}
            self._script_usage_times[pool_type] = {}
            self._script_connection_counts[pool_type] = {}

    async def start(self, collection_interval: int = 60) -> None:
        """
        Start the monitoring service.

        Args:
            collection_interval: Interval in seconds between metrics collections
        """
        if self._running:
            log.warning("Connection pool monitoring service is already running")
            return

        self._collection_interval = collection_interval
        self._running = True
        self._task = asyncio.create_task(self._monitoring_loop())
        log.info(
            f"Connection pool monitoring service started with interval {collection_interval}s"
        )

    async def stop(self) -> None:
        """Stop the monitoring service."""
        if not self._running or not self._task:
            log.warning("Connection pool monitoring service is not running")
            return

        self._running = False
        if not self._task.done():
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        log.info("Connection pool monitoring service stopped")

    async def _monitoring_loop(self) -> None:
        """Background task that periodically collects and stores metrics."""
        while self._running:
            try:
                # Collect metrics for all connection pools
                await self.collect_and_store_metrics()

                # Sleep until next collection
                await asyncio.sleep(self._collection_interval)
            except asyncio.CancelledError:
                log.info("Connection pool monitoring task cancelled")
                break
            except Exception as e:
                log.error(
                    f"Error in connection pool monitoring loop: {e}", exc_info=True
                )
                # Continue running despite errors
                await asyncio.sleep(self._collection_interval)

    async def collect_and_store_metrics(self) -> None:
        """Collect metrics from all connection pools and store them."""
        # Collect metrics
        postgres_metrics = await self._collect_postgres_metrics()
        clickhouse_metrics = await self._collect_clickhouse_metrics()
        logs_metrics = await self._collect_logs_metrics()

        # Collect metrics for ClickHouse cluster if configured
        clickhouse_cluster_metrics = None
        if (
            hasattr(settings, "has_clickhouse_cluster")
            and settings.has_clickhouse_cluster
        ):
            clickhouse_cluster_metrics = (
                await self._collect_clickhouse_cluster_metrics()
            )

        # Store metrics in memory
        if postgres_metrics:
            self._metrics_history["postgres"].append(postgres_metrics)
            self._last_metrics["postgres"] = postgres_metrics
            # Trim history if needed
            if len(self._metrics_history["postgres"]) > self._max_history_items:
                self._metrics_history["postgres"] = self._metrics_history["postgres"][
                    -self._max_history_items :
                ]

        if clickhouse_metrics:
            self._metrics_history["clickhouse"].append(clickhouse_metrics)
            self._last_metrics["clickhouse"] = clickhouse_metrics
            # Trim history if needed
            if len(self._metrics_history["clickhouse"]) > self._max_history_items:
                self._metrics_history["clickhouse"] = self._metrics_history[
                    "clickhouse"
                ][-self._max_history_items :]

        if logs_metrics:
            self._metrics_history["logs"].append(logs_metrics)
            self._last_metrics["logs"] = logs_metrics
            # Trim history if needed
            if len(self._metrics_history["logs"]) > self._max_history_items:
                self._metrics_history["logs"] = self._metrics_history["logs"][
                    -self._max_history_items :
                ]

        # Store cluster metrics if available
        if clickhouse_cluster_metrics:
            cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
            pool_type = f"clickhouse_{cluster_name}"
            self._metrics_history[pool_type].append(clickhouse_cluster_metrics)
            self._last_metrics[pool_type] = clickhouse_cluster_metrics
            # Trim history if needed
            if len(self._metrics_history[pool_type]) > self._max_history_items:
                self._metrics_history[pool_type] = self._metrics_history[pool_type][
                    -self._max_history_items :
                ]

        # Store metrics in database
        try:
            await self._store_metrics_in_database(
                [
                    m
                    for m in [
                        postgres_metrics,
                        clickhouse_metrics,
                        logs_metrics,
                        clickhouse_cluster_metrics,
                    ]
                    if m is not None
                ]
            )
        except Exception as e:
            log.error(
                f"Failed to store connection pool metrics in database: {e}",
                exc_info=True,
            )

    async def _collect_postgres_metrics(self) -> Optional[ConnectionPoolMetrics]:
        """
        Collect metrics from the PostgreSQL connection pool.

        Note: This method was updated to use the correct psycopg_pool 3.2.6 stats format.
        Previously, active_connections and idle_connections were always 0 because the
        monitoring service was looking for keys that don't exist in the actual pool stats.
        """
        if (
            not postgres_connection_manager.initialized
            or not postgres_connection_manager._pool
        ):
            return None

        try:
            # Get pool stats from psycopg_pool
            pool_stats = postgres_connection_manager._pool.get_stats()

            # Log the pool stats for debugging
            log.debug(f"PostgreSQL pool stats: {pool_stats}")

            # Calculate timing statistics
            acquisition_times = self._acquisition_times.get("postgres", [])
            usage_times = self._usage_times.get("postgres", [])

            avg_acquisition_time = (
                sum(acquisition_times) / len(acquisition_times)
                if acquisition_times
                else None
            )
            max_acquisition_time = max(acquisition_times) if acquisition_times else None
            avg_usage_time = (
                sum(usage_times) / len(usage_times) if usage_times else None
            )
            max_usage_time = max(usage_times) if usage_times else None

            # Extract metrics using correct psycopg_pool 3.2.6 format
            # psycopg_pool.get_stats() returns:
            # - pool_size: Current number of connections in the pool
            # - pool_available: Number of connections available for use
            # - requests_waiting: Number of requests waiting for a connection
            # - usage_ms: Total time connections have been in use
            # - requests_num: Total number of requests served
            # - requests_queued: Total number of requests queued
            # - requests_wait_ms: Total time requests have waited for a connection
            # - requests_errors: Total number of requests that resulted in an error
            # - returns_bad: Number of connections returned in a bad state

            pool_size = pool_stats.get("pool_size", 0)
            pool_available = pool_stats.get("pool_available", 0)

            # Calculate active and idle connections from psycopg_pool stats
            # Active connections = total pool size - available connections
            active_connections = pool_size - pool_available
            idle_connections = pool_available

            # For total connections created, we don't have a direct equivalent
            # Use pool_size as an approximation since it represents current connections
            total_created = pool_stats.get("requests_num", pool_size)

            # Create metrics object
            metrics = ConnectionPoolMetrics(
                timestamp=datetime.now(),
                pool_type="postgres",
                active_connections=active_connections,
                idle_connections=idle_connections,
                min_size=settings.POSTGRES_MIN_CONNECTIONS,
                max_size=settings.POSTGRES_MAX_CONNECTIONS,
                total_connections_created=total_created,
                total_connections_closed=pool_stats.get("returns_bad", 0),
                connection_timeouts=pool_stats.get("requests_queued", 0),
                connection_errors=pool_stats.get("requests_errors", 0),
                avg_acquisition_time_ms=avg_acquisition_time,
                max_acquisition_time_ms=max_acquisition_time,
                avg_usage_time_ms=avg_usage_time,
                max_usage_time_ms=max_usage_time,
                additional_metrics={
                    "pool_name": f"{settings.POSTGRES_APPLICATION_NAME}_pool",
                    "raw_stats": str(
                        pool_stats
                    ),  # Store raw stats as string for debugging
                    "requests_waiting": pool_stats.get("requests_waiting", 0),
                    "usage_ms": pool_stats.get("usage_ms", 0),
                    "requests_num": pool_stats.get("requests_num", 0),
                    "requests_queued": pool_stats.get("requests_queued", 0),
                    "requests_wait_ms": pool_stats.get("requests_wait_ms", 0),
                },
                script_usage_stats=self.get_script_usage_stats("postgres"),
            )
            return metrics
        except Exception as e:
            log.error(
                f"Error collecting PostgreSQL connection pool metrics: {e}",
                exc_info=True,
            )
            return None

    async def _collect_clickhouse_metrics(self) -> Optional[ConnectionPoolMetrics]:
        """Collect metrics from the ClickHouse connection pool."""
        # Use the registry to get the primary connection manager
        if not clickhouse_registry.initialized:
            log.debug("Metrics Collection: ClickHouse registry not initialized")
            return None

        try:
            # Get the primary manager from the registry
            log.debug(
                f"Metrics Collection: Available clusters in registry: {clickhouse_registry.get_available_clusters()}"
            )
            primary_manager = clickhouse_registry.get_manager("primary")
            log.debug(
                f"Metrics Collection: Primary manager initialized: {primary_manager.initialized}"
            )

            if not primary_manager.initialized or not primary_manager._pool:
                log.debug(
                    "Metrics Collection: Primary manager not initialized or no pool available"
                )
                return None

            # Get pool state
            state = primary_manager._pool

            # Calculate timing statistics
            acquisition_times = self._acquisition_times.get("clickhouse", [])
            usage_times = self._usage_times.get("clickhouse", [])

            avg_acquisition_time = (
                sum(acquisition_times) / len(acquisition_times)
                if acquisition_times
                else None
            )
            max_acquisition_time = max(acquisition_times) if acquisition_times else None
            avg_usage_time = (
                sum(usage_times) / len(usage_times) if usage_times else None
            )
            max_usage_time = max(usage_times) if usage_times else None

            # Get timeout and error counts from last metrics if available
            connection_timeouts = 0
            connection_errors = 0
            if hasattr(self, "_last_metrics") and "clickhouse" in self._last_metrics:
                last_metrics = self._last_metrics["clickhouse"]
                connection_timeouts = getattr(last_metrics, "connection_timeouts", 0)
                connection_errors = getattr(last_metrics, "connection_errors", 0)

            # Create metrics object
            metrics = ConnectionPoolMetrics(
                timestamp=datetime.now(),
                pool_type="clickhouse",
                active_connections=state.active_connections,
                idle_connections=len(state.idle),
                min_size=state.min_size,
                max_size=state.max_size,
                total_connections_created=state.active_connections
                + len(state.idle),  # Approximation
                connection_timeouts=connection_timeouts,
                connection_errors=connection_errors,
                avg_acquisition_time_ms=avg_acquisition_time,
                max_acquisition_time_ms=max_acquisition_time,
                avg_usage_time_ms=avg_usage_time,
                max_usage_time_ms=max_usage_time,
                additional_metrics={
                    "connect_timeout": state.connect_timeout,
                    "ping_timeout": state.ping_timeout,
                    "semaphore_value": state.semaphore._value,  # Access internal state
                },
                script_usage_stats=self.get_script_usage_stats("clickhouse"),
            )
            return metrics
        except Exception as e:
            log.error(
                f"Error collecting ClickHouse connection pool metrics: {e}",
                exc_info=True,
            )
            return None

    async def _collect_logs_metrics(self) -> Optional[ConnectionPoolMetrics]:
        """Collect metrics from the Logs connection."""
        if (
            not logs_connection_manager.initialized
            or not logs_connection_manager._connection
        ):
            return None

        try:
            # For logs connection, we don't have a pool but a single connection
            # Calculate timing statistics
            acquisition_times = self._acquisition_times.get("logs", [])
            usage_times = self._usage_times.get("logs", [])

            avg_acquisition_time = (
                sum(acquisition_times) / len(acquisition_times)
                if acquisition_times
                else None
            )
            max_acquisition_time = max(acquisition_times) if acquisition_times else None
            avg_usage_time = (
                sum(usage_times) / len(usage_times) if usage_times else None
            )
            max_usage_time = max(usage_times) if usage_times else None

            # Create metrics object
            metrics = ConnectionPoolMetrics(
                timestamp=datetime.now(),
                pool_type="logs",
                active_connections=1 if logs_connection_manager._connection else 0,
                idle_connections=0,  # Single connection, not a pool
                min_size=1,
                max_size=1,
                total_connections_created=1,  # Approximation
                connection_timeouts=0,  # Not tracked in current implementation
                connection_errors=0,  # Not tracked in current implementation
                avg_acquisition_time_ms=avg_acquisition_time,
                max_acquisition_time_ms=max_acquisition_time,
                avg_usage_time_ms=avg_usage_time,
                max_usage_time_ms=max_usage_time,
                additional_metrics={},
                script_usage_stats=self.get_script_usage_stats("logs"),
            )
            return metrics
        except Exception as e:
            log.error(f"Error collecting Logs connection metrics: {e}", exc_info=True)
            return None

    async def _collect_clickhouse_cluster_metrics(
        self,
    ) -> Optional[ConnectionPoolMetrics]:
        """Collect metrics from the ClickHouse cluster connection pool."""
        # Check if cluster is configured
        if (
            not hasattr(settings, "has_clickhouse_cluster")
            or not settings.has_clickhouse_cluster
        ):
            return None

        # Check if registry is initialized
        if not clickhouse_registry.initialized:
            return None

        # Get cluster name and manager
        cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
        pool_type = f"clickhouse_{cluster_name}"

        try:
            # Get the cluster manager
            cluster_manager = clickhouse_registry.get_manager(cluster_name)

            if not cluster_manager.initialized or not cluster_manager._pool:
                return None

            # Get pool state
            state = cluster_manager._pool

            # Calculate timing statistics
            acquisition_times = self._acquisition_times.get(pool_type, [])
            usage_times = self._usage_times.get(pool_type, [])

            avg_acquisition_time = (
                sum(acquisition_times) / len(acquisition_times)
                if acquisition_times
                else None
            )
            max_acquisition_time = max(acquisition_times) if acquisition_times else None
            avg_usage_time = (
                sum(usage_times) / len(usage_times) if usage_times else None
            )
            max_usage_time = max(usage_times) if usage_times else None

            # Get timeout and error counts from last metrics if available
            connection_timeouts = 0
            connection_errors = 0
            if hasattr(self, "_last_metrics") and pool_type in self._last_metrics:
                last_metrics = self._last_metrics[pool_type]
                connection_timeouts = getattr(last_metrics, "connection_timeouts", 0)
                connection_errors = getattr(last_metrics, "connection_errors", 0)

            # Create metrics object
            metrics = ConnectionPoolMetrics(
                timestamp=datetime.now(),
                pool_type=pool_type,
                active_connections=state.active_connections,
                idle_connections=len(state.idle),
                min_size=state.min_size,
                max_size=state.max_size,
                total_connections_created=state.active_connections
                + len(state.idle),  # Approximation
                connection_timeouts=connection_timeouts,
                connection_errors=connection_errors,
                avg_acquisition_time_ms=avg_acquisition_time,
                max_acquisition_time_ms=max_acquisition_time,
                avg_usage_time_ms=avg_usage_time,
                max_usage_time_ms=max_usage_time,
                additional_metrics={
                    "connect_timeout": state.connect_timeout,
                    "ping_timeout": state.ping_timeout,
                    "semaphore_value": state.semaphore._value,  # Access internal state
                    "cluster_name": cluster_name,
                },
                script_usage_stats=self.get_script_usage_stats(pool_type),
            )
            return metrics
        except Exception as e:
            log.error(
                f"Error collecting ClickHouse cluster connection pool metrics: {e}",
                exc_info=True,
            )
            return None

    async def _store_metrics_in_database(
        self, metrics_list: List[ConnectionPoolMetrics]
    ) -> None:
        """Store metrics in the logs database."""
        if not metrics_list:
            return

        # Insert each metric into the database
        for metrics in metrics_list:
            # Convert metrics to dict
            params = metrics.model_dump(mode="json")

            # Convert additional_metrics and script_usage_stats to JSON strings
            import json

            if isinstance(params["additional_metrics"], dict):
                params["additional_metrics"] = json.dumps(params["additional_metrics"])

            if isinstance(params["script_usage_stats"], dict):
                params["script_usage_stats"] = json.dumps(params["script_usage_stats"])

            # Try to insert with script_usage_stats column first (new schema)
            query_with_script_stats = """
            INSERT INTO api.connection_pool_monitoring (
                timestamp, pool_type, active_connections, idle_connections,
                min_size, max_size, total_connections_created, total_connections_closed,
                connection_timeouts, connection_errors, avg_acquisition_time_ms,
                max_acquisition_time_ms, avg_usage_time_ms, max_usage_time_ms,
                additional_metrics, script_usage_stats
            ) VALUES (
                %(timestamp)s, %(pool_type)s, %(active_connections)s, %(idle_connections)s,
                %(min_size)s, %(max_size)s, %(total_connections_created)s, %(total_connections_closed)s,
                %(connection_timeouts)s, %(connection_errors)s, %(avg_acquisition_time_ms)s,
                %(max_acquisition_time_ms)s, %(avg_usage_time_ms)s, %(max_usage_time_ms)s,
                %(additional_metrics)s, %(script_usage_stats)s
            )
            """

            # Fallback query without script_usage_stats column (old schema)
            query_without_script_stats = """
            INSERT INTO api.connection_pool_monitoring (
                timestamp, pool_type, active_connections, idle_connections,
                min_size, max_size, total_connections_created, total_connections_closed,
                connection_timeouts, connection_errors, avg_acquisition_time_ms,
                max_acquisition_time_ms, avg_usage_time_ms, max_usage_time_ms,
                additional_metrics
            ) VALUES (
                %(timestamp)s, %(pool_type)s, %(active_connections)s, %(idle_connections)s,
                %(min_size)s, %(max_size)s, %(total_connections_created)s, %(total_connections_closed)s,
                %(connection_timeouts)s, %(connection_errors)s, %(avg_acquisition_time_ms)s,
                %(max_acquisition_time_ms)s, %(avg_usage_time_ms)s, %(max_usage_time_ms)s,
                %(additional_metrics)s
            )
            """

            try:
                # Try with script_usage_stats column first
                await LogsHandler.execute_command(query_with_script_stats, params)
                log.debug(
                    f"Stored connection pool metrics for {metrics.pool_type} in database (with script stats)"
                )
            except Exception as e:
                # Check if the error is about missing column
                error_msg = str(e).lower()
                if "script_usage_stats" in error_msg and (
                    "does not exist" in error_msg or "column" in error_msg
                ):
                    # Column doesn't exist, try without it
                    try:
                        # Remove script_usage_stats from params for fallback query
                        fallback_params = {
                            k: v for k, v in params.items() if k != "script_usage_stats"
                        }
                        await LogsHandler.execute_command(
                            query_without_script_stats, fallback_params
                        )
                        log.debug(
                            f"Stored connection pool metrics for {metrics.pool_type} in database (without script stats - schema not migrated)"
                        )
                    except Exception as fallback_e:
                        log.error(
                            f"Failed to store connection pool metrics in database (fallback): {fallback_e}",
                            exc_info=True,
                        )
                else:
                    log.error(
                        f"Failed to store connection pool metrics in database: {e}",
                        exc_info=True,
                    )
                # Continue with next metric

    def record_acquisition_time(
        self, pool_type: str, time_ms: float, script_name: Optional[str] = None
    ) -> None:
        """
        Record the time taken to acquire a connection from the pool.

        Args:
            pool_type: Type of the pool ('postgres', 'clickhouse', 'logs')
            time_ms: Time in milliseconds
            script_name: Optional script name for script-specific tracking
        """
        if pool_type not in self._acquisition_times:
            self._acquisition_times[pool_type] = []

        self._acquisition_times[pool_type].append(time_ms)

        # Trim if needed
        if len(self._acquisition_times[pool_type]) > self._max_timing_samples:
            self._acquisition_times[pool_type] = self._acquisition_times[pool_type][
                -self._max_timing_samples :
            ]

        # Record script-specific metrics if script_name is provided
        if script_name:
            self._record_script_acquisition_time(pool_type, script_name, time_ms)

    def record_usage_time(
        self, pool_type: str, time_ms: float, script_name: Optional[str] = None
    ) -> None:
        """
        Record the time a connection was used.

        Args:
            pool_type: Type of the pool ('postgres', 'clickhouse', 'logs')
            time_ms: Time in milliseconds
            script_name: Optional script name for script-specific tracking
        """
        if pool_type not in self._usage_times:
            self._usage_times[pool_type] = []

        self._usage_times[pool_type].append(time_ms)

        # Trim if needed
        if len(self._usage_times[pool_type]) > self._max_timing_samples:
            self._usage_times[pool_type] = self._usage_times[pool_type][
                -self._max_timing_samples :
            ]

        # Record script-specific metrics if script_name is provided
        if script_name:
            self._record_script_usage_time(pool_type, script_name, time_ms)

    def get_connection_pool_status(self) -> Dict[str, ConnectionPoolStatus]:
        """
        Get the current status of all connection pools.

        Returns:
            Dictionary mapping pool types to their status
        """
        result = {}

        # Process PostgreSQL pool
        if "postgres" in self._last_metrics:
            metrics = self._last_metrics["postgres"]
            total_connections = metrics.active_connections + metrics.idle_connections
            utilization = (
                (total_connections / metrics.max_size) * 100
                if metrics.max_size > 0
                else 0
            )

            # Determine status
            status = "healthy"
            if utilization > 90:
                status = "critical"
            elif utilization > 70:
                status = "warning"

            result["postgres"] = ConnectionPoolStatus(
                pool_type="postgres",
                active_connections=metrics.active_connections,
                idle_connections=metrics.idle_connections,
                min_size=metrics.min_size,
                max_size=metrics.max_size,
                utilization_percent=utilization,
                status=status,
                last_updated=metrics.timestamp,
                acquisition_time_ms=metrics.avg_acquisition_time_ms,
                usage_time_ms=metrics.avg_usage_time_ms,
                timeouts=metrics.connection_timeouts,
                errors=metrics.connection_errors,
                script_usage_summary=metrics.script_usage_stats,
            )

        # Process ClickHouse pool
        if "clickhouse" in self._last_metrics:
            metrics = self._last_metrics["clickhouse"]
            total_connections = metrics.active_connections + metrics.idle_connections
            utilization = (
                (total_connections / metrics.max_size) * 100
                if metrics.max_size > 0
                else 0
            )

            # Determine status
            status = "healthy"
            if utilization > 90:
                status = "critical"
            elif utilization > 70:
                status = "warning"

            result["clickhouse"] = ConnectionPoolStatus(
                pool_type="clickhouse",
                active_connections=metrics.active_connections,
                idle_connections=metrics.idle_connections,
                min_size=metrics.min_size,
                max_size=metrics.max_size,
                utilization_percent=utilization,
                status=status,
                last_updated=metrics.timestamp,
                acquisition_time_ms=metrics.avg_acquisition_time_ms,
                usage_time_ms=metrics.avg_usage_time_ms,
                timeouts=metrics.connection_timeouts,
                errors=metrics.connection_errors,
                script_usage_summary=metrics.script_usage_stats,
            )

        # Process ClickHouse cluster pools
        for pool_type, metrics in self._last_metrics.items():
            if pool_type.startswith("clickhouse_") and pool_type != "clickhouse":
                total_connections = (
                    metrics.active_connections + metrics.idle_connections
                )
                utilization = (
                    (total_connections / metrics.max_size) * 100
                    if metrics.max_size > 0
                    else 0
                )

                # Determine status
                status = "healthy"
                if utilization > 90:
                    status = "critical"
                elif utilization > 70:
                    status = "warning"

                result[pool_type] = ConnectionPoolStatus(
                    pool_type=pool_type,
                    active_connections=metrics.active_connections,
                    idle_connections=metrics.idle_connections,
                    min_size=metrics.min_size,
                    max_size=metrics.max_size,
                    utilization_percent=utilization,
                    status=status,
                    last_updated=metrics.timestamp,
                    acquisition_time_ms=metrics.avg_acquisition_time_ms,
                    usage_time_ms=metrics.avg_usage_time_ms,
                    timeouts=metrics.connection_timeouts,
                    errors=metrics.connection_errors,
                    script_usage_summary=metrics.script_usage_stats,
                )

        # Process Logs connection
        if "logs" in self._last_metrics:
            metrics = self._last_metrics["logs"]

            result["logs"] = ConnectionPoolStatus(
                pool_type="logs",
                active_connections=metrics.active_connections,
                idle_connections=metrics.idle_connections,
                min_size=metrics.min_size,
                max_size=metrics.max_size,
                utilization_percent=100 if metrics.active_connections > 0 else 0,
                status="healthy" if metrics.active_connections > 0 else "critical",
                last_updated=metrics.timestamp,
                acquisition_time_ms=metrics.avg_acquisition_time_ms,
                usage_time_ms=metrics.avg_usage_time_ms,
                timeouts=metrics.connection_timeouts,
                errors=metrics.connection_errors,
                script_usage_summary=metrics.script_usage_stats,
            )

        return result

    def _record_script_acquisition_time(
        self, pool_type: str, script_name: str, time_ms: float
    ) -> None:
        """
        Record script-specific connection acquisition time.

        Args:
            pool_type: Type of the pool ('postgres', 'clickhouse', 'logs')
            script_name: Name of the script
            time_ms: Time in milliseconds
        """
        if pool_type not in self._script_acquisition_times:
            self._script_acquisition_times[pool_type] = {}

        if script_name not in self._script_acquisition_times[pool_type]:
            self._script_acquisition_times[pool_type][script_name] = []

        self._script_acquisition_times[pool_type][script_name].append(time_ms)

        # Trim if needed
        if (
            len(self._script_acquisition_times[pool_type][script_name])
            > self._max_script_timing_samples
        ):
            self._script_acquisition_times[pool_type][script_name] = (
                self._script_acquisition_times[pool_type][script_name][
                    -self._max_script_timing_samples :
                ]
            )

        # Update connection count
        if pool_type not in self._script_connection_counts:
            self._script_connection_counts[pool_type] = {}
        if script_name not in self._script_connection_counts[pool_type]:
            self._script_connection_counts[pool_type][script_name] = 0
        self._script_connection_counts[pool_type][script_name] += 1

    def _record_script_usage_time(
        self, pool_type: str, script_name: str, time_ms: float
    ) -> None:
        """
        Record script-specific connection usage time.

        Args:
            pool_type: Type of the pool ('postgres', 'clickhouse', 'logs')
            script_name: Name of the script
            time_ms: Time in milliseconds
        """
        if pool_type not in self._script_usage_times:
            self._script_usage_times[pool_type] = {}

        if script_name not in self._script_usage_times[pool_type]:
            self._script_usage_times[pool_type][script_name] = []

        self._script_usage_times[pool_type][script_name].append(time_ms)

        # Trim if needed
        if (
            len(self._script_usage_times[pool_type][script_name])
            > self._max_script_timing_samples
        ):
            self._script_usage_times[pool_type][script_name] = self._script_usage_times[
                pool_type
            ][script_name][-self._max_script_timing_samples :]

    def get_script_usage_stats(self, pool_type: str) -> Dict[str, Any]:
        """
        Get script usage statistics for a specific pool type.

        Args:
            pool_type: Type of the pool ('postgres', 'clickhouse', 'logs')

        Returns:
            Dictionary containing script usage statistics
        """
        stats = {}

        # Get acquisition time stats
        if pool_type in self._script_acquisition_times:
            for script_name, times in self._script_acquisition_times[pool_type].items():
                if times:
                    stats[script_name] = {
                        "connection_count": self._script_connection_counts.get(
                            pool_type, {}
                        ).get(script_name, 0),
                        "avg_acquisition_time_ms": sum(times) / len(times),
                        "max_acquisition_time_ms": max(times),
                        "min_acquisition_time_ms": min(times),
                    }

        # Add usage time stats
        if pool_type in self._script_usage_times:
            for script_name, times in self._script_usage_times[pool_type].items():
                if times:
                    if script_name not in stats:
                        stats[script_name] = {
                            "connection_count": self._script_connection_counts.get(
                                pool_type, {}
                            ).get(script_name, 0),
                        }
                    stats[script_name].update(
                        {
                            "avg_usage_time_ms": sum(times) / len(times),
                            "max_usage_time_ms": max(times),
                            "min_usage_time_ms": min(times),
                        }
                    )

        return stats


# Create a global instance of the monitoring service
connection_pool_monitoring_service = ConnectionPoolMonitoringService()
