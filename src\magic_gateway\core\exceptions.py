"""Custom exceptions for the MagicGateway application."""

from typing import Any, Dict, List, Optional, Union

from fastapi import HTTPException, status


class MagicGatewayException(Exception):
    """Base exception for all MagicGateway exceptions."""

    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class DatabaseException(MagicGatewayException):
    """Exception raised for database-related errors."""

    pass


class ClickHouseException(DatabaseException):
    """Exception raised for ClickHouse-related errors."""

    pass


class PostgresException(DatabaseException):
    """Exception raised for PostgreSQL-related errors."""

    pass


class AuthenticationException(MagicGatewayException):
    """Exception raised for authentication-related errors."""

    pass


class LDAPException(AuthenticationException):
    """Exception raised for LDAP-related errors."""

    pass


class JWTException(AuthenticationException):
    """Exception raised for JWT-related errors."""

    pass


class ScriptExecutionException(MagicGatewayException):
    """Exception raised for script execution-related errors."""

    pass


class ConnectionPoolException(DatabaseException):
    """Exception raised for connection pool-related errors."""

    pass


class ConnectionPoolExhaustedException(ConnectionPoolException):
    """Exception raised when connection pool is exhausted."""

    pass


class ConnectionTimeoutException(ConnectionPoolException):
    """Exception raised when connection acquisition times out."""

    pass


class TransientConnectionException(ConnectionPoolException):
    """Exception raised for transient connection failures that may be retried."""

    pass


class QueryTrackingException(MagicGatewayException):
    """Exception raised for query tracking-related errors."""

    pass


class RequestTrackingException(MagicGatewayException):
    """Exception raised for request tracking-related errors."""

    pass


# HTTP Exceptions
class NotAuthenticatedException(HTTPException):
    """Exception raised when a user is not authenticated."""

    def __init__(
        self,
        detail: str = "Not authenticated",
    ):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class ForbiddenException(HTTPException):
    """Exception raised when a user is not authorized to access a resource."""

    def __init__(
        self,
        detail: str = "Not enough permissions",
    ):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


class NotFoundException(HTTPException):
    """Exception raised when a resource is not found."""

    def __init__(
        self,
        detail: str = "Resource not found",
    ):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
        )


class BadRequestException(HTTPException):
    """Exception raised when a request is invalid."""

    def __init__(
        self,
        detail: str = "Bad request",
    ):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
        )


class ConflictException(HTTPException):
    """Exception raised when a resource already exists."""

    def __init__(
        self,
        detail: str = "Resource already exists",
    ):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
        )


class InternalServerErrorException(HTTPException):
    """Exception raised when an internal server error occurs."""

    def __init__(
        self,
        detail: str = "Internal server error",
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
        )
