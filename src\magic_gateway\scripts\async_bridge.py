"""
Async/sync bridge utilities for script execution.

This module provides helper functions to execute async connection operations
in synchronous script contexts, ensuring proper error propagation and
resource management.
"""

import asyncio
import functools
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Awaitable, Callable, Optional, TypeVar

from clickhouse_driver.errors import Error as ClickHouseDriverError
from clickhouse_driver.errors import NetworkError as ClickHouseNetworkError
from clickhouse_driver.errors import ServerException as ClickHouseServerException
from psycopg.errors import OperationalError as PsycopgOperationalError
from psycopg_pool import PoolTimeout

from magic_gateway.core.exceptions import (
    ScriptExecutionException,
    ClickHouseException,
    PostgresException,
    ConnectionPoolException,
    ConnectionPoolExhaustedException,
    ConnectionTimeoutException,
    TransientConnectionException,
)
from magic_gateway.core.logging_config import log
from magic_gateway.monitoring.script_context import get_current_script_name

T = TypeVar("T")


def is_transient_error(error: Exception) -> bool:
    """
    Determine if an error is transient and should be retried.

    Args:
        error: The exception to check

    Returns:
        True if the error is transient and can be retried
    """
    # ClickHouse transient errors
    if isinstance(error, ClickHouseNetworkError):
        return True
    if isinstance(error, ClickHouseDriverError):
        error_msg = str(error).lower()
        # Check for non-transient patterns first
        non_transient_patterns = [
            "too many connections",
            "connection limit",
            "authentication failed",
            "syntax error",
            "permission denied",
        ]
        if any(pattern in error_msg for pattern in non_transient_patterns):
            return False

        # Common transient error patterns
        transient_patterns = [
            "connection refused",
            "connection reset",
            "timeout",
            "temporary failure",
            "network is unreachable",
            "connection timed out",
        ]
        return any(pattern in error_msg for pattern in transient_patterns)

    # PostgreSQL transient errors
    if isinstance(error, PsycopgOperationalError):
        error_msg = str(error).lower()
        transient_patterns = [
            "connection refused",
            "connection reset",
            "timeout",
            "temporary failure",
            "network is unreachable",
            "connection timed out",
            "too many connections",
            "server closed the connection",
        ]
        return any(pattern in error_msg for pattern in transient_patterns)

    # Pool timeout is always considered transient
    if isinstance(error, PoolTimeout):
        return True

    return False


def retry_on_transient_error(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 30.0,
    exponential_base: float = 2.0,
):
    """
    Decorator to retry operations on transient errors with exponential backoff.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds between retries
        max_delay: Maximum delay in seconds between retries
        exponential_base: Base for exponential backoff calculation
    """

    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            last_error = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_error = e

                    # Don't retry on the last attempt
                    if attempt == max_retries:
                        break

                    # Only retry transient errors
                    if not is_transient_error(e):
                        log.debug(
                            f"Non-transient error, not retrying: {type(e).__name__}: {e}"
                        )
                        break

                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (exponential_base**attempt), max_delay)

                    log.warning(
                        f"Transient error on attempt {attempt + 1}/{max_retries + 1}: "
                        f"{type(e).__name__}: {e}. Retrying in {delay:.2f}s..."
                    )

                    await asyncio.sleep(delay)

            # All retries exhausted, raise the last error with context
            if is_transient_error(last_error):
                raise TransientConnectionException(
                    f"Operation failed after {max_retries + 1} attempts due to transient errors. "
                    f"Last error: {type(last_error).__name__}: {last_error}"
                ) from last_error
            else:
                # Re-raise non-transient errors as-is
                raise last_error

        return wrapper

    return decorator


def run_async_in_sync(coro: Awaitable[T]) -> T:
    """
    Execute an async coroutine in a synchronous context.

    This function handles different scenarios:
    1. If there's no event loop, creates a new one
    2. If there's a running event loop, uses a thread pool to avoid blocking
    3. Ensures proper error propagation from async to sync context

    Args:
        coro: The coroutine to execute

    Returns:
        The result of the coroutine execution

    Raises:
        ScriptExecutionException: If the async operation fails
    """
    try:
        # Try to get the current event loop
        try:
            loop = asyncio.get_running_loop()
            # We're in an async context, need to use thread pool
            log.debug("Running async operation in thread pool (async context detected)")
            with ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        except RuntimeError:
            # No running event loop, we can use asyncio.run directly
            log.debug("Running async operation with asyncio.run (no running loop)")
            return asyncio.run(coro)
    except Exception as e:
        # Preserve specific connection pool exceptions
        if isinstance(
            e,
            (
                ConnectionPoolException,
                ConnectionPoolExhaustedException,
                ConnectionTimeoutException,
                TransientConnectionException,
                ClickHouseException,
                PostgresException,
            ),
        ):
            # Re-raise connection-specific exceptions without wrapping
            raise e
        else:
            log.error(f"Error executing async operation in sync context: {e}")
            raise ScriptExecutionException(f"Async operation failed: {e}") from e


def sync_wrapper(async_func: Callable[..., Awaitable[T]]) -> Callable[..., T]:
    """
    Decorator to wrap an async function for synchronous use.

    Args:
        async_func: The async function to wrap

    Returns:
        A synchronous wrapper function
    """

    @functools.wraps(async_func)
    def wrapper(*args, **kwargs) -> T:
        coro = async_func(*args, **kwargs)
        return run_async_in_sync(coro)

    return wrapper


@retry_on_transient_error(max_retries=3, base_delay=1.0, max_delay=10.0)
async def execute_clickhouse_command_async(
    clickhouse_manager, command: str, parameters: Optional[dict] = None
) -> Any:
    """
    Execute a ClickHouse command using the connection manager asynchronously.

    Args:
        clickhouse_manager: The ClickHouse connection manager
        command: The SQL command to execute
        parameters: Optional parameters for the command

    Returns:
        The result of the command execution

    Raises:
        ScriptExecutionException: If the command execution fails
        ConnectionPoolExhaustedException: If connection pool is exhausted
        ConnectionTimeoutException: If connection acquisition times out
        TransientConnectionException: If transient errors persist after retries
    """
    if clickhouse_manager is None:
        raise ScriptExecutionException("ClickHouse connection manager is required")

    connection_start_time = time.time()

    try:
        async with clickhouse_manager.connection() as client:
            connection_time = (time.time() - connection_start_time) * 1000
            # Only log during normal operation, not startup
            if hasattr(clickhouse_manager, '_startup_complete') and clickhouse_manager._startup_complete:
                log.debug(f"Acquired ClickHouse connection in {connection_time:.2f}ms")

            # Execute in thread pool since ClickHouse client is synchronous
            loop = asyncio.get_event_loop()

            log.debug(f"Executing ClickHouse command: {command[:100]}...")
            execution_start_time = time.time()

            if parameters:
                result = await loop.run_in_executor(
                    None, client.command, command, parameters
                )
            else:
                result = await loop.run_in_executor(None, client.command, command)

            execution_time = (time.time() - execution_start_time) * 1000
            log.debug(
                f"ClickHouse command executed successfully in {execution_time:.2f}ms"
            )
            return result

    except PoolTimeout as e:
        connection_time = (time.time() - connection_start_time) * 1000
        log.error(
            f"ClickHouse connection pool timeout after {connection_time:.2f}ms. "
            f"Pool may be exhausted or connections are taking too long to become available."
        )
        raise ConnectionTimeoutException(
            f"Timeout acquiring ClickHouse connection from pool after {connection_time:.2f}ms"
        ) from e

    except ClickHouseServerException as e:
        # Server-side errors (syntax, permissions, etc.) - not transient
        log.error(f"ClickHouse server error: {e}")
        raise ClickHouseException(f"ClickHouse server error: {e}") from e

    except ClickHouseNetworkError as e:
        # Network errors are typically transient
        log.error(f"ClickHouse network error: {e}")
        raise TransientConnectionException(f"ClickHouse network error: {e}") from e

    except ClickHouseDriverError as e:
        # Check if it's a connection pool exhaustion error
        error_msg = str(e).lower()
        if "too many connections" in error_msg or "connection limit" in error_msg:
            log.error(f"ClickHouse connection pool exhausted: {e}")
            raise ConnectionPoolExhaustedException(
                f"ClickHouse connection pool exhausted: {e}"
            ) from e

        # Other driver errors
        log.error(f"ClickHouse driver error: {e}")
        if is_transient_error(e):
            raise TransientConnectionException(f"ClickHouse driver error: {e}") from e
        else:
            raise ClickHouseException(f"ClickHouse driver error: {e}") from e

    except Exception as e:
        log.error(f"Unexpected error during ClickHouse command execution: {e}")
        raise ScriptExecutionException(f"ClickHouse command failed: {e}") from e


@retry_on_transient_error(max_retries=3, base_delay=1.0, max_delay=10.0)
async def execute_clickhouse_query_async(
    clickhouse_manager, query: str, parameters: Optional[dict] = None
) -> Any:
    """
    Execute a ClickHouse query using the connection manager asynchronously.

    Args:
        clickhouse_manager: The ClickHouse connection manager
        query: The SQL query to execute
        parameters: Optional parameters for the query

    Returns:
        The result of the query execution (typically a DataFrame)

    Raises:
        ScriptExecutionException: If the query execution fails
        ConnectionPoolExhaustedException: If connection pool is exhausted
        ConnectionTimeoutException: If connection acquisition times out
        TransientConnectionException: If transient errors persist after retries
    """
    if clickhouse_manager is None:
        raise ScriptExecutionException("ClickHouse connection manager is required")

    connection_start_time = time.time()

    try:
        async with clickhouse_manager.connection() as client:
            connection_time = (time.time() - connection_start_time) * 1000
            # Only log during normal operation, not startup
            if hasattr(clickhouse_manager, '_startup_complete') and clickhouse_manager._startup_complete:
                log.debug(f"Acquired ClickHouse connection in {connection_time:.2f}ms")

            # Execute in thread pool since ClickHouse client is synchronous
            loop = asyncio.get_event_loop()

            log.debug(f"Executing ClickHouse query: {query[:100]}...")
            execution_start_time = time.time()

            if parameters:
                result = await loop.run_in_executor(
                    None, client.query_df, query, parameters
                )
            else:
                result = await loop.run_in_executor(None, client.query_df, query)

            execution_time = (time.time() - execution_start_time) * 1000
            log.debug(
                f"ClickHouse query executed successfully in {execution_time:.2f}ms"
            )
            return result

    except PoolTimeout as e:
        connection_time = (time.time() - connection_start_time) * 1000
        log.error(
            f"ClickHouse connection pool timeout after {connection_time:.2f}ms. "
            f"Pool may be exhausted or connections are taking too long to become available."
        )
        raise ConnectionTimeoutException(
            f"Timeout acquiring ClickHouse connection from pool after {connection_time:.2f}ms"
        ) from e

    except ClickHouseServerException as e:
        # Server-side errors (syntax, permissions, etc.) - not transient
        log.error(f"ClickHouse server error: {e}")
        raise ClickHouseException(f"ClickHouse server error: {e}") from e

    except ClickHouseNetworkError as e:
        # Network errors are typically transient
        log.error(f"ClickHouse network error: {e}")
        raise TransientConnectionException(f"ClickHouse network error: {e}") from e

    except ClickHouseDriverError as e:
        # Check if it's a connection pool exhaustion error
        error_msg = str(e).lower()
        if "too many connections" in error_msg or "connection limit" in error_msg:
            log.error(f"ClickHouse connection pool exhausted: {e}")
            raise ConnectionPoolExhaustedException(
                f"ClickHouse connection pool exhausted: {e}"
            ) from e

        # Other driver errors
        log.error(f"ClickHouse driver error: {e}")
        if is_transient_error(e):
            raise TransientConnectionException(f"ClickHouse driver error: {e}") from e
        else:
            raise ClickHouseException(f"ClickHouse driver error: {e}") from e

    except Exception as e:
        log.error(f"Unexpected error during ClickHouse query execution: {e}")
        raise ScriptExecutionException(f"ClickHouse query failed: {e}") from e


def execute_clickhouse_command(
    clickhouse_manager, command: str, parameters: Optional[dict] = None
) -> Any:
    """
    Execute a ClickHouse command using the connection pool synchronously.

    This is a synchronous wrapper around the async command execution.

    Args:
        clickhouse_manager: The ClickHouse connection manager
        command: The SQL command to execute
        parameters: Optional parameters for the command

    Returns:
        The result of the command execution

    Raises:
        ScriptExecutionException: If the command execution fails
    """
    return run_async_in_sync(
        execute_clickhouse_command_async(clickhouse_manager, command, parameters)
    )


def execute_clickhouse_query(
    clickhouse_manager, query: str, parameters: Optional[dict] = None
) -> Any:
    """
    Execute a ClickHouse query using the connection pool synchronously.

    This is a synchronous wrapper around the async query execution.

    Args:
        clickhouse_manager: The ClickHouse connection manager
        query: The SQL query to execute
        parameters: Optional parameters for the query

    Returns:
        The result of the query execution (typically a DataFrame)

    Raises:
        ScriptExecutionException: If the query execution fails
    """
    return run_async_in_sync(
        execute_clickhouse_query_async(clickhouse_manager, query, parameters)
    )


@retry_on_transient_error(max_retries=3, base_delay=1.0, max_delay=10.0)
async def execute_postgres_query_async(
    postgres_manager, query: str, parameters: Optional[dict] = None
) -> Any:
    """
    Execute a PostgreSQL query using the connection manager asynchronously.

    Args:
        postgres_manager: The PostgreSQL connection manager
        query: The SQL query to execute
        parameters: Optional parameters for the query

    Returns:
        The result of the query execution

    Raises:
        ScriptExecutionException: If the query execution fails
        ConnectionPoolExhaustedException: If connection pool is exhausted
        ConnectionTimeoutException: If connection acquisition times out
        TransientConnectionException: If transient errors persist after retries
    """
    if postgres_manager is None:
        raise ScriptExecutionException("PostgreSQL connection manager is required")

    connection_start_time = time.time()

    try:
        async with postgres_manager.connection() as conn:
            connection_time = (time.time() - connection_start_time) * 1000
            # Only log during normal operation, not startup
            if hasattr(postgres_manager, '_startup_complete') and postgres_manager._startup_complete:
                log.debug(f"Acquired PostgreSQL connection in {connection_time:.2f}ms")

            log.debug(f"Executing PostgreSQL query: {query[:100]}...")
            execution_start_time = time.time()

            if parameters:
                cursor = await conn.execute(query, parameters)
            else:
                cursor = await conn.execute(query)

            # Fetch results if it's a SELECT query
            if query.strip().upper().startswith("SELECT"):
                result = await cursor.fetchall()
            else:
                result = cursor.rowcount

            execution_time = (time.time() - execution_start_time) * 1000
            log.debug(
                f"PostgreSQL query executed successfully in {execution_time:.2f}ms"
            )
            return result

    except PoolTimeout as e:
        connection_time = (time.time() - connection_start_time) * 1000
        log.error(
            f"PostgreSQL connection pool timeout after {connection_time:.2f}ms. "
            f"Pool may be exhausted or connections are taking too long to become available."
        )
        raise ConnectionTimeoutException(
            f"Timeout acquiring PostgreSQL connection from pool after {connection_time:.2f}ms"
        ) from e

    except PsycopgOperationalError as e:
        # Check if it's a connection pool exhaustion error
        error_msg = str(e).lower()
        if "too many connections" in error_msg or "connection limit" in error_msg:
            log.error(f"PostgreSQL connection pool exhausted: {e}")
            raise ConnectionPoolExhaustedException(
                f"PostgreSQL connection pool exhausted: {e}"
            ) from e

        # Other operational errors
        log.error(f"PostgreSQL operational error: {e}")
        if is_transient_error(e):
            raise TransientConnectionException(
                f"PostgreSQL operational error: {e}"
            ) from e
        else:
            raise PostgresException(f"PostgreSQL operational error: {e}") from e

    except Exception as e:
        log.error(f"Unexpected error during PostgreSQL query execution: {e}")
        raise ScriptExecutionException(f"PostgreSQL query failed: {e}") from e


def execute_postgres_query(
    postgres_manager, query: str, parameters: Optional[dict] = None
) -> Any:
    """
    Execute a PostgreSQL query using the connection pool synchronously.

    This is a synchronous wrapper around the async query execution.

    Args:
        postgres_manager: The PostgreSQL connection manager
        query: The SQL query to execute
        parameters: Optional parameters for the query

    Returns:
        The result of the query execution

    Raises:
        ScriptExecutionException: If the query execution fails
    """
    return run_async_in_sync(
        execute_postgres_query_async(postgres_manager, query, parameters)
    )


class AsyncBridge:
    """
    Context manager for handling async/sync bridge operations.

    This class provides a convenient way to manage async operations
    within synchronous script contexts, with proper error handling
    and resource cleanup.
    """

    def __init__(self, connection_managers: Optional[dict] = None):
        """
        Initialize the async bridge.

        Args:
            connection_managers: Dictionary of connection managers
        """
        self.connection_managers = connection_managers or {}
        self._executor = None

    def __enter__(self):
        """Enter the context manager."""
        self._executor = ThreadPoolExecutor(max_workers=1)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        if self._executor:
            self._executor.shutdown(wait=True)

    def run_async(self, coro: Awaitable[T]) -> T:
        """
        Run an async coroutine within the bridge context.

        Args:
            coro: The coroutine to execute

        Returns:
            The result of the coroutine execution
        """
        return run_async_in_sync(coro)

    def clickhouse_command(
        self,
        command: str,
        parameters: Optional[dict] = None,
        manager_key: str = "clickhouse_primary",
    ) -> Any:
        """
        Execute a ClickHouse command using the specified manager.

        Args:
            command: The SQL command to execute
            parameters: Optional parameters for the command
            manager_key: Key to identify the connection manager

        Returns:
            The result of the command execution

        Raises:
            ScriptExecutionException: If connection manager is not available or command fails
            ConnectionPoolExhaustedException: If connection pool is exhausted
            ConnectionTimeoutException: If connection acquisition times out
            TransientConnectionException: If transient errors persist after retries
        """
        manager = self.connection_managers.get(manager_key)
        if manager is None:
            available_managers = list(self.connection_managers.keys())
            raise ScriptExecutionException(
                f"Connection manager '{manager_key}' not available. "
                f"Available managers: {available_managers}"
            )
        return execute_clickhouse_command(manager, command, parameters)

    def clickhouse_query(
        self,
        query: str,
        parameters: Optional[dict] = None,
        manager_key: str = "clickhouse_primary",
    ) -> Any:
        """
        Execute a ClickHouse query using the specified manager.

        Args:
            query: The SQL query to execute
            parameters: Optional parameters for the query
            manager_key: Key to identify the connection manager

        Returns:
            The result of the query execution

        Raises:
            ScriptExecutionException: If connection manager is not available or query fails
            ConnectionPoolExhaustedException: If connection pool is exhausted
            ConnectionTimeoutException: If connection acquisition times out
            TransientConnectionException: If transient errors persist after retries
        """
        manager = self.connection_managers.get(manager_key)
        if manager is None:
            available_managers = list(self.connection_managers.keys())
            raise ScriptExecutionException(
                f"Connection manager '{manager_key}' not available. "
                f"Available managers: {available_managers}"
            )
        return execute_clickhouse_query(manager, query, parameters)

    def postgres_query(
        self,
        query: str,
        parameters: Optional[dict] = None,
        manager_key: str = "postgres",
    ) -> Any:
        """
        Execute a PostgreSQL query using the specified manager.

        Args:
            query: The SQL query to execute
            parameters: Optional parameters for the query
            manager_key: Key to identify the connection manager

        Returns:
            The result of the query execution

        Raises:
            ScriptExecutionException: If connection manager is not available or query fails
            ConnectionPoolExhaustedException: If connection pool is exhausted
            ConnectionTimeoutException: If connection acquisition times out
            TransientConnectionException: If transient errors persist after retries
        """
        manager = self.connection_managers.get(manager_key)
        if manager is None:
            available_managers = list(self.connection_managers.keys())
            raise ScriptExecutionException(
                f"Connection manager '{manager_key}' not available. "
                f"Available managers: {available_managers}"
            )
        return execute_postgres_query(manager, query, parameters)
