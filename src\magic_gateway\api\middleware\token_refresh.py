"""Token refresh middleware for automatic token renewal."""

from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from magic_gateway.core.logging_config import log


class TokenRefreshMiddleware(BaseHTTPMiddleware):
    """Middleware to handle automatic token refresh responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and add token refresh headers if tokens were refreshed.
        
        Args:
            request: The incoming request
            call_next: The next middleware or endpoint
            
        Returns:
            Response with token refresh headers if applicable
        """
        # Process the request
        response = await call_next(request)
        
        # Check if new tokens were set during authentication
        if hasattr(request.state, 'new_access_token') and hasattr(request.state, 'new_refresh_token'):
            # Add the new tokens to response headers
            response.headers["X-New-Access-Token"] = request.state.new_access_token
            response.headers["X-New-Refresh-Token"] = request.state.new_refresh_token
            
            log.info("Added refreshed tokens to response headers")
            
            # If this is a JSON response, also add token info to the response body
            if isinstance(response, JSONResponse) and hasattr(response, 'body'):
                try:
                    import json
                    body = json.loads(response.body.decode())
                    if isinstance(body, dict):
                        body["_token_refreshed"] = True
                        body["_new_access_token"] = request.state.new_access_token
                        body["_new_refresh_token"] = request.state.new_refresh_token
                        response.body = json.dumps(body).encode()
                        response.headers["content-length"] = str(len(response.body))
                except Exception as e:
                    log.warning(f"Failed to add token info to response body: {e}")
        
        # Check if token is expiring soon (for web interface warnings)
        if hasattr(request.state, 'token_expiring_soon') and request.state.token_expiring_soon:
            response.headers["X-Token-Expiring-Soon"] = "true"
            
            # Add warning to JSON response body for web interface
            if isinstance(response, JSONResponse) and hasattr(response, 'body'):
                try:
                    import json
                    body = json.loads(response.body.decode())
                    if isinstance(body, dict):
                        body["_token_expiring_soon"] = True
                        body["_warning"] = "Your session will expire soon. Please save your work."
                        response.body = json.dumps(body).encode()
                        response.headers["content-length"] = str(len(response.body))
                except Exception as e:
                    log.warning(f"Failed to add token expiring warning to response body: {e}")
        
        return response