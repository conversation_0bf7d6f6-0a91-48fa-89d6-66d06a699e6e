"""
MagicGateway API Client

This module provides a base client class for interacting with the MagicGateway API.
It handles authentication, token refresh, and provides common functionality used by
all example scripts.
"""

import os
import json
import logging
import requests
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger("magic_gateway_client")

# API Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")


class MagicGatewayClient:
    """Base client for interacting with the MagicGateway API."""

    def __init__(self, base_url: str = API_BASE_URL):
        """Initialize the client with the API base URL."""
        self.base_url = base_url
        self.access_token = None
        self.refresh_token = None
        self.token_type = "bearer"
        self.token_expires_at = None
        self.session = requests.Session()
        self.username = None
        self.is_admin = False

    def authenticate(self, username: str, password: str) -> bool:
        """
        Authenticate with the API using LDAP credentials.

        Args:
            username: LDAP username
            password: LDAP password

        Returns:
            Boolean indicating if authentication was successful
        """
        login_url = f"{self.base_url}/api/v1/auth/login"
        logger.info(f"Authenticating user: {username}")

        # Use form data for OAuth2 authentication
        form_data = {"username": username, "password": password}

        try:
            response = self.session.post(login_url, data=form_data)
            response.raise_for_status()
            
            # Parse the response
            auth_data = response.json()
            self.access_token = auth_data.get("access_token")
            self.refresh_token = auth_data.get("refresh_token")
            self.token_type = auth_data.get("token_type", "bearer")
            
            # Calculate token expiration
            expires_in = auth_data.get("expires_in", 1800)  # Default to 30 minutes
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            # Store username for later use
            self.username = username
            
            # Get user info to check if admin
            self._update_user_info()
            
            logger.info(f"Authentication successful for user: {username}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Authentication failed: {e}")
            return False

    def refresh_auth_token(self) -> bool:
        """
        Refresh the authentication token.

        Returns:
            Boolean indicating if token refresh was successful
        """
        if not self.refresh_token:
            logger.error("No refresh token available")
            return False

        refresh_url = f"{self.base_url}/api/v1/auth/refresh"
        logger.info("Refreshing authentication token")

        try:
            # Send refresh token in the request body
            response = self.session.post(
                refresh_url, 
                json={"refresh_token": self.refresh_token}
            )
            response.raise_for_status()
            
            # Parse the response
            auth_data = response.json()
            self.access_token = auth_data.get("access_token")
            self.refresh_token = auth_data.get("refresh_token")
            self.token_type = auth_data.get("token_type", "bearer")
            
            # Calculate token expiration
            expires_in = auth_data.get("expires_in", 1800)  # Default to 30 minutes
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            logger.info("Token refreshed successfully")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Token refresh failed: {e}")
            return False

    def _update_user_info(self) -> None:
        """Update user information from the API."""
        user_info = self.get_user_info()
        if user_info:
            self.is_admin = user_info.get("is_admin", False)

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the authenticated user.

        Returns:
            Dictionary with user information or None if request failed
        """
        me_url = f"{self.base_url}/api/v1/auth/me"
        logger.info("Getting user information")

        try:
            response = self._make_authenticated_request("GET", me_url)
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get user information: {e}")
            return None

    def _make_authenticated_request(
        self, 
        method: str, 
        url: str, 
        **kwargs
    ) -> requests.Response:
        """
        Make an authenticated request to the API.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: URL to request
            **kwargs: Additional arguments to pass to requests

        Returns:
            Response object

        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        # Check if token is about to expire and refresh if needed
        if self.token_expires_at and datetime.now() > (self.token_expires_at - timedelta(minutes=1)):
            logger.info("Token is about to expire, refreshing")
            if not self.refresh_auth_token():
                raise requests.exceptions.RequestException("Failed to refresh token")

        # Add authorization header
        headers = kwargs.get("headers", {})
        headers["Authorization"] = f"{self.token_type} {self.access_token}"
        kwargs["headers"] = headers

        # Make the request
        response = self.session.request(method, url, **kwargs)
        response.raise_for_status()
        return response

    def close(self) -> None:
        """Close the session."""
        self.session.close()
        logger.info("Session closed")


# Helper function to print JSON responses nicely
def print_json(data: Any) -> None:
    """Print data as formatted JSON."""
    print(json.dumps(data, indent=2, default=str))
