"""
Example demonstrating the async/sync bridge for script execution.

This example shows how to use the async bridge utilities to execute
async connection operations in synchronous script contexts.
"""

from magic_gateway.scripts.async_bridge import (
    execute_clickhouse_command,
    execute_clickhouse_query,
    execute_postgres_query,
    AsyncBridge,
    run_async_in_sync,
)


def example_clickhouse_operations(clickhouse_manager):
    """
    Example of using ClickHouse operations with the async bridge.

    Args:
        clickhouse_manager: The ClickHouse connection manager
    """
    print("=== ClickHouse Operations Example ===")

    try:
        # Create a temporary table
        print("Creating temporary table...")
        execute_clickhouse_command(
            clickhouse_manager,
            "CREATE TEMPORARY TABLE example_table (id Int32, name String) ENGINE = Memory",
        )
        print("✓ Table created successfully")

        # Insert some data
        print("Inserting data...")
        execute_clickhouse_command(
            clickhouse_manager,
            "INSERT INTO example_table VALUES (1, 'Alice'), (2, '<PERSON>'), (3, '<PERSON>')",
        )
        print("✓ Data inserted successfully")

        # Query the data
        print("Querying data...")
        result = execute_clickhouse_query(
            clickhouse_manager, "SELECT * FROM example_table ORDER BY id"
        )
        print(f"✓ Query result: {len(result)} rows")
        print(result)

        # Clean up
        print("Dropping table...")
        execute_clickhouse_command(clickhouse_manager, "DROP TABLE example_table")
        print("✓ Table dropped successfully")

    except Exception as e:
        print(f"✗ Error: {e}")


def example_postgres_operations(postgres_manager):
    """
    Example of using PostgreSQL operations with the async bridge.

    Args:
        postgres_manager: The PostgreSQL connection manager
    """
    print("\n=== PostgreSQL Operations Example ===")

    try:
        # Simple query
        print("Executing simple query...")
        result = execute_postgres_query(
            postgres_manager,
            "SELECT 'Hello from PostgreSQL' as message, NOW() as timestamp",
        )
        print(f"✓ Query result: {result}")

        # Query with parameters (if supported)
        print("Executing parameterized query...")
        result = execute_postgres_query(
            postgres_manager, "SELECT $1 as input_value", {"$1": "test_parameter"}
        )
        print(f"✓ Parameterized query result: {result}")

    except Exception as e:
        print(f"✗ Error: {e}")


def example_async_bridge_context(connection_managers):
    """
    Example of using the AsyncBridge context manager.

    Args:
        connection_managers: Dictionary of connection managers
    """
    print("\n=== AsyncBridge Context Manager Example ===")

    try:
        with AsyncBridge(connection_managers) as bridge:
            print("AsyncBridge context established")

            # ClickHouse operations through bridge
            if "clickhouse_primary" in connection_managers:
                print("Executing ClickHouse command through bridge...")
                bridge.clickhouse_command(
                    "CREATE TEMPORARY TABLE bridge_test (id Int32) ENGINE = Memory"
                )
                print("✓ ClickHouse command executed")

                result = bridge.clickhouse_query("SELECT 1 as test_value")
                print(f"✓ ClickHouse query result: {result}")

                bridge.clickhouse_command("DROP TABLE bridge_test")
                print("✓ Cleanup completed")

            # PostgreSQL operations through bridge
            if "postgres" in connection_managers:
                print("Executing PostgreSQL query through bridge...")
                result = bridge.postgres_query("SELECT 'Bridge test' as message")
                print(f"✓ PostgreSQL query result: {result}")

        print("✓ AsyncBridge context closed successfully")

    except Exception as e:
        print(f"✗ Error: {e}")


async def example_async_operation():
    """Example async operation to demonstrate run_async_in_sync."""
    import asyncio

    await asyncio.sleep(0.1)  # Simulate async work
    return "Async operation completed"


def example_run_async_in_sync():
    """
    Example of running async operations in sync context.
    """
    print("\n=== Run Async in Sync Example ===")

    try:
        print("Running async operation in sync context...")
        result = run_async_in_sync(example_async_operation())
        print(f"✓ Result: {result}")

    except Exception as e:
        print(f"✗ Error: {e}")


def main():
    """
    Main example function.

    Note: This example uses mock connection managers for demonstration.
    In a real script, you would receive actual connection managers from
    the script runner.
    """
    print("Async/Sync Bridge Examples")
    print("=" * 50)

    # Mock connection managers for demonstration
    # In a real script, these would be provided by the script runner
    from unittest.mock import MagicMock

    mock_clickhouse_manager = MagicMock()
    mock_postgres_manager = MagicMock()

    # Configure mock responses
    mock_clickhouse_manager.connection.return_value.__aenter__.return_value.command.return_value = None
    mock_clickhouse_manager.connection.return_value.__aenter__.return_value.query_df.return_value = [
        {"id": 1, "name": "Alice"},
        {"id": 2, "name": "Bob"},
        {"id": 3, "name": "Charlie"},
    ]

    connection_managers = {
        "clickhouse_primary": mock_clickhouse_manager,
        "postgres": mock_postgres_manager,
    }

    # Run examples
    print("Note: Using mock connection managers for demonstration")
    print(
        "In a real script, connection managers would be provided by the script runner\n"
    )

    # Example 1: Direct ClickHouse operations
    example_clickhouse_operations(mock_clickhouse_manager)

    # Example 2: Direct PostgreSQL operations
    example_postgres_operations(mock_postgres_manager)

    # Example 3: AsyncBridge context manager
    example_async_bridge_context(connection_managers)

    # Example 4: Running async operations in sync context
    example_run_async_in_sync()

    print("\n" + "=" * 50)
    print("Examples completed!")


if __name__ == "__main__":
    main()
