# magic_gateway/tracking/service.py

import uuid
import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, Optional, List

from magic_gateway.core.logging_config import log
from magic_gateway.db.logs_handler import LogsHandler
from magic_gateway.core.exceptions import RequestTrackingException
from magic_gateway.tracking.models import (
    ApiRequestLog,
    RequestStatus,
    RequestStatusResponse,
)


class RequestTrackingService:
    """Service for managing API request tracking logs."""

    def __init__(self):
        """Initialize the service."""
        # No fallback flag needed - always use logs database

    async def start_request(
        self,
        request_id: uuid.UUID,
        endpoint_path: str,
        http_method: str,
        client_ip: Optional[str] = None,
    ) -> ApiRequestLog:
        """Log the start of an API request."""
        log_entry = ApiRequestLog(
            request_id=request_id,
            endpoint_path=endpoint_path,
            http_method=http_method,
            client_ip=client_ip,
            status=RequestStatus.RECEIVED,
            start_time=datetime.now(timezone.utc),  # Ensure start time is set here
        )
        try:
            # Use background task for DB insert to avoid blocking request path?
            # For now, await directly for simplicity, but consider background task
            # for high-throughput scenarios.
            await LogsHandler.insert_request_log(log_entry)
            return log_entry
        except Exception as e:
            # Log error but don't fail the request itself due to tracking issues
            log.error(
                f"Tracking Service: Failed to log request start {request_id}: {e}"
            )
            # Return the log entry anyway, even if DB failed, for middleware use
            return log_entry

    async def update_request_user(self, request_id: uuid.UUID, username: str) -> None:
        """Update the username associated with a request log."""
        try:
            await LogsHandler.update_request_log_partial(
                request_id, {"username": username}
            )
        except Exception as e:
            log.error(
                f"Tracking Service: Failed to update user for request {request_id}: {e}"
            )

    async def add_task_details(
        self, request_id: uuid.UUID, details: Dict[str, Any]
    ) -> None:
        """Add or update task-specific details (e.g., db_query_id)."""
        try:
            # Fetch existing details to merge, or just replace? Replacing is simpler.
            await LogsHandler.update_request_log_partial(
                request_id, {"task_details": details}
            )
        except Exception as e:
            log.error(
                f"Tracking Service: Failed to add task details for request {request_id}: {e}"
            )

    async def complete_request(
        self,
        request_id: uuid.UUID,
        status_code: int,
        start_time: datetime,  # Pass start_time from middleware context
        username: Optional[str] = None,  # Pass username if available
        task_details: Optional[Dict[str, Any]] = None,  # Pass details if available
        status: RequestStatus = RequestStatus.COMPLETED,  # Allow override
    ) -> None:
        """Log the successful completion of an API request."""
        end_time = datetime.now(timezone.utc)
        duration_ms = int((end_time - start_time).total_seconds() * 1000)

        log_entry_update = ApiRequestLog(
            request_id=request_id,
            endpoint_path="",  # Not needed for update
            http_method="",  # Not needed for update
            status=status,
            start_time=start_time,  # Needed for Pydantic model, but not updated
            end_time=end_time,
            duration_ms=duration_ms,
            status_code=status_code,
            username=username,  # Update username if passed
            task_details=task_details,  # Update details if passed
            # error_message remains None
        )
        try:
            await LogsHandler.update_request_log(log_entry_update)
        except Exception as e:
            log.error(
                f"Tracking Service: Failed to log request completion {request_id}: {e}"
            )

    async def fail_request(
        self,
        request_id: uuid.UUID,
        status_code: int,
        start_time: datetime,
        error: Exception,
        username: Optional[str] = None,
        task_details: Optional[Dict[str, Any]] = None,
        status: RequestStatus = RequestStatus.FAILED,
    ) -> None:
        """Log the failure of an API request."""
        end_time = datetime.now(timezone.utc)
        duration_ms = int((end_time - start_time).total_seconds() * 1000)
        error_message = (
            f"{type(error).__name__}: {str(error)[:500]}"  # Truncate long errors
        )

        log_entry_update = ApiRequestLog(
            request_id=request_id,
            endpoint_path="",
            http_method="",
            status=status,
            start_time=start_time,
            end_time=end_time,
            duration_ms=duration_ms,
            status_code=status_code,
            username=username,
            error_message=error_message,
            task_details=task_details,
        )
        try:
            await LogsHandler.update_request_log(log_entry_update)
        except Exception as e:
            log.error(
                f"Tracking Service: Failed to log request failure {request_id}: {e}"
            )

    async def cancel_request_task(
        self, request_id: uuid.UUID, reason: str = "Canceled by user"
    ) -> bool:
        """
        Mark the request log as canceled.
        NOTE: This method *only updates the log status*. The actual task cancellation
        must happen elsewhere (e.g., in a dedicated API endpoint).
        """
        try:
            # Optionally fetch the log first to ensure it exists and is in a cancellable state
            # log_entry = await self.get_request_status(request_id)
            # if not log_entry or log_entry.status not in [RequestStatus.PROCESSING, RequestStatus.RECEIVED]:
            #     log.warning(f"Cannot mark request {request_id} as canceled, invalid status: {log_entry.status if log_entry else 'Not Found'}")
            #     return False

            update_data = {
                "status": RequestStatus.CANCELED,
                "error_message": reason,
                # Optionally update end_time/duration if cancellation implies termination
                "end_time": datetime.now(timezone.utc),
            }

            await LogsHandler.update_request_log_partial(request_id, update_data)

            log.info(f"Marked request {request_id} as canceled in tracking log.")
            return True
        except Exception as e:
            log.error(
                f"Tracking Service: Failed to mark request {request_id} as canceled: {e}"
            )
            return False

    async def get_request_status(
        self, request_id: uuid.UUID
    ) -> Optional[RequestStatusResponse]:
        """Get the status of a specific request from the log."""
        try:
            log_entry = await LogsHandler.get_request_log(request_id)

            if log_entry:
                return RequestStatusResponse(**log_entry.model_dump())
            return None
        except RequestTrackingException:
            # Logged in handler
            return None  # Or re-raise depending on desired behavior

    async def get_request_history(
        self,
        limit: int = 100,
        offset: int = 0,
        username: Optional[str] = None,
        status: Optional[RequestStatus] = None,
        endpoint_path: Optional[str] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
    ) -> List[RequestStatusResponse]:
        """Get request history from the log."""
        try:
            log_entries = await LogsHandler.get_request_history(
                limit=limit,
                offset=offset,
                username=username,
                status=status,
                endpoint_path=endpoint_path,
                from_date=from_date,
                to_date=to_date,
            )

            return [
                RequestStatusResponse(**entry.model_dump()) for entry in log_entries
            ]
        except RequestTrackingException:
            # Logged in handler
            return []  # Or re-raise
