"""
Unit tests for export error handling system.

Tests the comprehensive error handling framework including error types,
context tracking, and error context manager functionality.
"""

import pytest
import time
from unittest.mock import Mock, patch

from src.magic_gateway.export.exceptions import (
    ExportError,
    ConnectionPoolExhaustedError,
    ConnectionTimeoutError,
    TableNotFoundError,
    FormatConversionError,
    InsufficientResourcesError,
    JobMetadataError,
    ConnectionPoolStatusError,
    ResourceCleanupError,
    ExportValidationError,
    StreamingError,
    ExportErrorContext
)


class TestExportErrorBase:
    """Test the base ExportError class functionality."""
    
    def test_export_error_creation(self):
        """Test basic ExportError creation and properties."""
        error = ExportError(
            error_type="test_error",
            message="Test error message",
            context={"key": "value"},
            request_id="req-123",
            recovery_suggestions=["Try again"]
        )
        
        assert error.error_type == "test_error"
        assert error.message == "Test error message"
        assert error.context == {"key": "value"}
        assert error.request_id == "req-123"
        assert error.recovery_suggestions == ["Try again"]
        assert isinstance(error.timestamp, float)
        assert error.operation is None
        assert error.connection_pool_status is None
        
    def test_export_error_string_representation(self):
        """Test ExportError string representation."""
        error = ExportError(
            error_type="test_error",
            message="Test message",
            context={},
            request_id="req-123",
            recovery_suggestions=[]
        )
        
        expected = "test_error: Test message (Request ID: req-123)"
        assert str(error) == expected
        
    def test_export_error_to_dict(self):
        """Test ExportError dictionary conversion."""
        error = ExportError(
            error_type="test_error",
            message="Test message",
            context={"key": "value"},
            request_id="req-123",
            recovery_suggestions=["suggestion1"],
            operation="test_operation"
        )
        
        result = error.to_dict()
        
        assert result["error_type"] == "test_error"
        assert result["message"] == "Test message"
        assert result["context"] == {"key": "value"}
        assert result["request_id"] == "req-123"
        assert result["recovery_suggestions"] == ["suggestion1"]
        assert result["operation"] == "test_operation"
        assert "timestamp" in result
        assert result["connection_pool_status"] is None


class TestSpecificErrorTypes:
    """Test specific error type implementations."""
    
    def test_connection_pool_exhausted_error(self):
        """Test ConnectionPoolExhaustedError creation and properties."""
        error = ConnectionPoolExhaustedError(
            request_id="req-123",
            pool_name="test_pool",
            active_connections=10,
            max_connections=10
        )
        
        assert error.error_type == "connection_pool_exhausted"
        assert "test_pool" in error.message
        assert "10/10" in error.message
        assert error.context["pool_name"] == "test_pool"
        assert error.context["active_connections"] == 10
        assert error.context["max_connections"] == 10
        assert len(error.recovery_suggestions) > 0
        
    def test_connection_timeout_error(self):
        """Test ConnectionTimeoutError creation and properties."""
        error = ConnectionTimeoutError(
            request_id="req-123",
            timeout_seconds=30.0,
            operation="query_execution"
        )
        
        assert error.error_type == "connection_timeout"
        assert "30.0s" in error.message
        assert "query_execution" in error.message
        assert error.context["timeout_seconds"] == 30.0
        assert error.context["operation"] == "query_execution"
        
    def test_table_not_found_error(self):
        """Test TableNotFoundError creation and properties."""
        error = TableNotFoundError(
            request_id="req-123",
            job_id=456,
            table_name="test_table",
            database_name="test_db"
        )
        
        assert error.error_type == "table_not_found"
        assert "test_db.test_table" in error.message
        assert "job 456" in error.message
        assert error.context["job_id"] == 456
        assert error.context["table_name"] == "test_table"
        assert error.context["database_name"] == "test_db"
        
    def test_format_conversion_error(self):
        """Test FormatConversionError creation and properties."""
        error = FormatConversionError(
            request_id="req-123",
            source_format="parquet",
            target_format="csv",
            error_details="Invalid data type"
        )
        
        assert error.error_type == "format_conversion_failed"
        assert "parquet" in error.message
        assert "csv" in error.message
        assert "Invalid data type" in error.message
        assert error.context["source_format"] == "parquet"
        assert error.context["target_format"] == "csv"
        
    def test_job_metadata_error(self):
        """Test JobMetadataError creation and properties."""
        error = JobMetadataError(
            request_id="req-123",
            job_id=789,
            error_details="Job not found"
        )
        
        assert error.error_type == "job_metadata_error"
        assert "job 789" in error.message
        assert "Job not found" in error.message
        assert error.context["job_id"] == 789
        assert error.context["error_details"] == "Job not found"
        
    def test_connection_pool_status_error(self):
        """Test ConnectionPoolStatusError creation and properties."""
        pool_status = {"active": 5, "idle": 2}
        error = ConnectionPoolStatusError(
            request_id="req-123",
            pool_name="test_pool",
            status_details="Pool unhealthy",
            pool_status=pool_status
        )
        
        assert error.error_type == "connection_pool_status_error"
        assert "test_pool" in error.message
        assert "Pool unhealthy" in error.message
        assert error.connection_pool_status == pool_status
        
    def test_resource_cleanup_error(self):
        """Test ResourceCleanupError creation and properties."""
        error = ResourceCleanupError(
            request_id="req-123",
            resource_type="temp_file",
            resource_id="file123.tmp",
            cleanup_error="Permission denied"
        )
        
        assert error.error_type == "resource_cleanup_error"
        assert "temp_file" in error.message
        assert "file123.tmp" in error.message
        assert "Permission denied" in error.message
        
    def test_export_validation_error(self):
        """Test ExportValidationError creation and properties."""
        error = ExportValidationError(
            request_id="req-123",
            validation_type="parameter",
            validation_details="Invalid format specified"
        )
        
        assert error.error_type == "export_validation_error"
        assert "parameter" in error.message
        assert "Invalid format specified" in error.message
        
    def test_streaming_error(self):
        """Test StreamingError creation and properties."""
        error = StreamingError(
            request_id="req-123",
            stream_type="parquet",
            error_details="Connection lost",
            bytes_processed=1024
        )
        
        assert error.error_type == "streaming_error"
        assert "parquet" in error.message
        assert "Connection lost" in error.message
        assert error.context["bytes_processed"] == 1024


class TestExportErrorContext:
    """Test the ExportErrorContext context manager."""
    
    def test_error_context_basic_usage(self):
        """Test basic error context manager usage."""
        with ExportErrorContext("req-123", "test_operation") as ctx:
            assert ctx.request_id == "req-123"
            assert ctx.operation == "test_operation"
            assert "operation" in ctx.context
            assert "start_time" in ctx.context
            
    def test_error_context_with_job_id(self):
        """Test error context with job ID."""
        with ExportErrorContext("req-123", "export", job_id=456) as ctx:
            assert ctx.job_id == 456
            assert ctx.context["job_id"] == 456
            
    def test_error_context_add_context(self):
        """Test adding additional context information."""
        with ExportErrorContext("req-123", "test") as ctx:
            ctx.add_context("custom_key", "custom_value")
            assert ctx.context["custom_key"] == "custom_value"
            
    def test_error_context_enhances_export_error(self):
        """Test that context manager enhances ExportError exceptions."""
        with pytest.raises(ExportError) as exc_info:
            with ExportErrorContext("req-123", "test_operation") as ctx:
                ctx.add_context("test_key", "test_value")
                raise ExportError(
                    error_type="test",
                    message="test",
                    context={},
                    request_id="req-123",
                    recovery_suggestions=[]
                )
                
        error = exc_info.value
        assert error.operation == "test_operation"
        assert "test_key" in error.context
        assert error.context["test_key"] == "test_value"
        assert "operation_duration" in error.context
        
    def test_error_context_converts_non_export_errors(self):
        """Test that context manager converts non-ExportError exceptions."""
        with pytest.raises(ExportError) as exc_info:
            with ExportErrorContext("req-123", "test_operation"):
                raise ValueError("Test error")
                
        error = exc_info.value
        assert error.error_type == "unexpected_error"
        assert error.operation == "test_operation"
        assert "Test error" in error.message
        assert "operation_duration" in error.context
        assert error.context["original_exception_type"] == "ValueError"
        
    def test_error_context_with_connection_pool_adapter(self):
        """Test error context with connection pool adapter."""
        mock_adapter = Mock()
        
        with ExportErrorContext(
            "req-123", 
            "test_operation", 
            connection_pool_adapter=mock_adapter
        ) as ctx:
            # Should not raise even if adapter doesn't have expected methods
            pass
            
        assert ctx.connection_pool_adapter == mock_adapter
        
    def test_error_context_timing(self):
        """Test that error context tracks operation timing."""
        with pytest.raises(ExportError) as exc_info:
            with ExportErrorContext("req-123", "test_operation"):
                time.sleep(0.01)  # Small delay to test timing
                raise ValueError("Test error")
                
        error = exc_info.value
        assert "operation_duration" in error.context
        assert error.context["operation_duration"] > 0
        
    def test_error_context_no_exception(self):
        """Test error context when no exception occurs."""
        # Should not raise any exceptions
        with ExportErrorContext("req-123", "test_operation") as ctx:
            ctx.add_context("success", True)
            
        # Context should still be populated
        assert ctx.context["success"] is True