-- отобор на 1 итерации         
SELECT 
    position_number, 
    buyers_rp * projectc / 1000 as buyers_rp,
    population * projectc / 1000 as population,
    projectc,
	buyers_rp / population as penetration
FROM (
	SELECT
		position_number,
	    any(population) AS population,
	    sum(buyers) AS buyers_rp,    
	    any(projectc) as projectc
	FROM final_KPI_rwbasis
	GROUP BY
	    position_number
)
ORDER BY buyers_rp DESC
LIMIT 1;