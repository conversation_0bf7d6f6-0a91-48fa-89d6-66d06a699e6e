# magic_gateway/api/deps.py

import uuid
from typing import Optional, Dict, Any
from datetime import datetime, timezone

from fastapi import Depends, Request, HTTPException, status

from magic_gateway.core.logging_config import log
from magic_gateway.core.config import settings
from magic_gateway.db.connection_manager import (
    postgres_connection_manager,
    clickhouse_connection_manager,
    logs_connection_manager,
    clickhouse_registry,
)
from magic_gateway.monitoring.service import connection_pool_monitoring_service
from magic_gateway.scripts.runner import <PERSON><PERSON>t<PERSON><PERSON>ner
from magic_gateway.tracking.service import RequestTrackingService


# --- Request Tracking Dependency ---
_request_tracker_service_instance = RequestTrackingService()


async def get_request_tracker_service() -> RequestTrackingService:
    """Dependency to get the request tracking service instance."""
    # In a real app, might initialize dependencies elsewhere (e.g., app startup)
    return _request_tracker_service_instance


async def track_request_details(
    request: Request,
    tracker: RequestTrackingService = Depends(get_request_tracker_service),
) -> uuid.UUID:
    """
    Dependency that makes request_id available and allows updating tracking context.

    Usage in endpoint:
    async def my_endpoint(
        req_id: uuid.UUID = Depends(track_request_details),
        tracker: RequestTrackingService = Depends(get_request_tracker_service),
        ...
    ):
        # req_id is available
        # Update username after auth:
        # await tracker.update_request_user(req_id, current_user['username'])
        # Add task details:
        # await tracker.add_task_details(req_id, {"db_query_id": "xyz"})
        ...
    """
    request_id = getattr(request.state, "request_id", None)
    if not request_id:
        # This should not happen if middleware runs first, but handle defensively
        log.error("Request ID not found in state for tracking details.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error: Request tracking context missing.",
        )
    return request_id


async def update_request_context(
    request: Request,
    username: Optional[str] = None,
    task_details: Optional[Dict[str, Any]] = None,
):
    """Helper to update request state for middleware logging."""
    if username:
        request.state.request_username = username
    if task_details:
        # Merge details if some already exist? For simplicity, this replaces.
        request.state.request_task_details = task_details


# --- Database Connection Check Dependency ---
async def check_database_connections() -> Dict[str, Dict[str, Any]]:
    """Check connectivity for configured databases."""
    results = {}
    now = datetime.now(timezone.utc)

    # Check PostgreSQL
    pg_connected = False
    pg_message = "Not initialized or check failed"
    if postgres_connection_manager.initialized and postgres_connection_manager._pool:
        try:
            async with postgres_connection_manager.connection() as conn:
                await conn.execute("SELECT 1")
            pg_connected = True
            pg_message = "Connection successful"
        except Exception as e:
            pg_message = f"Connection check failed: {type(e).__name__}"
            log.warning(f"Health Check: PostgreSQL connection failed: {e}")
    results["postgres"] = {
        "connected": pg_connected,
        "message": pg_message,
        "last_checked": now,
    }

    # Check ClickHouse (primary)
    ch_connected = False
    ch_message = "Not initialized or check failed"

    # Add debug logging
    log.debug(
        f"Health Check: ClickHouse registry initialized: {clickhouse_registry.initialized}"
    )

    # Use the registry to get the primary connection manager
    if clickhouse_registry.initialized:
        try:
            # Get the primary manager from the registry
            log.debug(
                f"Health Check: Available clusters in registry: {clickhouse_registry.get_available_clusters()}"
            )
            primary_manager = clickhouse_registry.get_manager("primary")
            log.debug(
                f"Health Check: Primary manager initialized: {primary_manager.initialized}"
            )

            if primary_manager.initialized and primary_manager._pool:
                log.debug(
                    "Health Check: Primary manager pool exists, attempting connection"
                )
                # Use the internal ping method for a quick check
                async with primary_manager.connection() as client:
                    # The connection context manager already performs a ping if reusing idle
                    # If creating new, it tests with SELECT 1. So acquiring is enough.
                    pass
                ch_connected = True
                ch_message = "Connection successful"
                log.debug("Health Check: ClickHouse primary connection successful")
        except Exception as e:
            ch_message = f"Connection check failed: {type(e).__name__}"
            log.warning(f"Health Check: ClickHouse primary connection failed: {e}")
    else:
        log.warning("Health Check: ClickHouse registry not initialized")

    results["clickhouse"] = {
        "connected": ch_connected,
        "message": ch_message,
        "last_checked": now,
    }

    # Check ClickHouse Cluster (if configured)
    if settings.has_clickhouse_cluster:
        cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
        ch_cluster_connected = False
        ch_cluster_message = "Not initialized or check failed"

        # Check if registry is initialized
        if clickhouse_registry.initialized:
            try:
                # Get the cluster manager and test connection
                cluster_manager = clickhouse_registry.get_manager(cluster_name)
                if cluster_manager.initialized and cluster_manager._pool:
                    async with cluster_manager.connection() as client:
                        # Connection context manager performs the check
                        pass
                    ch_cluster_connected = True
                    ch_cluster_message = "Connection successful"
            except Exception as e:
                ch_cluster_message = f"Connection check failed: {type(e).__name__}"
                log.warning(
                    f"Health Check: ClickHouse cluster '{cluster_name}' connection failed: {e}"
                )

        # Add cluster status to results
        results[f"clickhouse_{cluster_name}"] = {
            "connected": ch_cluster_connected,
            "message": ch_cluster_message,
            "last_checked": now,
        }

    # Check Logs PostgreSQL
    logs_connected = False
    logs_message = "Not initialized or check failed"
    if logs_connection_manager.initialized and logs_connection_manager._connection:
        try:
            async with logs_connection_manager.connection() as conn:
                await conn.execute("SELECT 1")
            logs_connected = True
            logs_message = "Connection successful"
        except Exception as e:
            logs_message = f"Connection check failed: {type(e).__name__}"
            log.warning(f"Health Check: Logs PostgreSQL connection failed: {e}")
    results["logs"] = {
        "connected": logs_connected,
        "message": logs_message,
        "last_checked": now,
    }

    return results


# --- Connection Pool Status Dependency ---
async def check_connection_pools() -> Dict[str, Dict[str, Any]]:
    """Get status of all connection pools."""
    # Ensure we have the latest metrics
    await connection_pool_monitoring_service.collect_and_store_metrics()

    # Get the status of all pools and convert to dictionaries
    pool_status = connection_pool_monitoring_service.get_connection_pool_status()

    # Convert Pydantic models to dictionaries
    return {pool_type: status.model_dump() for pool_type, status in pool_status.items()}


# --- Script Runner Dependency ---
_script_runner_instance = ScriptRunner()


async def get_script_runner() -> ScriptRunner:
    """Dependency to get the script runner instance."""
    return _script_runner_instance


# --- Connection Manager Dependencies for Scripts ---
async def get_connection_managers() -> Optional[Dict[str, Any]]:
    """
    Dependency to get connection managers for script injection.

    Returns a dictionary of available connection managers that can be injected
    into script execution context. Only returns initialized connection managers.

    Returns:
        Dictionary of connection manager instances, or None if none are available
    """
    connection_managers = {}

    # Check PostgreSQL connection manager
    if postgres_connection_manager.initialized:
        connection_managers["postgres"] = postgres_connection_manager
        log.debug("PostgreSQL connection manager available for script injection")
    else:
        log.warning(
            "PostgreSQL connection manager not initialized for script injection"
        )

    # Check ClickHouse primary connection manager
    if clickhouse_registry.initialized:
        try:
            primary_manager = clickhouse_registry.get_manager("primary")
            if primary_manager.initialized:
                connection_managers["clickhouse_primary"] = primary_manager
                log.debug(
                    "ClickHouse primary connection manager available for script injection"
                )
            else:
                log.warning(
                    "ClickHouse primary connection manager not initialized for script injection"
                )
        except Exception as e:
            log.warning(
                f"Failed to get ClickHouse primary manager for script injection: {e}"
            )

    # Check ClickHouse cluster connection manager (if configured)
    if settings.has_clickhouse_cluster and clickhouse_registry.initialized:
        cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
        try:
            cluster_manager = clickhouse_registry.get_manager(cluster_name)
            if cluster_manager.initialized:
                connection_managers["clickhouse_cluster"] = cluster_manager
                log.debug(
                    f"ClickHouse cluster '{cluster_name}' connection manager available for script injection"
                )
            else:
                log.warning(
                    f"ClickHouse cluster '{cluster_name}' connection manager not initialized for script injection"
                )
        except Exception as e:
            log.warning(
                f"Failed to get ClickHouse cluster manager '{cluster_name}' for script injection: {e}"
            )

    # Check Logs PostgreSQL connection manager
    if logs_connection_manager.initialized:
        connection_managers["logs"] = logs_connection_manager
        log.debug("Logs PostgreSQL connection manager available for script injection")
    else:
        log.warning(
            "Logs PostgreSQL connection manager not initialized for script injection"
        )

    # Return connection managers if any are available
    if connection_managers:
        log.info(
            f"Connection managers available for script injection: {list(connection_managers.keys())}"
        )
        return connection_managers
    else:
        log.warning("No connection managers available for script injection")
        return None


async def check_connection_managers_initialization() -> Dict[str, bool]:
    """
    Check initialization status of all connection managers before script execution.

    This function verifies that connection managers are properly initialized
    and ready for use in script execution context.

    Returns:
        Dictionary mapping connection manager names to their initialization status
    """
    initialization_status = {}

    # Check PostgreSQL connection manager
    initialization_status["postgres"] = (
        postgres_connection_manager.initialized
        and postgres_connection_manager._pool is not None
    )

    # Check ClickHouse primary connection manager
    if clickhouse_registry.initialized:
        try:
            primary_manager = clickhouse_registry.get_manager("primary")
            initialization_status["clickhouse_primary"] = (
                primary_manager.initialized and primary_manager._pool is not None
            )
        except Exception as e:
            log.warning(
                f"Error checking ClickHouse primary manager initialization: {e}"
            )
            initialization_status["clickhouse_primary"] = False
    else:
        initialization_status["clickhouse_primary"] = False

    # Check ClickHouse cluster connection manager (if configured)
    if settings.has_clickhouse_cluster:
        cluster_name = settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
        if clickhouse_registry.initialized:
            try:
                cluster_manager = clickhouse_registry.get_manager(cluster_name)
                initialization_status[f"clickhouse_cluster_{cluster_name}"] = (
                    cluster_manager.initialized and cluster_manager._pool is not None
                )
            except Exception as e:
                log.warning(
                    f"Error checking ClickHouse cluster manager '{cluster_name}' initialization: {e}"
                )
                initialization_status[f"clickhouse_cluster_{cluster_name}"] = False
        else:
            initialization_status[f"clickhouse_cluster_{cluster_name}"] = False

    # Check Logs PostgreSQL connection manager
    initialization_status["logs"] = (
        logs_connection_manager.initialized
        and logs_connection_manager._connection is not None
    )

    # Log initialization status
    initialized_managers = [
        name for name, status in initialization_status.items() if status
    ]
    uninitialized_managers = [
        name for name, status in initialization_status.items() if not status
    ]

    if initialized_managers:
        log.info(f"Initialized connection managers: {initialized_managers}")
    if uninitialized_managers:
        log.warning(f"Uninitialized connection managers: {uninitialized_managers}")

    return initialization_status


def prepare_connection_managers_for_injection(
    connection_managers: Optional[Dict[str, Any]],
) -> Optional[Any]:
    """
    Prepare connection managers for script injection.

    This helper function takes the raw connection managers dictionary and
    converts it into the ScriptConnectionManagers dataclass format expected
    by the script runner.

    Args:
        connection_managers: Dictionary of connection manager instances

    Returns:
        ScriptConnectionManagers instance or None if no managers available
    """
    if not connection_managers:
        log.debug("No connection managers to prepare for injection")
        return None

    # Import ScriptConnectionManagers here to avoid circular imports
    from magic_gateway.scripts.runner import ScriptConnectionManagers

    # Create ScriptConnectionManagers instance with available managers
    script_managers = ScriptConnectionManagers(
        clickhouse_primary=connection_managers.get("clickhouse_primary"),
        clickhouse_cluster=connection_managers.get("clickhouse_cluster"),
        postgres=connection_managers.get("postgres"),
        logs=connection_managers.get("logs"),
    )

    # Log what managers are being prepared for injection
    available_managers = []
    if script_managers.clickhouse_primary:
        available_managers.append("clickhouse_primary")
    if script_managers.clickhouse_cluster:
        available_managers.append("clickhouse_cluster")
    if script_managers.postgres:
        available_managers.append("postgres")
    if script_managers.logs:
        available_managers.append("logs")

    if available_managers:
        log.info(
            f"Prepared connection managers for script injection: {available_managers}"
        )
    else:
        log.warning("No connection managers prepared for script injection")
        return None

    return script_managers
