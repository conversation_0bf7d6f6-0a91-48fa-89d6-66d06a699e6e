# Assortment Optimizer Configuration

This document describes the configuration settings for the Assortment Optimizer script.

## Configuration Settings

The following settings can be configured in the `.env` file or through environment variables:

### Temporary File Storage Settings

| Setting | Description | Default Value |
|---------|-------------|---------------|
| `TEMP_FILE_STORAGE_PATH` | Path for temporary file storage | `"downloads"` |
| `TEMP_FILE_RETENTION_HOURS` | Hours to keep temporary files before cleanup | `24` |
| `TEMP_FILE_CLEANUP_INTERVAL_MINUTES` | Minutes between cleanup runs | `60` |
| `TEMP_FILE_MAX_SIZE_MB` | Maximum size of temporary files in MB | `100` |
| `TEMP_FILE_ALLOWED_EXTENSIONS` | Allowed file extensions | `["xlsx", "csv", "json", "txt"]` |

### Assortment Optimizer Specific Settings

| Setting | Description | Default Value |
|---------|-------------|---------------|
| `ASSORTMENT_OPTIMIZER_TIMEOUT` | Timeout for assortment optimizer script in seconds | `600` (10 minutes) |
| `ASSORTMENT_OPTIMIZER_FILE_PREFIX` | Prefix for assortment optimizer result files | `"assortment_optimizer_"` |
| `ASSORTMENT_OPTIMIZER_DEFAULT_EXTENSION` | Default file extension for assortment optimizer results | `"xlsx"` |

## File Management

The Assortment Optimizer script generates result files that are stored in the temporary file storage path. These files are automatically cleaned up after the configured retention period.

### File Naming Convention

Files are named using the following pattern:
```
{prefix}{timestamp}_{unique_id}.{extension}
```

For example:
```
assortment_optimizer_20250423_123456_a1b2c3d4.xlsx
```

### File Cleanup

Files are automatically cleaned up after the configured retention period. The cleanup process runs at the interval specified by `TEMP_FILE_CLEANUP_INTERVAL_MINUTES`.

### File Size Limits

Files are limited to the size specified by `TEMP_FILE_MAX_SIZE_MB`. If a file exceeds this size, an error will be raised.

## Example Configuration

```env
# Temporary file storage settings
TEMP_FILE_STORAGE_PATH=downloads
TEMP_FILE_RETENTION_HOURS=48
TEMP_FILE_CLEANUP_INTERVAL_MINUTES=120
TEMP_FILE_MAX_SIZE_MB=200
TEMP_FILE_ALLOWED_EXTENSIONS=["xlsx", "csv", "json", "txt", "pdf"]

# Assortment optimizer settings
ASSORTMENT_OPTIMIZER_TIMEOUT=1200
ASSORTMENT_OPTIMIZER_FILE_PREFIX=assortment_optimizer_
ASSORTMENT_OPTIMIZER_DEFAULT_EXTENSION=xlsx
```