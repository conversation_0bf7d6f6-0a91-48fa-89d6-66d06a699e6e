"""Logging configuration for the MagicGateway application."""

import logging
import sys
from pathlib import Path

from loguru import logger

from magic_gateway.core.config import settings


class InterceptHandler(logging.Handler):
    """
    Intercept standard logging messages and redirect them to Loguru.

    This allows libraries that use the standard logging module to have their
    logs processed by Loguru.
    """

    def emit(self, record: logging.LogRecord) -> None:
        """Intercept log records and pass them to Loguru."""
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where the logged message originated
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_logging() -> None:
    """Configure logging for the application."""
    # Remove default loguru handler
    logger.remove()

    # Define a filter function to reduce SQL query verbosity and filter noisy messages
    def filter_log_messages(record):
        """Filter out long SQL queries and noisy debug messages from logs."""
        message = record["message"]

        # Filter out noisy ClickHouse driver debug messages about column reading
        if "clickhouse_driver" in record["name"] and "read column" in message.lower():
            return False

        # Filter out other noisy ClickHouse driver messages
        if "clickhouse_driver" in record["name"] and any(
            x in message.lower()
            for x in [
                "packet:",
                "sent packet",
                "received packet",
                "read block",
                "read varint",
            ]
        ):
            return False

        # Filter out noisy form data processing messages
        if record["level"].name == "DEBUG" and "Calling on_field" in message:
            return False

        # Filter out other noisy debug messages
        if record["level"].name == "DEBUG" and any(
            x in message
            for x in [
                "on_field_start",
                "on_field_name",
                "on_field_data",
                "on_field_end",
                "on_end with no data",
            ]
        ):
            return False

        # Filter out detailed SQL query logs at INFO level (keep them at DEBUG)
        if record["level"].name == "INFO" and any(
            x in message for x in ["Query:", "Command:", "SQL:"]
        ):
            # Only show these detailed logs at DEBUG level
            if settings.LOG_LEVEL.upper() != "DEBUG":
                return False

        # Check if this is a SQL query log that needs truncation
        if (
            message.startswith(
                ("Query:", "Command:", "SQL:", "Query summary:", "Command summary:")
            )
            and len(message) > 100
            and "["
            not in message[:100]  # Skip if already formatted with our new formatter
        ):
            # Truncate long SQL queries more aggressively
            record["message"] = message[:100] + f"... [truncated, {len(message)} chars]"

        # Always include other records
        return True

    # Configure loguru
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

    # Add console handler
    logger.add(
        sys.stderr,
        format=log_format,
        level=settings.LOG_LEVEL,
        colorize=True,
        filter=filter_log_messages,
    )

    # Create memlog directory if it doesn't exist
    log_dir = Path("memlog")
    log_dir.mkdir(exist_ok=True, parents=True)

    # Add file handler for all environments
    if settings.ENVIRONMENT == "development":
        logger.add(
            "memlog/magic_gateway_dev.log",
            rotation="10 MB",
            retention="1 week",
            format=log_format,
            level="DEBUG",
            filter=filter_log_messages,
        )
    else:
        logger.add(
            "memlog/magic_gateway.log",
            rotation="10 MB",
            retention="1 week",
            format=log_format,
            level=settings.LOG_LEVEL,
            compression="zip",
            filter=filter_log_messages,
        )

    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Update logging levels for some noisy libraries
    for logger_name in (
        "uvicorn",
        "uvicorn.error",
        "uvicorn.access",
        "fastapi",
        "starlette",
        "starlette.middleware",
        "starlette.middleware.errors",
        "starlette.middleware.exceptions",
        "starlette.routing",
        "multiprocessing",
        "asyncio",
        "clickhouse_driver",
        "clickhouse_driver.connection",
        "clickhouse_driver.client",
        "clickhouse_driver.protocol",
        "clickhouse_driver.columns",
        "urllib3",
        "httpx",
    ):
        logging_logger = logging.getLogger(logger_name)
        logging_logger.handlers = [InterceptHandler()]
        logging_logger.propagate = False

        # Set higher log level for these libraries
        if logger_name not in ("uvicorn.error"):
            logging_logger.setLevel(logging.WARNING)

        # Set even higher log level for ClickHouse driver to reduce noise
        if logger_name.startswith("clickhouse_driver"):
            logging_logger.setLevel(logging.ERROR)

        # Set higher log level for FastAPI and Starlette to reduce form processing noise
        if logger_name in ("fastapi", "starlette") or logger_name.startswith(
            "starlette."
        ):
            logging_logger.setLevel(logging.ERROR)

    # Log some basic information
    logger.info(f"Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")
    logger.info(f"Log level: {settings.LOG_LEVEL}")


# Export the logger instance
log = logger
