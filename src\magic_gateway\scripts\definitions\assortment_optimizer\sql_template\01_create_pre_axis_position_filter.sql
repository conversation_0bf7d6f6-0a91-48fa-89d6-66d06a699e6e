CREATE TEMPORARY TABLE pre_axis AS
SELECT  DISTINCT
	{{position_number}} as position_number,
	m.hhkey as hhkey,
	(m.hhkey || m.movedate::text || m.id_shop::text) AS id_trip,
	weight_wave, 
	rwbasis,
    population,
    m.fullmass as fullmass,
    m.rw_compensat as rw_compensat,
    m.projectc as projectc
-- if Article axis join purchase and axis table
FROM pet.purchases_{{id_panel}} as m
INNER JOIN (
	SELECT * FROM {{temp_axis_name}}
	WHERE position_number in %(position_number_filter)s
) as axis on 
{% if axis_type == 'axsa' %}
	axis."article_key" = m."article_key" AND m.movedate >= axis.reporting_from and m.movedate <= axis.to_date 
{% endif %}

-- if movement axis take data from an axis directly
{% if axis_type == 'axsm' %}
	axis."id_rec" = m."id_rec" AND m.movedate = axis.movedate
{% endif %}

INNER JOIN (
	SELECT
		sum(fullmass) / (age('month', toDate(%(start_date)s), toDate(%(end_date)s)) + 1) AS weight_wave,
		sum(weight_wave) OVER (PARTITION BY rwbasis) AS population,
			hhkey,
			argMax(rwbasis, dt_start) AS rwbasis
	FROM pet.hh_weights_fullmass
	WHERE id_panel=%(id_panel)s AND dt_start >= %(start_date)s AND dt_end <= %(end_date)s
	GROUP BY hhkey
) p on m.hhkey = p.hhkey
WHERE m.movedate BETWEEN %(start_date)s and %(end_date)s
