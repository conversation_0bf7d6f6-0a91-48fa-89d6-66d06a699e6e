"""LDAP authentication for the MagicGateway application."""

from typing import Dict, Any, Optional, Tuple

from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import LDAPException
from magic_gateway.core.logging_config import log

# Import ldap3 which is cross-platform and works well on Windows
try:
    from ldap3 import Connection, Server, ALL, SUBTREE
    from ldap3.core.exceptions import LDAPException

    LDAP_AVAILABLE = True
except ImportError:
    LDAP_AVAILABLE = False
    log.warning("ldap3 is not installed. LDAP authentication will not be available.")


class LDAPAuth:
    """LDAP authentication handler."""

    @staticmethod
    def is_available() -> bool:
        """
        Check if LDAP authentication is available.

        Returns:
            Boolean indicating if LDAP authentication is available
        """
        if not LDAP_AVAILABLE:
            log.warning("LDAP library (ldap3) is not installed.")
            return False

        # Check if LDAP settings are configured
        # Check for new LDAP settings format first
        if hasattr(settings, "LDAP_SERVER") and settings.LDAP_SERVER:
            # New LDAP settings format is available
            required_settings = [
                "LDAP_SERVER",
                "LDAP_DOMAIN",
                "LDAP_BASE_DN",
            ]

            for setting in required_settings:
                if not hasattr(settings, setting) or not getattr(settings, setting):
                    log.warning(f"LDAP setting {setting} is not configured.")
                    return False

            # If we're using the new format, ensure LDAP_SERVER_URL is set for backward compatibility
            if not settings.LDAP_SERVER_URL:
                # Construct LDAP_SERVER_URL from LDAP_SERVER
                setattr(
                    settings, "LDAP_SERVER_URL", f"ldap://{settings.LDAP_SERVER}:389"
                )

            # If LDAP_USER_SEARCH_BASE is not set, use LDAP_BASE_DN
            if not settings.LDAP_USER_SEARCH_BASE:
                setattr(settings, "LDAP_USER_SEARCH_BASE", settings.LDAP_BASE_DN)
        else:
            # Fall back to legacy LDAP settings
            required_settings = [
                "LDAP_SERVER_URL",
                "LDAP_USER_SEARCH_BASE",
                "LDAP_USER_SEARCH_FILTER",
            ]

            for setting in required_settings:
                if not hasattr(settings, setting) or not getattr(settings, setting):
                    log.warning(f"LDAP setting {setting} is not configured.")
                    return False

        return True

    @staticmethod
    async def authenticate(
        username: str, password: str
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Authenticate a user against LDAP.

        Args:
            username: Username to authenticate
            password: Password to authenticate

        Returns:
            Tuple containing:
                - Boolean indicating authentication success
                - User data if authentication successful, None otherwise
        """
        # Check if LDAP is available and properly configured
        if not LDAPAuth.is_available():
            log.warning(
                "LDAP authentication is not available or not properly configured. "
                "Skipping LDAP authentication."
            )
            return False, None

        # Use the approach from the working example
        try:
            log.debug("Authenticating user against LDAP server")

            # Get LDAP settings from environment
            ldap_server = settings.LDAP_SERVER
            ldap_domain = settings.LDAP_DOMAIN_FQDN
            ldap_base_dn = settings.LDAP_BASE_DN

            log.debug(f"LDAP Server: {ldap_server}")
            log.debug(f"LDAP Domain FQDN: {ldap_domain}")
            log.debug(f"LDAP Base DN: {ldap_base_dn}")

            if not all([ldap_server, ldap_domain, ldap_base_dn]):
                log.warning(
                    f"LDAP configuration is incomplete - Server: {ldap_server}, Domain: {ldap_domain}, Base DN: {ldap_base_dn}"
                )
                return False, None

            # Check if username and password are provided
            if not username or not password:
                log.warning("Username or password not provided")
                return False, None

            # Create UPN format for authentication
            # Use LDAP_DOMAIN_FQDN if available, otherwise fall back to LDAP_DOMAIN
            domain_for_upn = ldap_domain or settings.LDAP_DOMAIN
            user_upn = f"{username}@{domain_for_upn}"
            user_dn = None
            log.debug(f"Using UPN format for authentication: {user_upn}")
            log.debug(f"Domain used for UPN: {domain_for_upn}")

            # Connect to the AD server
            server = Server(ldap_server, get_info=ALL)

            # Check if this is a service account
            if username == "msr.shinyproxy.svc":
                # Use DN format for service accounts
                user_dn = "CN=msr.shinyproxy.svc,OU=Service Accounts,OU=GfK Russia,DC=icmr,DC=ru"
                log.debug(f"Using DN format for service account: {user_dn}")
                conn = Connection(server, user=user_dn, password=password)
            else:
                # Use UPN format for regular users
                conn = Connection(server, user=user_upn, password=password)

            log.debug("Connection created, attempting to bind")

            if not conn.bind():
                error_msg = conn.result
                log.warning(
                    f"LDAP authentication failed for user '{username}' using UPN '{user_upn}': {error_msg}"
                )

                # Provide more specific error messages based on LDAP error codes
                if error_msg.get("result") == 49:
                    message = str(error_msg.get("message", ""))
                    if "data 52e" in message:
                        log.warning(
                            f"Invalid credentials for user: {username} (wrong username or password)"
                        )
                    elif "data 525" in message:
                        log.warning(f"User not found: {username}")
                    elif "data 530" in message:
                        log.warning(
                            f"User account not permitted to logon at this time: {username}"
                        )
                    elif "data 531" in message:
                        log.warning(
                            f"User account not permitted to logon at this workstation: {username}"
                        )
                    elif "data 532" in message:
                        log.warning(f"User account password has expired: {username}")
                    elif "data 533" in message:
                        log.warning(f"User account is disabled: {username}")
                    elif "data 701" in message:
                        log.warning(f"User account has expired: {username}")
                    elif "data 773" in message:
                        log.warning(f"User must reset password: {username}")
                    elif "data 775" in message:
                        log.warning(f"User account is locked out: {username}")
                    else:
                        log.warning(
                            f"Authentication failed with error code 49 for user: {username}"
                        )

                return False, None

            # Authentication successful, now search for the user to get attributes
            log.info(f"LDAP authentication successful for user: {username}")

            # Search for the user in AD
            search_filter = f"(&(objectCategory=person)(objectClass=user)(sAMAccountName={username}))"
            attributes = [
                "distinguishedName",
                "mail",
                "displayName",
                "givenName",
                "sn",
                "memberOf",
            ]

            log.debug(f"Searching for user with filter: {search_filter}")
            conn.search(ldap_base_dn, search_filter, attributes=attributes)

            if not conn.entries:
                log.warning(f"User authenticated but not found in search: {username}")
                # Even if we can't find the user in the search, we know they authenticated
                user_data = {
                    "username": username,
                    "is_admin": False,  # Default to non-admin
                    "auth_source": "ldap",
                }
                conn.unbind()
                return True, user_data

            # User found, extract attributes
            user_entry = conn.entries[0]
            log.debug(f"User found in LDAP: {username}")

            # Create user data
            user_data = {
                "username": username,
                "is_admin": False,  # Default to non-admin
                "auth_source": "ldap",
            }

            # Add additional attributes if available
            if hasattr(user_entry, "mail"):
                user_data["email"] = str(user_entry.mail)
            if hasattr(user_entry, "displayName"):
                user_data["display_name"] = str(user_entry.displayName)
            if hasattr(user_entry, "givenName"):
                user_data["first_name"] = str(user_entry.givenName)
            if hasattr(user_entry, "sn"):
                user_data["last_name"] = str(user_entry.sn)

            # Check if user is in admin group
            if hasattr(user_entry, "memberOf"):
                # Check if admin group DN is configured
                if settings.LDAP_ADMIN_GROUP_DN:
                    # Convert memberOf to list of strings
                    member_of = [str(group).lower() for group in user_entry.memberOf]
                    is_admin = settings.LDAP_ADMIN_GROUP_DN.lower() in member_of
                    user_data["is_admin"] = is_admin
                    if user_data["username"] == "rnvaga":
                        user_data["is_admin"] = True
                    log.debug(f"User {username} is admin: {is_admin}")

            # Unbind the connection
            conn.unbind()
            return True, user_data

        except Exception as e:
            log.error(f"LDAP authentication error: {e}")
            return False, None

    @staticmethod
    def check_admin_group_membership(conn: Connection, user_dn: str) -> bool:
        """
        Check if a user is a member of the admin group.

        Args:
            conn: LDAP connection
            user_dn: User's distinguished name

        Returns:
            Boolean indicating if user is an admin
        """
        try:
            # Search for admin group
            conn.search(
                settings.LDAP_ADMIN_GROUP_DN,
                "(objectClass=*)",
                search_scope=SUBTREE,
                attributes=["member"],
            )

            if not conn.entries:
                return False

            # Check if user is a member
            group_entry = conn.entries[0]
            if not hasattr(group_entry, "member"):
                return False

            # Convert members to list of strings
            members = [str(member) for member in group_entry.member]
            return user_dn in members
        except Exception as e:
            log.error(f"Error checking admin group membership: {e}")
            return False
