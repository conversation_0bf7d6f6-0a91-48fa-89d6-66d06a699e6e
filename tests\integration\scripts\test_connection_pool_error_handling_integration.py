"""
Integration tests for connection pool error handling in scripts.

Tests the actual error handling behavior with real connection managers.
"""

import pytest
from unittest.mock import Mock, patch

from magic_gateway.core.exceptions import (
    ScriptExecutionException,
    ClickHouseException,
    ConnectionPoolExhaustedException,
    ConnectionTimeoutException,
    TransientConnectionException,
)
from magic_gateway.scripts.async_bridge import (
    execute_clickhouse_command,
    execute_clickhouse_query,
    is_transient_error,
    AsyncBridge,
)
from clickhouse_driver.errors import Error as ClickHouseDriverError
from clickhouse_driver.errors import NetworkError as ClickHouseNetworkError
from psycopg_pool import PoolTimeout


class TestErrorHandlingIntegration:
    """Integration tests for error handling functionality."""

    def test_transient_error_detection_clickhouse_network(self):
        """Test that ClickHouse network errors are correctly identified as transient."""
        error = ClickHouseNetworkError("Connection refused")
        assert is_transient_error(error) is True

    def test_transient_error_detection_pool_timeout(self):
        """Test that pool timeout errors are correctly identified as transient."""
        error = PoolTimeout("Pool timeout")
        assert is_transient_error(error) is True

    def test_non_transient_error_detection(self):
        """Test that non-transient errors are correctly identified."""
        error = ValueError("Some value error")
        assert is_transient_error(error) is False

    def test_connection_manager_validation(self):
        """Test that missing connection managers are properly handled."""
        bridge = AsyncBridge({})

        with pytest.raises(ScriptExecutionException) as exc_info:
            bridge.clickhouse_command("SELECT 1")

        assert "Connection manager 'clickhouse_primary' not available" in str(
            exc_info.value
        )

    def test_connection_manager_listing(self):
        """Test that available connection managers are listed in error messages."""
        managers = {"postgres": Mock(), "clickhouse_cluster": Mock()}
        bridge = AsyncBridge(managers)

        with pytest.raises(ScriptExecutionException) as exc_info:
            bridge.clickhouse_command("SELECT 1", manager_key="nonexistent")

        error_msg = str(exc_info.value)
        assert "Connection manager 'nonexistent' not available" in error_msg
        assert "postgres" in error_msg
        assert "clickhouse_cluster" in error_msg

    def test_async_bridge_context_manager(self):
        """Test AsyncBridge context manager functionality."""
        with AsyncBridge() as bridge:
            assert bridge._executor is not None

        # Executor should be shut down after exiting context
        assert (
            bridge._executor is not None
        )  # Reference remains but executor is shut down

    def test_error_classification_clickhouse_driver_error(self):
        """Test that ClickHouse driver errors are properly classified."""
        # Test connection pool exhaustion error
        pool_exhausted_error = ClickHouseDriverError("too many connections")
        assert "too many connections" in str(pool_exhausted_error).lower()

        # Test timeout error
        timeout_error = ClickHouseDriverError("connection timed out")
        assert is_transient_error(timeout_error) is True

    def test_error_message_formatting(self):
        """Test that error messages are properly formatted with context."""
        # This test verifies that our error handling preserves useful information
        error = ClickHouseDriverError("Code: 516. Authentication failed")
        formatted_msg = f"ClickHouse connection pool exhausted: {error}"

        assert "Code: 516" in formatted_msg
        assert "Authentication failed" in formatted_msg

    def test_connection_pool_exception_hierarchy(self):
        """Test that connection pool exceptions have proper inheritance."""
        # Test exception hierarchy
        pool_exhausted = ConnectionPoolExhaustedException("Pool exhausted")
        timeout_exception = ConnectionTimeoutException("Connection timeout")
        transient_exception = TransientConnectionException("Transient error")

        # All should be instances of the base connection pool exception
        assert isinstance(pool_exhausted, ConnectionPoolExhaustedException)
        assert isinstance(timeout_exception, ConnectionTimeoutException)
        assert isinstance(transient_exception, TransientConnectionException)

        # Test that they can be caught by the base exception
        try:
            raise pool_exhausted
        except ConnectionPoolExhaustedException:
            pass  # Expected

        try:
            raise timeout_exception
        except ConnectionTimeoutException:
            pass  # Expected

        try:
            raise transient_exception
        except TransientConnectionException:
            pass  # Expected


class TestErrorHandlingBehavior:
    """Test error handling behavior in realistic scenarios."""

    def test_connection_manager_none_handling(self):
        """Test handling when connection manager is None."""
        with pytest.raises(ScriptExecutionException) as exc_info:
            execute_clickhouse_command(None, "SELECT 1")

        assert "ClickHouse connection manager is required" in str(exc_info.value)

    def test_connection_manager_none_handling_query(self):
        """Test handling when connection manager is None for queries."""
        with pytest.raises(ScriptExecutionException) as exc_info:
            execute_clickhouse_query(None, "SELECT 1")

        assert "ClickHouse connection manager is required" in str(exc_info.value)

    def test_async_bridge_error_propagation(self):
        """Test that AsyncBridge properly propagates connection errors."""
        # Test with a mock manager that raises a specific error
        mock_manager = Mock()
        mock_manager.connection.side_effect = PoolTimeout("Pool timeout")

        bridge = AsyncBridge({"clickhouse_primary": mock_manager})

        # The error should propagate through the bridge
        with pytest.raises(ConnectionTimeoutException):
            bridge.clickhouse_command("SELECT 1")

    def test_error_logging_context(self):
        """Test that errors are logged with appropriate context."""
        # This test ensures that our error handling includes proper logging
        mock_manager = Mock()
        mock_manager.connection.side_effect = ClickHouseDriverError("Connection failed")

        with patch("magic_gateway.scripts.async_bridge.log") as mock_log:
            try:
                execute_clickhouse_command(mock_manager, "SELECT 1")
            except:
                pass  # We expect an exception

            # Verify that error logging occurred
            mock_log.error.assert_called()


class TestRetryLogic:
    """Test retry logic behavior."""

    def test_retry_logic_configuration(self):
        """Test that retry logic can be configured."""
        # This test verifies that our retry decorator accepts configuration
        from magic_gateway.scripts.async_bridge import retry_on_transient_error

        # Test that the decorator can be instantiated with different parameters
        decorator = retry_on_transient_error(
            max_retries=5, base_delay=2.0, max_delay=60.0
        )
        assert decorator is not None

    def test_exponential_backoff_calculation(self):
        """Test exponential backoff calculation logic."""
        # Test the mathematical progression of delays
        base_delay = 1.0
        exponential_base = 2.0
        max_delay = 30.0

        # Calculate expected delays
        expected_delays = []
        for attempt in range(5):
            delay = min(base_delay * (exponential_base**attempt), max_delay)
            expected_delays.append(delay)

        # Verify the progression: 1.0, 2.0, 4.0, 8.0, 16.0
        assert expected_delays[0] == 1.0
        assert expected_delays[1] == 2.0
        assert expected_delays[2] == 4.0
        assert expected_delays[3] == 8.0
        assert expected_delays[4] == 16.0

    def test_max_delay_capping(self):
        """Test that delays are capped at max_delay."""
        base_delay = 1.0
        exponential_base = 2.0
        max_delay = 10.0

        # For a high attempt number, delay should be capped
        attempt = 10  # Would normally give 1024 seconds
        delay = min(base_delay * (exponential_base**attempt), max_delay)
        assert delay == max_delay
