"""
Database utility functions for MagicGateway.

This module provides utility functions for working with database names and tables,
including functions to extract database names from table names and determine
if a cluster connection should be used based on the database name.
"""

from typing import Optional
from magic_gateway.core.config import settings


def get_database_from_table(table_name: str) -> str:
    """Extract database name from a fully qualified table name.

    Args:
        table_name: Fully qualified table name (e.g., "database.table")

    Returns:
        Database name or empty string if no database specified
    """
    if not table_name:
        return ""

    parts = table_name.split(".")
    if len(parts) > 1:
        return parts[0]
    return ""


def should_use_cluster_for_database(database_name: Optional[str]) -> bool:
    """Determine if a database should use the cluster connection pool.

    Args:
        database_name: Name of the database

    Returns:
        True if the database should use the cluster connection, False otherwise
    """
    # Handle None or empty database name
    if not database_name:
        return False

    # Currently only job_result database uses the cluster connection
    return database_name.lower() == "job_result"


def get_cluster_for_table(
    table_name: str, explicit_cluster: Optional[str] = None
) -> Optional[str]:
    """Determine which cluster to use for a given table.

    This function combines the database extraction and cluster determination logic,
    while also respecting any explicitly provided cluster parameter.

    Args:
        table_name: Fully qualified table name (e.g., "database.table")
        explicit_cluster: Optional explicit cluster name that overrides automatic detection

    Returns:
        Cluster name to use, or None if the primary connection should be used
    """
    # Explicit cluster parameter always takes precedence
    if explicit_cluster:
        return explicit_cluster

    # Extract database name and check if it should use cluster
    database_name = get_database_from_table(table_name)
    if should_use_cluster_for_database(database_name):
        # Return the configured cluster name from settings
        return settings.CLICKHOUSE_CLUSTER_NAME or "default_cluster"

    # Return None to indicate primary connection should be used
    return None
