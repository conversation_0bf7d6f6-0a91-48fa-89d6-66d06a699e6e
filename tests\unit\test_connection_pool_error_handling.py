"""
Unit tests for connection pool error handling integration.

Tests the enhanced error handling in ConnectionPoolAdapter including
connection pool status logging and error context integration.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from src.magic_gateway.export.adapters.connection_pool import ConnectionPoolAdapter
from src.magic_gateway.export.exceptions import (
    ExportError,
    ConnectionPoolExhaustedError,
    ConnectionTimeoutError,
    ConnectionPoolStatusError,
    ExportErrorContext
)
from src.magic_gateway.export.models import ConnectionSelection


class TestConnectionPoolStatusLogging:
    """Test connection pool status logging functionality."""
    
    def test_get_connection_pool_status_registry_not_initialized(self):
        """Test status when registry is not initialized."""
        mock_registry = Mock()
        mock_registry.initialized = False
        
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        status = adapter.get_connection_pool_status()
        
        assert status["registry_initialized"] is False
        assert status["available_clusters"] == []
        assert status["pool_details"] == {}
        assert "timestamp" in status
        
    def test_get_connection_pool_status_with_clusters(self):
        """Test status with available clusters."""
        mock_registry = Mock()
        mock_registry.initialized = True
        mock_registry.get_available_clusters.return_value = ["primary", "cluster1"]
        
        # Mock managers
        mock_primary = Mock()
        mock_primary.initialized = True
        mock_cluster1 = Mock()
        mock_cluster1.initialized = False
        
        mock_registry.get_manager.side_effect = lambda name: {
            "primary": mock_primary,
            "cluster1": mock_cluster1
        }[name]
        
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        status = adapter.get_connection_pool_status()
        
        assert status["registry_initialized"] is True
        assert "primary" in status["available_clusters"]
        assert "cluster1" in status["available_clusters"]
        assert status["pool_details"]["primary"]["initialized"] is True
        assert status["pool_details"]["primary"]["is_primary"] is True
        assert status["pool_details"]["cluster1"]["initialized"] is False
        assert status["pool_details"]["cluster1"]["is_primary"] is False
        
    def test_get_connection_pool_status_with_pool_info(self):
        """Test status with detailed pool information."""
        mock_registry = Mock()
        mock_registry.initialized = True
        mock_registry.get_available_clusters.return_value = ["primary"]
        
        # Mock manager with pool information
        mock_manager = Mock()
        mock_manager.initialized = True
        mock_pool = Mock()
        mock_pool.size = 10
        mock_pool.maxsize = 20
        mock_pool.freesize = 5
        mock_manager.pool = mock_pool
        
        mock_registry.get_manager.return_value = mock_manager
        
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        status = adapter.get_connection_pool_status()
        
        pool_details = status["pool_details"]["primary"]
        assert pool_details["pool_size"] == 10
        assert pool_details["pool_maxsize"] == 20
        assert pool_details["pool_freesize"] == 5
        
    def test_get_connection_pool_status_with_errors(self):
        """Test status when manager retrieval fails."""
        mock_registry = Mock()
        mock_registry.initialized = True
        mock_registry.get_available_clusters.return_value = ["primary"]
        mock_registry.get_manager.side_effect = Exception("Connection failed")
        
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        status = adapter.get_connection_pool_status()
        
        assert "primary" in status["pool_details"]
        assert "error" in status["pool_details"]["primary"]
        assert status["pool_details"]["primary"]["status"] == "error"


class TestConnectionWithStatusLogging:
    """Test connection selection with enhanced status logging."""
    
    @pytest.mark.asyncio
    async def test_get_connection_with_status_logging_success(self):
        """Test successful connection with status logging."""
        mock_registry = Mock()
        mock_registry.initialized = True
        
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        
        # Mock the get_optimal_connection method
        mock_connection_selection = ConnectionSelection(
            manager=Mock(),
            cluster_name="primary",
            is_fallback=False,
            routing_reason="Primary connection selected"
        )
        
        with patch.object(adapter, 'get_optimal_connection', new_callable=AsyncMock, return_value=mock_connection_selection):
            with patch.object(adapter, 'get_connection_pool_status', return_value={
                "registry_initialized": True, 
                "available_clusters": ["primary"]
            }):
                result = await adapter.get_connection_with_status_logging(
                    table_name="test.table",
                    request_id="req-123",
                    operation="test_operation"
                )
                
                assert result == mock_connection_selection
                
    @pytest.mark.asyncio
    async def test_get_connection_with_status_logging_error_enhancement(self):
        """Test that errors are enhanced with pool status."""
        mock_registry = Mock()
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        
        original_error = ExportError(
            error_type="test_error",
            message="Test error",
            context={},
            request_id="req-123",
            recovery_suggestions=[]
        )
        
        pool_status = {"registry_initialized": True, "available_clusters": ["primary"]}
        
        with patch.object(adapter, 'get_optimal_connection', new_callable=AsyncMock, side_effect=original_error):
            with patch.object(adapter, 'get_connection_pool_status', return_value=pool_status):
                with pytest.raises(ExportError) as exc_info:
                    await adapter.get_connection_with_status_logging(
                        table_name="test.table",
                        request_id="req-123",
                        operation="test_operation"
                    )
                
                error = exc_info.value
                # The error should be enhanced, not wrapped
                assert error.connection_pool_status == pool_status
                assert error.operation == "test_operation"
                # The error type should be preserved from the original error
                assert error.error_type == "test_error"


class TestStreamingWithFallbackErrorHandling:
    """Test streaming with enhanced error handling and fallback."""
    
    @pytest.mark.asyncio
    async def test_streaming_with_fallback_success(self):
        """Test successful streaming with fallback mechanism."""
        mock_registry = Mock()
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        
        mock_connection_selection = ConnectionSelection(
            manager=Mock(),
            cluster_name="primary",
            is_fallback=False,
            routing_reason="Primary connection"
        )
        
        async def mock_stream():
            yield b"chunk1"
            yield b"chunk2"
        
        with patch.object(adapter, 'get_connection_with_status_logging', new_callable=AsyncMock, return_value=mock_connection_selection):
            with patch.object(adapter, 'export_to_parquet_stream', return_value=mock_stream()):
                chunks = []
                async for chunk in adapter.export_to_parquet_stream_with_fallback(
                    query="SELECT * FROM test",
                    table_name="test.table",
                    query_id="query-123",
                    request_id="req-123"
                ):
                    chunks.append(chunk)
                
                assert chunks == [b"chunk1", b"chunk2"]
                
    @pytest.mark.asyncio
    async def test_streaming_with_fallback_connection_errors(self):
        """Test streaming fallback on connection errors."""
        mock_registry = Mock()
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        
        mock_connection_selection = ConnectionSelection(
            manager=Mock(),
            cluster_name="primary",
            is_fallback=False,
            routing_reason="Primary connection"
        )
        
        # First attempt fails, second succeeds
        call_count = 0
        async def mock_stream_with_failure(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ConnectionPoolExhaustedError(
                    request_id="req-123",
                    pool_name="primary",
                    active_connections=10,
                    max_connections=10
                )
            else:
                yield b"success_chunk"
        
        with patch.object(adapter, 'get_connection_with_status_logging', new_callable=AsyncMock, return_value=mock_connection_selection):
            with patch.object(adapter, 'export_to_parquet_stream', side_effect=mock_stream_with_failure):
                with patch.object(adapter, 'get_connection_pool_status', return_value={"test": "status"}):
                    chunks = []
                    async for chunk in adapter.export_to_parquet_stream_with_fallback(
                        query="SELECT * FROM test",
                        table_name="test.table",
                        query_id="query-123",
                        request_id="req-123",
                        max_retries=1
                    ):
                        chunks.append(chunk)
                    
                    assert chunks == [b"success_chunk"]
                    assert call_count == 2  # First failed, second succeeded
                    
    @pytest.mark.asyncio
    async def test_streaming_with_fallback_all_attempts_fail(self):
        """Test streaming when all fallback attempts fail."""
        mock_registry = Mock()
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        
        mock_connection_selection = ConnectionSelection(
            manager=Mock(),
            cluster_name="primary",
            is_fallback=False,
            routing_reason="Primary connection"
        )
        
        async def mock_failing_stream(*args, **kwargs):
            raise ConnectionTimeoutError(
                request_id="req-123",
                timeout_seconds=30.0,
                operation="test"
            )
            yield  # This will never be reached
        
        with patch.object(adapter, 'get_connection_with_status_logging', new_callable=AsyncMock, return_value=mock_connection_selection):
            with patch.object(adapter, 'export_to_parquet_stream', side_effect=mock_failing_stream):
                with patch.object(adapter, 'get_connection_pool_status', return_value={"test": "status"}):
                    with pytest.raises(ExportError) as exc_info:
                        chunks = []
                        async for chunk in adapter.export_to_parquet_stream_with_fallback(
                            query="SELECT * FROM test",
                            table_name="test.table",
                            query_id="query-123",
                            request_id="req-123",
                            max_retries=1
                        ):
                            chunks.append(chunk)
                    
                    error = exc_info.value
                    assert error.error_type == "streaming_failed_all_connections"
                    assert "final_pool_status" in error.context
                    assert error.connection_pool_status is not None
                    
    @pytest.mark.asyncio
    async def test_streaming_with_fallback_export_error_no_retry(self):
        """Test that ExportError exceptions are not retried."""
        mock_registry = Mock()
        adapter = ConnectionPoolAdapter(registry=mock_registry)
        
        mock_connection_selection = ConnectionSelection(
            manager=Mock(),
            cluster_name="primary",
            is_fallback=False,
            routing_reason="Primary connection"
        )
        
        export_error = ExportError(
            error_type="table_not_found",
            message="Table not found",
            context={},
            request_id="req-123",
            recovery_suggestions=[]
        )
        
        async def mock_failing_stream(*args, **kwargs):
            raise export_error
            yield  # This will never be reached
        
        with patch.object(adapter, 'get_connection_with_status_logging', new_callable=AsyncMock, return_value=mock_connection_selection):
            with patch.object(adapter, 'export_to_parquet_stream', side_effect=mock_failing_stream):
                with pytest.raises(ExportError) as exc_info:
                    chunks = []
                    async for chunk in adapter.export_to_parquet_stream_with_fallback(
                        query="SELECT * FROM test",
                        table_name="test.table",
                        query_id="query-123",
                        request_id="req-123",
                        max_retries=2
                    ):
                        chunks.append(chunk)
                
                # Should be the same error, not wrapped
                assert exc_info.value == export_error


class TestErrorContextIntegration:
    """Test integration with ExportErrorContext."""
    
    @pytest.mark.asyncio
    async def test_error_context_captures_connection_pool_status(self):
        """Test that error context captures connection pool status."""
        mock_adapter = Mock()
        mock_adapter.get_connection_pool_status.return_value = {
            "registry_initialized": True,
            "available_clusters": ["primary"]
        }
        
        with pytest.raises(ExportError) as exc_info:
            with ExportErrorContext("req-123", "test_operation", connection_pool_adapter=mock_adapter):
                raise ValueError("Test error")
        
        error = exc_info.value
        assert error.connection_pool_status is not None
        assert error.connection_pool_status["registry_initialized"] is True
        
    @pytest.mark.asyncio
    async def test_error_context_handles_adapter_errors(self):
        """Test error context when adapter status retrieval fails."""
        mock_adapter = Mock()
        mock_adapter.get_connection_pool_status.side_effect = Exception("Status error")
        
        with pytest.raises(ExportError) as exc_info:
            with ExportErrorContext("req-123", "test_operation", connection_pool_adapter=mock_adapter):
                raise ValueError("Test error")
        
        error = exc_info.value
        # Should still work even if status retrieval fails
        assert error.error_type == "unexpected_error"
        assert error.operation == "test_operation"