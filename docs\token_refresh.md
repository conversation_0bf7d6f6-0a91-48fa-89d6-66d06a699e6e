# Token Refresh Functionality

MagicGateway now includes automatic token refresh functionality to prevent users from being logged out during long-running script executions.

## Overview

JWT tokens in MagicGateway have a limited lifespan (30 minutes for access tokens). Previously, if a script execution took longer than the token expiry time, users would be logged out and need to re-authenticate. The new token refresh functionality automatically handles token renewal during script execution.

## How It Works

### Server-Side Implementation

1. **Enhanced Authentication Dependency**: A new `get_current_active_user_with_refresh` dependency automatically checks token expiration and refreshes tokens when needed.

2. **Token Refresh Middleware**: The `TokenRefreshMiddleware` adds refreshed tokens to response headers when tokens are renewed.

3. **Proactive Refresh**: Tokens are refreshed proactively when they have less than 5 minutes remaining, preventing expiration during execution.

### Client-Side Implementation

1. **Refresh Token Headers**: The client automatically includes refresh tokens in request headers (`X-Refresh-Token`).

2. **Response Header Processing**: The client checks for new tokens in response headers (`X-New-Access-Token`, `X-New-Refresh-Token`) and updates its stored tokens.

3. **Seamless Integration**: Token refresh happens transparently without interrupting the user's workflow.

## Usage

### Server-Side Endpoints

Scripts endpoints now use the enhanced authentication dependency:

```python
from magic_gateway.auth.dependencies import get_current_active_user_with_refresh

@router.post("/run/{script_name}")
async def run_script(
    current_user: Dict[str, Any] = Depends(get_current_active_user_with_refresh),
    # ... other parameters
):
    # Script execution logic
    pass
```

### Client-Side Usage

No changes are required in client code. The token refresh happens automatically:

```python
from magic_gateway_client import MagicGatewayClient

client = MagicGatewayClient(
    username="your_username",
    password="your_password"
)

# Long-running script execution - tokens will be refreshed automatically
result = client.scripts.run_script(
    script_name="assortment_optimizer",
    parameters={
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        # ... other parameters
    },
    timeout_seconds=1800  # 30 minutes
)
```

## Middleware Organization

The token refresh functionality is implemented using a clean middleware architecture:

```
├── src/magic_gateway/api/middleware/
│   ├── __init__.py           # Package exports
│   ├── request_tracking.py   # Request tracking middleware
│   └── token_refresh.py      # Token refresh middleware
```

Import middleware components directly from their modules:

```python
from magic_gateway.api.middleware.token_refresh import TokenRefreshMiddleware
from magic_gateway.api.middleware.request_tracking import RequestTrackingMiddleware
```

Or use package-level imports:

```python
from magic_gateway.api.middleware import TokenRefreshMiddleware, RequestTrackingMiddleware
```

## Configuration

### Token Expiration Settings

Token expiration times are configured in the server settings:

```python
# Access token expires in 30 minutes
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Refresh token expires in 7 days
REFRESH_TOKEN_EXPIRE_DAYS = 7
```

### Refresh Threshold

Tokens are proactively refreshed when they have less than 5 minutes remaining. This threshold is configured in the authentication dependency:

```python
# Refresh tokens with less than 5 minutes remaining
if time_until_expiry < timedelta(minutes=5):
    # Refresh token logic
```

## Error Handling

### Server-Side Errors

- **Invalid Refresh Token**: If the refresh token is invalid or expired, the user receives a 401 Unauthorized response.
- **Token Refresh Failure**: If token refresh fails, the original token is used if still valid.
- **Web Interface Errors**: Browser requests receive user-friendly error messages with login redirect instructions.

### Client-Side Errors

- **Authentication Error**: If both access and refresh tokens are invalid, the client attempts to re-authenticate using username/password.
- **Connection Error**: Network issues during token refresh are handled with retry logic.

### Web Interface Support

The token refresh system now intelligently detects web browser requests and provides appropriate handling:

- **User-Friendly Messages**: Instead of technical JWT errors, web users see "Your session has expired. Please log in again."
- **Login Redirect**: Web interface receives `redirect_to_login: true` flag for automatic redirect handling.
- **Extended Warning**: Web users get 10-minute expiration warnings (vs 5 minutes for API clients).

## Security Considerations

1. **Refresh Token Security**: Refresh tokens are transmitted in headers and should only be used over HTTPS in production.

2. **Token Rotation**: Both access and refresh tokens are rotated on each refresh to prevent token reuse attacks.

3. **Expiration Validation**: All tokens are validated for expiration and signature before use.

## Monitoring and Logging

### Server-Side Logging

Token refresh events are logged for monitoring:

```
INFO - Token refreshed successfully for user: username
INFO - Token proactively refreshed for user: username
WARNING - Token refresh failed: error_message
```

### Client-Side Logging

The client logs token refresh events:

```
INFO - Received refreshed tokens from server
```

## Backward Compatibility

The token refresh functionality is fully backward compatible:

- Existing client code continues to work without changes
- Endpoints without refresh functionality still work normally
- Old client versions can still authenticate normally

## Testing

### Example Script

Use the provided example script to test token refresh functionality:

```bash
export MAGIC_GATEWAY_USERNAME='your_username'
export MAGIC_GATEWAY_PASSWORD='your_password'
python examples/token_refresh_example.py
```

### Manual Testing

1. Start a long-running script execution
2. Monitor server logs for token refresh events
3. Verify the script completes successfully without authentication errors

## Troubleshooting

### Common Issues

1. **Missing Refresh Token**: Ensure the client is properly authenticated and has a refresh token.

2. **Token Refresh Loops**: Check for clock synchronization issues between client and server.

3. **CORS Issues**: Ensure the server allows the `X-Refresh-Token` header in CORS configuration.

### Debug Information

Enable debug logging to see detailed token refresh information:

```python
import logging
logging.getLogger('magic_gateway_client').setLevel(logging.DEBUG)
```

## Future Enhancements

Planned improvements to the token refresh functionality:

1. **Configurable Refresh Threshold**: Allow customization of the 5-minute refresh threshold.
2. **Token Refresh Metrics**: Add metrics for monitoring token refresh frequency and success rates.
3. **Refresh Token Blacklisting**: Implement refresh token blacklisting for enhanced security.
