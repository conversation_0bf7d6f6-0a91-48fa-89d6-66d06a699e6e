-- <PERSON><PERSON><PERSON> to create the connection pool monitoring table in the logs database
-- Run this script as a user with appropriate permissions on the logs database

-- Create the api schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS api;

-- Create the connection_pool_monitoring table if it doesn't exist
CREATE TABLE IF NOT EXISTS api.connection_pool_monitoring (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    pool_type VARCHAR(50) NOT NULL, -- 'postgres', 'clickhouse', 'logs', 'clickhouse_cluster_name'
    active_connections INTEGER NOT NULL,
    idle_connections INTEGER NOT NULL,
    min_size INTEGER NOT NULL,
    max_size INTEGER NOT NULL,
    total_connections_created INTEGER NOT NULL DEFAULT 0,
    total_connections_closed INTEGER NOT NULL DEFAULT 0,
    connection_timeouts INTEGER NOT NULL DEFAULT 0,
    connection_errors INTEGER NOT NULL DEFAULT 0,
    avg_acquisition_time_ms FLOAT,
    max_acquisition_time_ms FLOAT,
    avg_usage_time_ms FLOAT,
    max_usage_time_ms FLOAT,
    additional_metrics JSONB
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_connection_pool_monitoring_timestamp ON api.connection_pool_monitoring(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_connection_pool_monitoring_pool_type ON api.connection_pool_monitoring(pool_type);

-- Grant permissions to the application user
GRANT USAGE ON SCHEMA api TO "msr.shinyproxy.svc";
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA api TO "msr.shinyproxy.svc";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA api TO "msr.shinyproxy.svc";

-- Create a view for the last hour of monitoring data
CREATE OR REPLACE VIEW api.connection_pool_monitoring_last_hour AS
SELECT * FROM api.connection_pool_monitoring
WHERE timestamp > (CURRENT_TIMESTAMP - INTERVAL '1 hour')
ORDER BY timestamp DESC;

-- Create a view for daily aggregated metrics
CREATE OR REPLACE VIEW api.connection_pool_monitoring_daily AS
SELECT 
    DATE_TRUNC('day', timestamp) AS day,
    pool_type,
    AVG(active_connections) AS avg_active_connections,
    MAX(active_connections) AS max_active_connections,
    AVG(idle_connections) AS avg_idle_connections,
    MAX(idle_connections) AS max_idle_connections,
    MAX(total_connections_created) AS total_connections_created,
    MAX(total_connections_closed) AS total_connections_closed,
    SUM(connection_timeouts) AS connection_timeouts,
    SUM(connection_errors) AS connection_errors,
    AVG(avg_acquisition_time_ms) AS avg_acquisition_time_ms,
    MAX(max_acquisition_time_ms) AS max_acquisition_time_ms,
    AVG(avg_usage_time_ms) AS avg_usage_time_ms,
    MAX(max_usage_time_ms) AS max_usage_time_ms
FROM api.connection_pool_monitoring
GROUP BY DATE_TRUNC('day', timestamp), pool_type
ORDER BY day DESC, pool_type;
