"""Unit tests for the ClickHouse connection registry."""

import async<PERSON>
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from magic_gateway.core.exceptions import ClickHouseException
from magic_gateway.db.connection_manager import (
    ClickHouseConnectionRegistry,
    ClickHouseConnectionManager,
)


@pytest.fixture
def mock_settings():
    """Create a mock settings object for testing."""
    settings = MagicMock()

    # Primary ClickHouse settings
    settings.CLICKHOUSE_HOST = "primary-host"
    settings.CLICKHOUSE_PORT = 9000
    settings.CLICKHOUSE_USER = "primary-user"
    settings.CLICKHOUSE_PASSWORD = "primary-password"
    settings.CLICKHOUSE_DATABASE = "primary-db"
    settings.CLICKHOUSE_MIN_CONNECTIONS = 3
    settings.CLICKHOUSE_MAX_CONNECTIONS = 10

    # Cluster ClickHouse settings
    settings.CLICKHOUSE_CLUSTER_HOST = "cluster-host"
    settings.CLICKHOUSE_CLUSTER_PORT = 5000
    settings.CLICKHOUSE_CLUSTER_USER = "cluster-user"
    settings.CLICKHOUSE_CLUSTER_PASSWORD = "cluster-password"
    settings.CLICKHOUSE_CLUSTER_DATABASE = "cluster-db"
    settings.CLICKHOUSE_CLUSTER_NAME = "default_cluster"
    settings.CLICKHOUSE_CLUSTER_MIN_CONNECTIONS = 3
    settings.CLICKHOUSE_CLUSTER_MAX_CONNECTIONS = 10

    # Property to check if cluster is configured
    settings.has_clickhouse_cluster = True

    return settings


@pytest.fixture
def mock_connection_manager():
    """Create a mock ClickHouseConnectionManager."""
    manager = AsyncMock(spec=ClickHouseConnectionManager)
    manager.initialize = AsyncMock()
    manager.close = AsyncMock()
    return manager


@pytest.mark.asyncio
async def test_registry_initialization_with_cluster(mock_settings):
    """Test registry initialization with both primary and cluster connections."""
    with patch(
        "magic_gateway.db.connection_manager.ClickHouseConnectionManager"
    ) as mock_manager_class:
        # Configure the mock manager class to return our mock instances
        primary_manager = AsyncMock()
        cluster_manager = AsyncMock()
        mock_manager_class.side_effect = [primary_manager, cluster_manager]

        # Create the registry
        registry = ClickHouseConnectionRegistry()
        registry._settings = mock_settings

        # Initialize the registry
        await registry.initialize()

        # Verify that two connection managers were created
        assert mock_manager_class.call_count == 2

        # Verify that both managers were initialized
        primary_manager.initialize.assert_called_once()
        cluster_manager.initialize.assert_called_once()

        # Verify that both managers were added to the registry
        assert len(registry._managers) == 2
        assert "primary" in registry._managers
        assert "default_cluster" in registry._managers

        # Verify that the registry is marked as initialized
        assert registry._initialized is True


@pytest.mark.asyncio
async def test_registry_initialization_without_cluster(mock_settings):
    """Test registry initialization with only primary connection."""
    # Update mock settings to disable cluster
    mock_settings.has_clickhouse_cluster = False

    with patch(
        "magic_gateway.db.connection_manager.ClickHouseConnectionManager"
    ) as mock_manager_class:
        # Configure the mock manager class to return our mock instance
        primary_manager = AsyncMock()
        mock_manager_class.return_value = primary_manager

        # Create the registry
        registry = ClickHouseConnectionRegistry()
        registry._settings = mock_settings

        # Initialize the registry
        await registry.initialize()

        # Verify that only one connection manager was created
        assert mock_manager_class.call_count == 1

        # Verify that the primary manager was initialized
        primary_manager.initialize.assert_called_once()

        # Verify that only the primary manager was added to the registry
        assert len(registry._managers) == 1
        assert "primary" in registry._managers

        # Verify that the registry is marked as initialized
        assert registry._initialized is True


@pytest.mark.asyncio
async def test_get_manager_primary(mock_settings):
    """Test getting the primary connection manager."""
    registry = ClickHouseConnectionRegistry()
    registry._settings = mock_settings
    registry._initialized = True

    # Add mock managers to the registry
    primary_manager = AsyncMock()
    cluster_manager = AsyncMock()
    registry._managers = {
        "primary": primary_manager,
        "default_cluster": cluster_manager,
    }

    # Get the primary manager (no cluster specified)
    manager = registry.get_manager()

    # Verify that the primary manager was returned
    assert manager == primary_manager


@pytest.mark.asyncio
async def test_get_manager_cluster(mock_settings):
    """Test getting a specific cluster connection manager."""
    registry = ClickHouseConnectionRegistry()
    registry._settings = mock_settings
    registry._initialized = True

    # Add mock managers to the registry
    primary_manager = AsyncMock()
    cluster_manager = AsyncMock()
    registry._managers = {
        "primary": primary_manager,
        "default_cluster": cluster_manager,
    }

    # Get the cluster manager
    manager = registry.get_manager("default_cluster")

    # Verify that the cluster manager was returned
    assert manager == cluster_manager


@pytest.mark.asyncio
async def test_get_manager_invalid_cluster(mock_settings):
    """Test getting an invalid cluster connection manager."""
    registry = ClickHouseConnectionRegistry()
    registry._settings = mock_settings
    registry._initialized = True

    # Add mock managers to the registry
    primary_manager = AsyncMock()
    cluster_manager = AsyncMock()
    registry._managers = {
        "primary": primary_manager,
        "default_cluster": cluster_manager,
    }

    # Try to get an invalid cluster manager
    with pytest.raises(ClickHouseException) as excinfo:
        registry.get_manager("invalid_cluster")

    # Verify that the correct exception was raised
    assert "Unknown ClickHouse cluster: invalid_cluster" in str(excinfo.value)


@pytest.mark.asyncio
async def test_get_available_clusters(mock_settings):
    """Test getting the list of available clusters."""
    registry = ClickHouseConnectionRegistry()
    registry._settings = mock_settings
    registry._initialized = True

    # Add mock managers to the registry
    primary_manager = AsyncMock()
    cluster_manager = AsyncMock()
    registry._managers = {
        "primary": primary_manager,
        "default_cluster": cluster_manager,
    }

    # Get the available clusters
    clusters = registry.get_available_clusters()

    # Verify that both clusters are in the list
    assert len(clusters) == 2
    assert "primary" in clusters
    assert "default_cluster" in clusters


@pytest.mark.asyncio
async def test_close(mock_settings):
    """Test closing all connection managers."""
    registry = ClickHouseConnectionRegistry()
    registry._settings = mock_settings
    registry._initialized = True

    # Add mock managers to the registry
    primary_manager = AsyncMock()
    cluster_manager = AsyncMock()
    registry._managers = {
        "primary": primary_manager,
        "default_cluster": cluster_manager,
    }

    # Close the registry
    await registry.close()

    # Verify that both managers were closed
    primary_manager.close.assert_called_once()
    cluster_manager.close.assert_called_once()

    # Verify that the registry is marked as not initialized
    assert registry._initialized is False

    # Verify that the managers dictionary is empty
    assert len(registry._managers) == 0


@pytest.mark.asyncio
async def test_cluster_initialization_failure(mock_settings):
    """Test handling of cluster initialization failure."""
    with patch(
        "magic_gateway.db.connection_manager.ClickHouseConnectionManager"
    ) as mock_manager_class:
        # Configure the primary manager to succeed and the cluster manager to fail
        primary_manager = AsyncMock()

        def side_effect(*args, **kwargs):
            if args and args[0] == "cluster":
                raise ClickHouseException("Failed to initialize cluster connection")
            return primary_manager

        mock_manager_class.side_effect = side_effect

        # Create the registry
        registry = ClickHouseConnectionRegistry()
        registry._settings = mock_settings

        # Initialize the registry - should not raise an exception
        await registry.initialize()

        # Verify that the primary manager was initialized
        assert "primary" in registry._managers

        # Verify that the cluster manager was not added
        assert "default_cluster" not in registry._managers

        # Verify that the registry is still marked as initialized
        assert registry._initialized is True
