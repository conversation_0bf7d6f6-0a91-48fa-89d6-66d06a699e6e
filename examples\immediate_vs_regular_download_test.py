#!/usr/bin/env python
"""
Comparison test between immediate download and regular download approaches.

This script demonstrates the benefits of the immediate download approach by:
1. Testing the immediate download endpoint
2. Simulating a regular download approach (where the file is fully generated before download starts)
3. Comparing metrics like time to first byte, download progress, and user experience

The test uses job ID 12178 as an example.
"""

import os
import sys
import time
import logging
import requests
import tempfile
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from datetime import datetime
import asyncio
import aiohttp
import threading
import xlsxwriter
from concurrent.futures import ThreadPoolExecutor

# Add the parent directory to the path so we can import the api_client
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DownloadComparisonTest:
    """Compare immediate download with regular download approaches."""

    def __init__(self, base_url="http://localhost:8000"):
        """Initialize the tester."""
        self.base_url = base_url
        # Create output directory if it doesn't exist
        self.output_dir = Path("export_output")
        self.output_dir.mkdir(exist_ok=True)
        
        # For storing download metrics
        self.download_metrics = {}

    def test_immediate_download(self, job_id, export_format="excel"):
        """
        Test the immediate download endpoint.
        
        Args:
            job_id: ID of the job to export
            export_format: Format to export (excel, excel_facts, csv)
            
        Returns:
            Path to the downloaded file
        """
        # Construct the export URL
        export_url = f"{self.base_url}/api/v1/scripts/export/job/{job_id}"
        
        # Add format parameter
        params = {"format": export_format}
        
        # Generate a unique filename for this test
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        extension = ".xlsx" if export_format.startswith("excel") else ".csv"
        filename = f"immediate_job_{job_id}_{export_format}_{timestamp}{extension}"
        output_path = self.output_dir / filename
        
        # Metrics to track
        metrics = {
            "job_id": job_id,
            "format": export_format,
            "approach": "immediate",
            "start_time": time.time(),
            "first_byte_time": None,
            "end_time": None,
            "total_size": 0,
            "progress": [],  # List of (time, bytes) tuples
        }
        
        try:
            logger.info(f"Testing IMMEDIATE download for job {job_id} in {export_format} format...")
            
            # Make the request with streaming
            response = requests.get(export_url, params=params, stream=True)
            response.raise_for_status()
            
            with open(output_path, "wb") as f:
                for i, chunk in enumerate(response.iter_content(chunk_size=8192)):
                    if chunk:
                        # Record time of first byte
                        current_time = time.time()
                        if i == 0:
                            metrics["first_byte_time"] = current_time
                            ttfb = current_time - metrics["start_time"]
                            logger.info(f"IMMEDIATE: Time to first byte: {ttfb:.3f} seconds")
                        
                        # Write the chunk
                        f.write(chunk)
                        
                        # Update metrics
                        metrics["total_size"] += len(chunk)
                        elapsed = current_time - metrics["start_time"]
                        metrics["progress"].append((elapsed, metrics["total_size"]))
                        
                        # Log progress periodically (every 10 chunks or ~80KB)
                        if i % 10 == 0:
                            speed = metrics["total_size"] / elapsed / 1024 if elapsed > 0 else 0
                            logger.info(
                                f"IMMEDIATE: Downloaded {metrics['total_size']/1024:.1f} KB "
                                f"in {elapsed:.2f}s ({speed:.1f} KB/s)"
                            )
            
            # Record end time and calculate stats
            metrics["end_time"] = time.time()
            total_time = metrics["end_time"] - metrics["start_time"]
            ttfb = metrics["first_byte_time"] - metrics["start_time"]
            
            logger.info(f"IMMEDIATE download complete! Stats:")
            logger.info(f"  - Total size: {metrics['total_size']/1024:.1f} KB")
            logger.info(f"  - Total time: {total_time:.2f} seconds")
            logger.info(f"  - Time to first byte: {ttfb:.3f} seconds")
            logger.info(f"  - Average speed: {metrics['total_size']/total_time/1024:.1f} KB/s")
            
            # Store metrics for later analysis
            self.download_metrics[f"immediate_{export_format}"] = metrics
            
            return output_path
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error during immediate download test: {e}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Response status code: {e.response.status_code}")
                logger.error(f"Response text: {e.response.text}")
            return None

    def simulate_regular_download(self, job_id, export_format="excel"):
        """
        Simulate a regular download approach (where the file is fully generated before download starts).
        
        This simulates the traditional approach where:
        1. Client requests the file
        2. Server generates the entire file
        3. Only after the file is complete, the download starts
        
        Args:
            job_id: ID of the job to export
            export_format: Format to export (excel, excel_facts, csv)
            
        Returns:
            Path to the downloaded file
        """
        # Construct the export URL
        export_url = f"{self.base_url}/api/v1/scripts/export/job/{job_id}"
        
        # Add format parameter
        params = {"format": export_format}
        
        # Generate a unique filename for this test
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        extension = ".xlsx" if export_format.startswith("excel") else ".csv"
        filename = f"regular_job_{job_id}_{export_format}_{timestamp}{extension}"
        output_path = self.output_dir / filename
        
        # Metrics to track
        metrics = {
            "job_id": job_id,
            "format": export_format,
            "approach": "regular",
            "start_time": time.time(),
            "first_byte_time": None,
            "end_time": None,
            "total_size": 0,
            "progress": [],  # List of (time, bytes) tuples
        }
        
        try:
            logger.info(f"Testing REGULAR download for job {job_id} in {export_format} format...")
            
            # First, get the data but don't start downloading yet
            # We'll use a non-streaming request to simulate the server generating the entire file
            logger.info("REGULAR: Requesting data (server generating file)...")
            
            # Make the request without streaming to get the full response
            response = requests.get(export_url, params=params, stream=False)
            response.raise_for_status()
            
            # Now we have the full response, record the time when the "download" would start
            # This simulates the time when the server would start sending the file
            metrics["first_byte_time"] = time.time()
            ttfb = metrics["first_byte_time"] - metrics["start_time"]
            logger.info(f"REGULAR: Time to first byte: {ttfb:.3f} seconds")
            
            # Now simulate the download by writing the content to a file
            content = response.content
            metrics["total_size"] = len(content)
            
            # Simulate downloading in chunks to track progress
            chunk_size = 8192
            with open(output_path, "wb") as f:
                for i in range(0, len(content), chunk_size):
                    chunk = content[i:i+chunk_size]
                    f.write(chunk)
                    
                    # Update metrics
                    current_time = time.time()
                    elapsed = current_time - metrics["start_time"]
                    metrics["progress"].append((elapsed, i + len(chunk)))
                    
                    # Log progress periodically
                    if i % (10 * chunk_size) == 0:
                        speed = (i + len(chunk)) / (current_time - metrics["first_byte_time"]) / 1024 if current_time > metrics["first_byte_time"] else 0
                        logger.info(
                            f"REGULAR: Downloaded {(i + len(chunk))/1024:.1f} KB "
                            f"in {current_time - metrics['first_byte_time']:.2f}s ({speed:.1f} KB/s)"
                        )
            
            # Record end time and calculate stats
            metrics["end_time"] = time.time()
            total_time = metrics["end_time"] - metrics["start_time"]
            download_time = metrics["end_time"] - metrics["first_byte_time"]
            
            logger.info(f"REGULAR download complete! Stats:")
            logger.info(f"  - Total size: {metrics['total_size']/1024:.1f} KB")
            logger.info(f"  - Total time (including generation): {total_time:.2f} seconds")
            logger.info(f"  - Time to first byte (generation time): {ttfb:.3f} seconds")
            logger.info(f"  - Download time (after generation): {download_time:.2f} seconds")
            logger.info(f"  - Download speed: {metrics['total_size']/download_time/1024:.1f} KB/s")
            
            # Store metrics for later analysis
            self.download_metrics[f"regular_{export_format}"] = metrics
            
            return output_path
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error during regular download test: {e}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Response status code: {e.response.status_code}")
                logger.error(f"Response text: {e.response.text}")
            return None

    def plot_download_comparison(self, output_path=None):
        """
        Plot a comparison of the download approaches.
        
        Args:
            output_path: Path to save the plot, if None, the plot is displayed
        """
        if not self.download_metrics:
            logger.warning("No download metrics to plot")
            return
        
        plt.figure(figsize=(12, 8))
        
        # Plot download progress
        plt.subplot(2, 1, 1)
        
        for test_name, metrics in self.download_metrics.items():
            # Extract progress data
            times = [p[0] for p in metrics["progress"]]
            sizes = [p[1] / 1024 for p in metrics["progress"]]  # Convert to KB
            
            # Plot progress
            label = f"{metrics['approach'].capitalize()} {metrics['format']} (TTFB: {metrics['first_byte_time'] - metrics['start_time']:.2f}s)"
            plt.plot(times, sizes, label=label)
            
            # Mark first byte
            if metrics["first_byte_time"]:
                ttfb = metrics["first_byte_time"] - metrics["start_time"]
                plt.axvline(x=ttfb, color='gray', linestyle='--', alpha=0.5)
        
        plt.title("Download Progress Comparison")
        plt.xlabel("Time (seconds)")
        plt.ylabel("Downloaded Size (KB)")
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Plot time to first byte comparison
        plt.subplot(2, 1, 2)
        
        approaches = []
        ttfb_values = []
        total_times = []
        formats = set()
        
        for test_name, metrics in self.download_metrics.items():
            approach = metrics["approach"]
            format_name = metrics["format"]
            formats.add(format_name)
            
            approaches.append(f"{approach.capitalize()} {format_name}")
            ttfb = metrics["first_byte_time"] - metrics["start_time"]
            ttfb_values.append(ttfb)
            
            total_time = metrics["end_time"] - metrics["start_time"]
            total_times.append(total_time)
        
        # Create bar positions
        x = np.arange(len(approaches))
        width = 0.35
        
        # Plot TTFB bars
        plt.bar(x - width/2, ttfb_values, width, label='Time to First Byte')
        
        # Plot Total Time bars
        plt.bar(x + width/2, total_times, width, label='Total Time')
        
        plt.title("Time Comparison")
        plt.xlabel("Approach")
        plt.ylabel("Time (seconds)")
        plt.xticks(x, approaches, rotation=45)
        plt.grid(True, alpha=0.3, axis='y')
        plt.legend()
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path)
            logger.info(f"Comparison plot saved to {output_path}")
        else:
            plt.show()


def main():
    """Run the comparison tests."""
    # Create the tester
    tester = DownloadComparisonTest()
    
    # Job ID to test
    job_id = 12178
    
    # Test Excel format with both approaches
    logger.info("\n=== Testing Excel format ===")
    immediate_excel_path = tester.test_immediate_download(job_id, export_format="excel")
    regular_excel_path = tester.simulate_regular_download(job_id, export_format="excel")
    
    # Test Excel with horizontal facts format with both approaches
    logger.info("\n=== Testing Excel with horizontal facts format ===")
    immediate_facts_path = tester.test_immediate_download(job_id, export_format="excel_facts")
    regular_facts_path = tester.simulate_regular_download(job_id, export_format="excel_facts")
    
    # Plot the comparison
    plot_path = tester.output_dir / f"download_comparison_job_{job_id}.png"
    tester.plot_download_comparison(output_path=plot_path)
    
    logger.info("\n=== Test Summary ===")
    logger.info(f"Immediate Excel file: {immediate_excel_path}")
    logger.info(f"Regular Excel file: {regular_excel_path}")
    logger.info(f"Immediate Excel Facts file: {immediate_facts_path}")
    logger.info(f"Regular Excel Facts file: {regular_facts_path}")
    logger.info(f"Comparison plot: {plot_path}")
    
    # Calculate and display improvement metrics
    for format_name in ["excel", "excel_facts"]:
        immediate_metrics = tester.download_metrics.get(f"immediate_{format_name}")
        regular_metrics = tester.download_metrics.get(f"regular_{format_name}")
        
        if immediate_metrics and regular_metrics:
            immediate_ttfb = immediate_metrics["first_byte_time"] - immediate_metrics["start_time"]
            regular_ttfb = regular_metrics["first_byte_time"] - regular_metrics["start_time"]
            
            ttfb_improvement = (regular_ttfb - immediate_ttfb) / regular_ttfb * 100
            
            immediate_total = immediate_metrics["end_time"] - immediate_metrics["start_time"]
            regular_total = regular_metrics["end_time"] - regular_metrics["start_time"]
            
            total_improvement = (regular_total - immediate_total) / regular_total * 100
            
            logger.info(f"\n{format_name.upper()} Format Improvements:")
            logger.info(f"  - Time to First Byte: {ttfb_improvement:.1f}% faster with immediate download")
            logger.info(f"  - Total Time: {total_improvement:.1f}% faster with immediate download")


if __name__ == "__main__":
    main()
