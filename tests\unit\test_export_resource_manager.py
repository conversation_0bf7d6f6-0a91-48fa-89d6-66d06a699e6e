"""
Unit tests for ExportResourceManager.

Tests the resource lifecycle management functionality including
temporary file handling, stream cleanup, and async context management.
"""

import asyncio
import tempfile
import uuid
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
import pytest

from magic_gateway.export.resources.manager import ExportResourceManager, export_resource_context
from magic_gateway.utils.temp_file_manager import TempFileManager


class TestExportResourceManager:
    """Test cases for ExportResourceManager."""

    @pytest.fixture
    def mock_temp_file_manager(self):
        """Create a mock TempFileManager for testing."""
        mock_tfm = MagicMock(spec=TempFileManager)
        mock_tfm.save_result_file.return_value = "test_file_id"
        mock_tfm.get_file_path.return_value = Path("/tmp/test_file.csv")
        mock_tfm.cleanup_file.return_value = True
        return mock_tfm

    @pytest.fixture
    def resource_manager(self, mock_temp_file_manager):
        """Create an ExportResourceManager instance for testing."""
        return ExportResourceManager(temp_file_manager=mock_temp_file_manager)

    def test_initialization(self, mock_temp_file_manager):
        """Test ExportResourceManager initialization."""
        manager = ExportResourceManager(temp_file_manager=mock_temp_file_manager)
        
        assert manager.temp_file_manager is mock_temp_file_manager
        assert manager.request_id is not None
        assert len(manager.request_id) > 0
        assert manager.resource_count == 0
        assert not manager.is_cleaned_up

    def test_initialization_with_global_temp_file_manager(self):
        """Test initialization with global temp file manager."""
        with patch('magic_gateway.utils.temp_file_manager.temp_file_manager') as mock_global:
            manager = ExportResourceManager()
            assert manager.temp_file_manager is mock_global

    def test_register_temp_file_with_existing_file(self, resource_manager):
        """Test registering an existing temporary file."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            
        try:
            resource_id = resource_manager.register_temp_file(tmp_path)
            
            assert resource_id is not None
            assert len(resource_id) > 0
            assert resource_manager.resource_count == 1
            assert resource_manager.get_temp_file_path(resource_id) == tmp_path
        finally:
            tmp_path.unlink(missing_ok=True)

    def test_register_temp_file_with_custom_resource_id(self, resource_manager):
        """Test registering a temp file with custom resource ID."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            
        try:
            custom_id = "custom_resource_id"
            resource_id = resource_manager.register_temp_file(tmp_path, resource_id=custom_id)
            
            assert resource_id == custom_id
            assert resource_manager.get_temp_file_path(custom_id) == tmp_path
        finally:
            tmp_path.unlink(missing_ok=True)

    def test_register_temp_file_nonexistent_file(self, resource_manager):
        """Test registering a non-existent file raises ValueError."""
        nonexistent_path = Path("/nonexistent/file.txt")
        
        with pytest.raises(ValueError, match="File does not exist"):
            resource_manager.register_temp_file(nonexistent_path)

    def test_register_temp_file_duplicate_resource_id(self, resource_manager):
        """Test registering duplicate resource ID raises ValueError."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file1:
            tmp_path1 = Path(tmp_file1.name)
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file2:
            tmp_path2 = Path(tmp_file2.name)
            
        try:
            resource_id = "duplicate_id"
            resource_manager.register_temp_file(tmp_path1, resource_id=resource_id)
            
            with pytest.raises(ValueError, match="already registered"):
                resource_manager.register_temp_file(tmp_path2, resource_id=resource_id)
        finally:
            tmp_path1.unlink(missing_ok=True)
            tmp_path2.unlink(missing_ok=True)

    def test_register_stream(self, resource_manager):
        """Test registering a stream resource."""
        mock_stream = MagicMock()
        
        resource_id = resource_manager.register_stream(mock_stream)
        
        assert resource_id is not None
        assert resource_manager.resource_count == 1
        assert resource_manager.get_stream(resource_id) is mock_stream

    def test_register_stream_with_cleanup_callback(self, resource_manager):
        """Test registering a stream with custom cleanup callback."""
        mock_stream = MagicMock()
        mock_cleanup = MagicMock()
        
        resource_id = resource_manager.register_stream(
            mock_stream, 
            cleanup_callback=mock_cleanup
        )
        
        assert resource_id is not None
        assert resource_manager.get_stream(resource_id) is mock_stream

    def test_register_stream_duplicate_resource_id(self, resource_manager):
        """Test registering duplicate stream resource ID raises ValueError."""
        mock_stream1 = MagicMock()
        mock_stream2 = MagicMock()
        
        resource_id = "duplicate_stream_id"
        resource_manager.register_stream(mock_stream1, resource_id=resource_id)
        
        with pytest.raises(ValueError, match="already registered"):
            resource_manager.register_stream(mock_stream2, resource_id=resource_id)

    def test_register_temp_file_from_manager(self, resource_manager, mock_temp_file_manager):
        """Test creating and registering a temp file via TempFileManager."""
        test_content = "test content"
        test_extension = "csv"
        test_prefix = "export_"
        
        resource_id = resource_manager.register_temp_file_from_manager(
            content=test_content,
            extension=test_extension,
            prefix=test_prefix
        )
        
        assert resource_id is not None
        assert resource_manager.resource_count == 1
        
        # Verify TempFileManager was called correctly
        mock_temp_file_manager.save_result_file.assert_called_once_with(
            content=test_content,
            extension=test_extension,
            prefix=test_prefix
        )
        mock_temp_file_manager.get_file_path.assert_called_once_with("test_file_id")

    def test_register_temp_file_from_manager_failure(self, resource_manager, mock_temp_file_manager):
        """Test handling of TempFileManager failure."""
        mock_temp_file_manager.save_result_file.side_effect = OSError("Disk full")
        
        with pytest.raises(OSError, match="Disk full"):
            resource_manager.register_temp_file_from_manager(content="test")

    def test_register_temp_file_from_manager_no_path(self, resource_manager, mock_temp_file_manager):
        """Test handling when TempFileManager returns no path."""
        mock_temp_file_manager.get_file_path.return_value = None
        
        with pytest.raises(OSError, match="Failed to get file path"):
            resource_manager.register_temp_file_from_manager(content="test")

    def test_get_temp_file_path_missing_file(self, resource_manager):
        """Test getting path for non-existent resource."""
        assert resource_manager.get_temp_file_path("nonexistent") is None

    def test_get_temp_file_path_file_deleted(self, resource_manager):
        """Test getting path when registered file was deleted."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            
        resource_id = resource_manager.register_temp_file(tmp_path)
        
        # Delete the file
        tmp_path.unlink()
        
        # Should return None since file no longer exists
        assert resource_manager.get_temp_file_path(resource_id) is None

    def test_get_stream_missing(self, resource_manager):
        """Test getting non-existent stream."""
        assert resource_manager.get_stream("nonexistent") is None

    def test_list_resources(self, resource_manager, mock_temp_file_manager):
        """Test listing all registered resources."""
        # Register a temp file
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            
        try:
            file_resource_id = resource_manager.register_temp_file(tmp_path)
            
            # Register a stream
            mock_stream = MagicMock()
            stream_resource_id = resource_manager.register_stream(mock_stream)
            
            # Register a temp file from manager
            tfm_resource_id = resource_manager.register_temp_file_from_manager(
                content="test"
            )
            
            resources = resource_manager.list_resources()
            
            assert len(resources) == 3
            
            # Check file resource
            assert resources[file_resource_id]["type"] == "temp_file"
            assert resources[file_resource_id]["exists"] is True
            
            # Check stream resource
            assert resources[stream_resource_id]["type"] == "stream"
            assert "stream_type" in resources[stream_resource_id]
            
            # Check TempFileManager resource
            assert resources[tfm_resource_id]["type"] == "temp_file"
            assert resources[tfm_resource_id]["file_id"] == "test_file_id"
            
        finally:
            tmp_path.unlink(missing_ok=True)

    @pytest.mark.asyncio
    async def test_cleanup_resource_temp_file(self, resource_manager, mock_temp_file_manager):
        """Test cleaning up a temporary file resource."""
        resource_id = resource_manager.register_temp_file_from_manager(content="test")
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is True
        mock_temp_file_manager.cleanup_file.assert_called_once_with("test_file_id")
        assert resource_manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_cleanup_resource_temp_file_failure(self, resource_manager, mock_temp_file_manager):
        """Test handling cleanup failure for temp file."""
        mock_temp_file_manager.cleanup_file.return_value = False
        resource_id = resource_manager.register_temp_file_from_manager(content="test")
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is False

    @pytest.mark.asyncio
    async def test_cleanup_resource_stream_with_aclose(self, resource_manager):
        """Test cleaning up a stream with async close method."""
        mock_stream = AsyncMock()
        resource_id = resource_manager.register_stream(mock_stream)
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is True
        mock_stream.aclose.assert_called_once()
        assert resource_manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_cleanup_resource_stream_with_close(self, resource_manager):
        """Test cleaning up a stream with sync close method."""
        mock_stream = MagicMock()
        mock_stream.aclose = None  # No async close
        resource_id = resource_manager.register_stream(mock_stream)
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is True
        mock_stream.close.assert_called_once()
        assert resource_manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_cleanup_resource_stream_with_async_callback(self, resource_manager):
        """Test cleaning up a stream with async cleanup callback."""
        mock_stream = MagicMock()
        mock_cleanup = AsyncMock()
        
        resource_id = resource_manager.register_stream(
            mock_stream, 
            cleanup_callback=mock_cleanup
        )
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is True
        mock_cleanup.assert_called_once_with(mock_stream)
        assert resource_manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_cleanup_resource_stream_with_sync_callback(self, resource_manager):
        """Test cleaning up a stream with sync cleanup callback."""
        mock_stream = MagicMock()
        mock_cleanup = MagicMock()
        
        resource_id = resource_manager.register_stream(
            mock_stream, 
            cleanup_callback=mock_cleanup
        )
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is True
        mock_cleanup.assert_called_once_with(mock_stream)
        assert resource_manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_cleanup_resource_stream_exception(self, resource_manager):
        """Test handling exceptions during stream cleanup."""
        mock_stream = MagicMock()
        mock_stream.close.side_effect = Exception("Cleanup failed")
        
        resource_id = resource_manager.register_stream(mock_stream)
        
        success = await resource_manager.cleanup_resource(resource_id)
        
        assert success is False
        assert resource_manager.resource_count == 0  # Resource still removed

    @pytest.mark.asyncio
    async def test_cleanup_all_resources(self, resource_manager, mock_temp_file_manager):
        """Test cleaning up all resources."""
        # Register multiple resources
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            
        try:
            resource_manager.register_temp_file(tmp_path)
            resource_manager.register_temp_file_from_manager(content="test")
            resource_manager.register_stream(MagicMock())
            
            assert resource_manager.resource_count == 3
            
            await resource_manager.cleanup_all()
            
            assert resource_manager.resource_count == 0
            assert resource_manager.is_cleaned_up is True
            
        finally:
            tmp_path.unlink(missing_ok=True)

    @pytest.mark.asyncio
    async def test_cleanup_all_idempotent(self, resource_manager):
        """Test that cleanup_all can be called multiple times safely."""
        mock_stream = MagicMock()
        resource_manager.register_stream(mock_stream)
        
        await resource_manager.cleanup_all()
        assert resource_manager.is_cleaned_up is True
        
        # Second call should be safe
        await resource_manager.cleanup_all()
        assert resource_manager.is_cleaned_up is True

    @pytest.mark.asyncio
    async def test_register_after_cleanup_raises_error(self, resource_manager):
        """Test that registering resources after cleanup raises ValueError."""
        await resource_manager.cleanup_all()
        
        with pytest.raises(ValueError, match="Cannot register resources after cleanup"):
            resource_manager.register_temp_file_from_manager(content="test")
            
        with pytest.raises(ValueError, match="Cannot register resources after cleanup"):
            resource_manager.register_stream(MagicMock())

    @pytest.mark.asyncio
    async def test_async_context_manager_normal_exit(self, mock_temp_file_manager):
        """Test async context manager with normal exit."""
        async with ExportResourceManager(mock_temp_file_manager) as manager:
            manager.register_temp_file_from_manager(content="test")
            assert manager.resource_count == 1
            assert not manager.is_cleaned_up
            
        assert manager.is_cleaned_up is True
        assert manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_async_context_manager_exception_exit(self, mock_temp_file_manager):
        """Test async context manager with exception exit."""
        manager = None
        
        with pytest.raises(ValueError, match="Test exception"):
            async with ExportResourceManager(mock_temp_file_manager) as mgr:
                manager = mgr
                manager.register_temp_file_from_manager(content="test")
                assert manager.resource_count == 1
                raise ValueError("Test exception")
                
        assert manager.is_cleaned_up is True
        assert manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_export_resource_context_factory(self, mock_temp_file_manager):
        """Test the export_resource_context factory function."""
        async with export_resource_context(mock_temp_file_manager) as manager:
            assert isinstance(manager, ExportResourceManager)
            assert manager.temp_file_manager is mock_temp_file_manager
            
            manager.register_temp_file_from_manager(content="test")
            assert manager.resource_count == 1
            
        assert manager.is_cleaned_up is True

    @pytest.mark.asyncio
    async def test_export_resource_context_default_temp_file_manager(self):
        """Test export_resource_context with default temp file manager."""
        with patch('magic_gateway.utils.temp_file_manager.temp_file_manager') as mock_global:
            async with export_resource_context() as manager:
                assert manager.temp_file_manager is mock_global