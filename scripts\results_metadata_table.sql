-- Results Metadata Table Schema
-- This table stores job-level metadata for KPI analysis results
-- One record per job_id (consolidating all periods)

CREATE TABLE IF NOT EXISTS metadata.results_metadata (
    -- Primary identifiers
    id String COMMENT 'Combined result ID for the entire job',
    job_id String COMMENT 'Original job ID from the system',
    final_result_table String COMMENT 'Name of the final result table',

    -- Job status tracking
    status Enum('in_progress' = 1, 'done' = 2, 'error' = 3) DEFAULT 'in_progress' COMMENT 'Current status of the job',
    
    -- Analysis information
    analysis_name String COMMENT 'Name of the KPI analysis',
    kpi_type String COMMENT 'Type of KPI (e.g., standard or catman)',
    id_panel UInt32 COMMENT 'Panel ID used for the analysis',
    
    -- Periods and Facts information 
    periods String COMMENT 'List of periods',
    facts String COMMENT 'List of facts (measures)',
    
    -- Query information
    query_ids String DEFAULT '' COMMENT 'List of all query IDs executed for this job',
    result_rows UInt64 COMMENT 'Total number of rows in the final result table',
    job_duration String COMMENT 'Total job duration in milliseconds',

    -- Timestamps
    created_at DateTime DEFAULT now() COMMENT 'When the job was created',
    completed_at DateTime COMMENT 'When the job was completed',
    
    -- Job metadata and configuration
    username String DEFAULT '' COMMENT 'Username who initiated the job',
    job_info String DEFAULT '{}' COMMENT 'JSON containing all job details, periods, axes, filters',
    backend_version String DEFAULT '' COMMENT 'Version of the kpi+ backend',
    error_info String DEFAULT '{}' COMMENT 'JSON containing error information if job failed',
    retention_days UInt16 DEFAULT 30 COMMENT 'Number of days to retain the result',
    
    -- Primary key
    PRIMARY KEY (id)
)
ENGINE = MergeTree()
TTL created_at + toIntervalDay(retention_days + 10)
ORDER BY (id, created_at)
SETTINGS index_granularity = 8192
COMMENT 'Metadata table for KPI analysis jobs';
