"""Configuration settings for the MagicGateway application."""

import logging
from typing import Dict, List, Optional, Union

from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Application settings
    APP_NAME: str = "MagicGateway"
    APP_VERSION: str = "0.5.5"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "INFO"

    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # Security settings
    SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # CORS settings
    BACKEND_CORS_ORIGINS: Union[List[AnyHttpUrl], List[str]] = ["*"]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from string to list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # LDAP settings
    # New LDAP settings format
    LDAP_SERVER: str = ""  # LDAP server address
    LDAP_DOMAIN: str = ""  # LDAP domain (NetBIOS name)
    LDAP_DOMAIN_FQDN: str = ""  # LDAP domain FQDN
    LDAP_BASE_DN: str = ""  # LDAP base DN

    # Legacy LDAP settings (for backward compatibility)
    LDAP_SERVER_URL: str = ""  # Will be constructed from LDAP_SERVER
    LDAP_BIND_DN: str = ""  # Not used in new authentication method
    LDAP_BIND_PASSWORD: str = ""  # Not used in new authentication method
    LDAP_USER_SEARCH_BASE: str = ""  # Will use LDAP_BASE_DN if not set
    LDAP_USER_SEARCH_FILTER: str = (
        "(sAMAccountName={username})"  # Default search filter
    )
    LDAP_ADMIN_GROUP_DN: str = ""  # Admin group DN

    # Test user credentials
    TEST_LDAP_USERNAME: str = ""  # Test LDAP username
    TEST_LDAP_PASSWORD: str = ""  # Test LDAP password

    # PostgreSQL settings
    POSTGRES_HOST: str
    POSTGRES_PORT: int
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_MIN_CONNECTIONS: int = 1
    POSTGRES_MAX_CONNECTIONS: int = 10
    POSTGRES_POOL_TIMEOUT: int = 30  # Timeout for acquiring a connection from the pool
    POSTGRES_MAX_IDLE_TIME: int = 600  # Maximum time a connection can be idle
    POSTGRES_MAX_LIFETIME: int = 3600  # Maximum lifetime of a connection
    POSTGRES_APPLICATION_NAME: str = (
        "magic_gateway"  # Application name for identification in PostgreSQL logs
    )

    @property
    def POSTGRES_DSN(self) -> str:
        """Construct PostgreSQL DSN from settings."""
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}?application_name={self.POSTGRES_APPLICATION_NAME}"

    # Logs Database settings (separate PostgreSQL connection for logs)
    LOGS_DB_HOST: str
    LOGS_DB_PORT: int
    LOGS_DB_USER: str
    LOGS_DB_PASSWORD: str
    LOGS_DB_NAME: str
    LOGS_DB_SCHEMA: str

    @property
    def LOGS_DB_DSN(self) -> str:
        """Construct Logs Database DSN from settings."""
        return f"postgresql://{self.LOGS_DB_USER}:{self.LOGS_DB_PASSWORD}@{self.LOGS_DB_HOST}:{self.LOGS_DB_PORT}/{self.LOGS_DB_NAME}?application_name={self.POSTGRES_APPLICATION_NAME}_logs"

    # ClickHouse settings
    CLICKHOUSE_HOST: str
    CLICKHOUSE_PORT: int  # Native client port (usually 9000)
    CLICKHOUSE_HTTP_PORT: int = 8123  # HTTP interface port (usually 8123)
    CLICKHOUSE_USER: str
    CLICKHOUSE_PASSWORD: str
    CLICKHOUSE_DATABASE: str
    CLICKHOUSE_MIN_CONNECTIONS: int = 3
    CLICKHOUSE_MAX_CONNECTIONS: int = 10
    CLICKHOUSE_CONNECT_TIMEOUT: int = 10  # Connection timeout in seconds
    CLICKHOUSE_PING_TIMEOUT: int = 5  # Timeout for liveness check in seconds
    CLICKHOUSE_POOL_WAIT_TIMEOUT: int = (
        30  # Timeout for waiting for a connection from the pool
    )
    CLICKHOUSE_SOCKET_TIMEOUT: int = 30  # Socket timeout for send/receive operations

    # ClickHouse Cluster settings (new cluster server)
    CLICKHOUSE_CLUSTER_HOST: Optional[str] = None
    CLICKHOUSE_CLUSTER_PORT: Optional[int] = None
    CLICKHOUSE_CLUSTER_HTTP_PORT: Optional[int] = None
    CLICKHOUSE_CLUSTER_USER: Optional[str] = None
    CLICKHOUSE_CLUSTER_PASSWORD: Optional[str] = None
    CLICKHOUSE_CLUSTER_DATABASE: Optional[str] = None
    CLICKHOUSE_CLUSTER_NAME: Optional[str] = None
    CLICKHOUSE_CLUSTER_MIN_CONNECTIONS: int = 3
    CLICKHOUSE_CLUSTER_MAX_CONNECTIONS: int = 10
    CLICKHOUSE_CLUSTER_CONNECT_TIMEOUT: int = 10
    CLICKHOUSE_CLUSTER_PING_TIMEOUT: int = 5
    CLICKHOUSE_CLUSTER_POOL_WAIT_TIMEOUT: int = 30
    CLICKHOUSE_CLUSTER_SOCKET_TIMEOUT: int = 30

    def __init__(self, **kwargs):
        """Initialize settings and cache cluster validation result."""
        super().__init__(**kwargs)
        self._cluster_validation_cached = False
        self._cluster_available = None

    @property
    def has_clickhouse_cluster(self) -> bool:
        """Check if cluster configuration is available (cached result)."""
        if self._cluster_validation_cached:
            return self._cluster_available

        logger = logging.getLogger(__name__)

        required_fields = {
            "CLICKHOUSE_CLUSTER_HOST": self.CLICKHOUSE_CLUSTER_HOST,
            "CLICKHOUSE_CLUSTER_PORT": self.CLICKHOUSE_CLUSTER_PORT,
            "CLICKHOUSE_CLUSTER_USER": self.CLICKHOUSE_CLUSTER_USER,
            "CLICKHOUSE_CLUSTER_PASSWORD": self.CLICKHOUSE_CLUSTER_PASSWORD,
            "CLICKHOUSE_CLUSTER_DATABASE": self.CLICKHOUSE_CLUSTER_DATABASE,
        }

        missing_fields = [
            field for field, value in required_fields.items() if not value
        ]

        if missing_fields:
            logger.info(
                f"ClickHouse cluster configuration incomplete. Missing fields: {', '.join(missing_fields)}. "
                "Cluster functionality will be disabled."
            )
            self._cluster_available = False
        else:
            cluster_name = self.CLICKHOUSE_CLUSTER_NAME or "default_cluster"
            logger.info(
                f"ClickHouse cluster configuration validated successfully. "
                f"Cluster '{cluster_name}' will be available at {self.CLICKHOUSE_CLUSTER_HOST}:{self.CLICKHOUSE_CLUSTER_PORT}"
            )
            self._cluster_available = True

        self._cluster_validation_cached = True
        return self._cluster_available

    # Query settings
    MAX_QUERY_EXECUTION_TIME: int = 300  # seconds
    TRACK_QUERIES: bool = True

    # Large dataset processing settings
    LARGE_DATASET_THRESHOLD_ROWS: int = (
        50_000_000  # Rows threshold for memory-efficient processing
    )
    LARGE_DATASET_MAX_MEMORY_MB: int = (
        8000  # Memory limit before switching to efficient mode
    )

    # Unified chunk size settings for ClickHouse streaming and parquet processing
    # These settings ensure consistent chunk sizes across the entire pipeline
    CHUNK_SIZE_DEFAULT_ROWS: int = 100_000  # Default chunk size in rows (100K)
    CHUNK_SIZE_LARGE_ROWS: int = 1_000_000  # Large chunk size in rows (1M)
    CHUNK_SIZE_HUGE_ROWS: int = 10_000_000  # Huge chunk size in rows (10M)

    # ClickHouse streaming chunk sizes (in bytes)
    CHUNK_SIZE_DEFAULT_BYTES: int = 65_536  # Default: 64KB
    CHUNK_SIZE_LARGE_BYTES: int = 524_288  # Large: 512KB
    CHUNK_SIZE_HUGE_BYTES: int = 2_097_152  # Huge: 2MB

    # Memory threshold for chunk size adjustments
    CHUNK_SIZE_MEMORY_THRESHOLD_MB: int = 3000  # 3GB memory threshold

    # Temporary file storage settings
    TEMP_FILE_STORAGE_PATH: str = "downloads"  # Path for temporary file storage
    TEMP_FILE_RETENTION_HOURS: int = 24  # Hours to keep temporary files before cleanup
    TEMP_FILE_CLEANUP_INTERVAL_MINUTES: int = 60  # Minutes between cleanup runs
    TEMP_FILE_MAX_SIZE_MB: int = 100  # Maximum size of temporary files in MB
    TEMP_FILE_EXPORT_MAX_SIZE_MB: int = (
        1000  # Maximum size for export files in MB (larger limit)
    )
    TEMP_FILE_ALLOWED_EXTENSIONS: Union[List[str], str] = [
        "xlsx",
        "csv",
        "json",
        "txt",
        "parquet",
    ]  # Allowed file extensions
    TEMP_FILE_CLEANUP_DOWNLOADED_ONLY: bool = True  # Only cleanup files after download

    @field_validator("TEMP_FILE_ALLOWED_EXTENSIONS", mode="before")
    def assemble_allowed_extensions(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse allowed extensions from string to list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            # Try to parse as JSON
            try:
                import json

                return json.loads(v)
            except:
                # If parsing fails, split by comma
                return [i.strip() for i in v.split(",")]
        return ["xlsx", "csv", "json", "txt"]  # Default value

    # Script execution settings
    ASSORTMENT_OPTIMIZER_TIMEOUT: int = (
        1200  # Timeout for assortment optimizer script in seconds (20 minutes)
    )
    ASSORTMENT_OPTIMIZER_FILE_PREFIX: str = (
        "assortment_optimizer_"  # Prefix for assortment optimizer result files
    )
    ASSORTMENT_OPTIMIZER_DEFAULT_EXTENSION: str = (
        "xlsx"  # Default file extension for assortment optimizer results
    )

    # CP-API settings for assortment optimizer
    API_HOST: str = (
        "https://cp-api.icmr.ru/"  # CP-API host URL for assortment optimizer
    )

    # Monitoring settings
    ENABLE_CONNECTION_POOL_MONITORING: bool = True
    CONNECTION_POOL_MONITORING_INTERVAL: int = 60  # seconds
    CONNECTION_POOL_MONITORING_RETENTION_DAYS: int = 7  # days to keep monitoring data

    # Session settings
    SESSION_TIMEOUT: int = 1800  # 30 minutes in seconds

    # Table mappings for PostgreSQL to ClickHouse conversion
    # This dictionary maps PostgreSQL table names to ClickHouse table names
    # Format: {"pg_schema.pg_table": "ch_database.ch_table"}
    TABLES_MAPPING: Dict[str, str] = {
        "movements_pet": "pet.purchases_1",  # Base mapping without id_panel
        "ctlg_shops_pet": "pet.ctlg_shops",
        "cps.ctlg_shops_pet": "pet.ctlg_shops",
        "household_pet": "pet.household",
        "cps.household_pet": "pet.household",
        "art_product_groups_pet": "pet.ctlg_article_plus",
        "cps.art_product_groups_pet": "pet.ctlg_article_plus",
    }

    def get_table_mapping(self, id_panel: Optional[str] = None) -> Dict[str, str]:
        """
        Get table mapping dictionary, optionally with id_panel applied.

        Args:
            id_panel: Optional ID panel to append to certain table names

        Returns:
            Dictionary mapping PostgreSQL table names to ClickHouse table names
        """
        # Create a copy of the mapping to avoid modifying the original
        mapping = self.TABLES_MAPPING.copy()

        # Apply id_panel to specific tables if provided
        if id_panel:
            # Update specific mappings that need id_panel
            for pg_table, ch_table in self.TABLES_MAPPING.items():
                if pg_table == "movements_pet":
                    mapping[pg_table] = f"{ch_table[:-1]}{id_panel}"

        return mapping

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="allow"
    )


# Create a global settings instance
settings = Settings()
