from clickhouse_connect import get_client

from datetime import datetime
import pandas as pd
from dotenv import load_dotenv
import os
import logging
import sys
from typing import List, Dict, Optional
from .exception import AxisTranslationError
from jinja2 import Environment, FileSystemLoader, select_autoescape
from pathlib import Path

from .cpaapi_client.client import CPAApi_Client, DBEngineEnum
from .util import get_version_from_toml

# Set up Jinja2 environment to load templates from the sql_template directory
template_dir = Path(__file__).parent / "sql_template"
env = Environment(
    loader=FileSystemLoader(template_dir),
    autoescape=select_autoescape(),
    trim_blocks=True,
    lstrip_blocks=True,
)


def raise_helper(msg):
    raise Exception(msg)


env.globals["raise"] = raise_helper

load_dotenv()


# растишка
"""start_date = '2023-01-01'
end_date = '2023-12-31'
json_axis_id = 668  
json_flt_hh_id = 656
total_position_number = 1  # Required 
rest_position_number = -1  # -1 if there is no REST in axis
id_panel = 1
"""


# fem care
start_date = "2024-04-01"
end_date = "2024-10-31"
json_axis_id = 831
json_flt_hh_id = None
total_position_number = 1
rest_position_number = -1
id_panel = 1


"""
# movement axis Ehrman Deligrande test
start_date = "2024-04-01"
end_date = "2024-10-31"
json_axis_id = 794
json_flt_hh_id = None
total_position_number = 1  # Required
rest_position_number = -1  # -1 if there is no REST in axis
id_panel = 9
"""

# zero penetration
"""start_date = '2023-01-01'
end_date = '2023-12-31'
json_axis_id = 794  
json_flt_hh_id = None
total_position_number = 1  # Required 
rest_position_number = -1  # -1 if there is no REST in axis
id_panel = 1
"""

file_handler = logging.FileHandler(filename=f"main.log")
stdout_handler = logging.StreamHandler(stream=sys.stdout)
handlers = [file_handler, stdout_handler]

LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(logging.INFO)
for handler in handlers:
    handler.setFormatter(logging.Formatter("%(asctime)s %(levelname)s: %(message)s"))
    LOGGER.addHandler(handler)


def initialize_cpapi_client():
    API_HOST = f"{os.environ.get('API_HOST', 'https://cp-api.icmr.ru/')}"

    try:
        cpaapi_client = CPAApi_Client(API_HOST, app_name="Assort Optimize")
    except Exception as e:
        LOGGER.error(f"cant connect to CP-API by  {API_HOST}")
        sys.exit()

    return cpaapi_client


def initialize_click_connection():
    click_connection_params = {
        "host": os.environ.get("CLICKHOUSE_HOST"),
        "port": os.environ.get("CLICKHOUSE_PORT"),
        "user": os.environ.get("CLICKHOUSE_USER"),
        "password": os.environ.get("CLICKHOUSE_PASSWORD"),
        "database": os.environ.get("CLICKHOUSE_DATABASE"),
    }

    try:
        # Создаем клиентское соединение
        client = get_client(**click_connection_params)

    except Exception as e:
        LOGGER.error(f"cant connect to ClickHouse using {click_connection_params}")
        sys.exit()

    return client


def build_create_temp_table_query(
    temp_axis_table: str, cte: str, queries: Dict, take_positions: List = []
) -> str:
    if take_positions:
        chunk_queries = [queries[position] for position in take_positions]
    else:
        chunk_queries = [queries[position] for position in list(queries.keys())]

    UNION_query = " \nUNION ALL ".join(chunk_queries)

    create_temp_axis_query = f""
    create_temp_axis_query += f"CREATE TEMPORARY TABLE {temp_axis_table} AS with CTE_TABLE as ( {cte} )  {UNION_query} ;"

    return create_temp_axis_query


def get_axis_code(
    temp_table_name: str,
    cpapi_client: CPAApi_Client,
    json_id_axis: int,
    engine_type: DBEngineEnum,
    id_panel: int,
):
    translation_result = cpapi_client.get_axis_translation(
        id=json_id_axis, engine_type=engine_type, id_panel=id_panel
    )

    cte = ""
    queries = {}
    if "cte" in translation_result:
        cte = translation_result["cte"]
    else:
        raise AxisTranslationError(f"CTE is absent in json axis translation result")

    if "queries" in translation_result:
        queries = translation_result["queries"]
    else:
        raise AxisTranslationError(
            f"List of queries are absent in json axis translation result"
        )

    return build_create_temp_table_query(
        temp_axis_table=temp_table_name, cte=cte, queries=queries, take_positions=[]
    )


def get_article_filter_code(
    cpapi_client: CPAApi_Client, json_article_filter_id: int, engine_type: DBEngineEnum
):
    flta_translation_result = cpapi_client.get_filter_translation(
        id=json_article_filter_id, engine_type=engine_type
    )

    return flta_translation_result["query"]


def get_sql_code(template_filename: str, params: Optional[Dict] = None) -> str:
    template = env.get_template(template_filename)

    query_text = template.render(params)

    return query_text


def create_pre_axis(
    click_client,
    axis_table_name: str,
    axis_type: str,
    position_number_filter: List[int],
    id_panel: int,
    start_date: str,
    end_date: str,
    position_number: str,
):
    # LOGGER.info("Create pre-axis (axis joined with purchase and product filter)")

    pre_axis_code = get_sql_code(
        template_filename="01_create_pre_axis_position_filter.sql",
        params={
            "id_panel": id_panel,
            "temp_axis_name": axis_table_name,
            "axis_type": axis_type,
            "position_number": position_number,
        },
    )

    try:
        click_client.command("DROP TABLE IF EXISTS pre_axis;")
        click_client.command(
            pre_axis_code,
            parameters={
                "start_date": start_date,
                "end_date": end_date,
                "position_number_filter": tuple(position_number_filter),
                "id_panel": id_panel,
            },
        )
    except Exception as e:
        LOGGER.error(e)
        LOGGER.info(pre_axis_code)
        sys.exit()


def create_axis_by_id_trip(click_client):
    # LOGGER.info("Create axis grouped by hhkey, id trip, rwbasis, position number")

    axis_grouped_code = get_sql_code("02_create_axis_grouped_by_id_trip.sql", {})
    try:
        click_client.command("DROP TABLE IF EXISTS axis_grouped;")
        click_client.command(axis_grouped_code)
    except Exception as e:
        LOGGER.error(e)
        LOGGER.info(axis_grouped_code)
        sys.exit()


def create_buyers_final(
    click_client,
    target_tablename: str,
    hh_filter: bool,
    calc_exclusive_penetration: bool,
):
    # LOGGER.info("Create buyers_final table")

    buyers_final_code = get_sql_code(
        "03_create_buyers_final.sql",
        params={
            "buyers_final": target_tablename,
            "hh_filter": hh_filter,
            "calc_exclusive_penetration": calc_exclusive_penetration,
        },
    )
    try:
        click_client.command(f"DROP TABLE IF EXISTS {target_tablename};")
        click_client.command(buyers_final_code, parameters={"excluded_positions": []})
    except Exception as e:
        LOGGER.error(e)
        LOGGER.info(buyers_final_code)
        sys.exit()


def create_rp_data(click_client, buyers_table_name: str, sql_parameters: Dict):
    # LOGGER.info("Create rp_data table")
    create_rp_data_code = get_sql_code(
        template_filename="05_create_rp_data.sql",
        params={"buyers_final": buyers_table_name},
    )

    try:
        click_client.command("DROP TABLE IF EXISTS rp_data_tmp;")
        click_client.command(create_rp_data_code, sql_parameters)
    except Exception as e:
        LOGGER.error(e)
        LOGGER.info(create_rp_data_code)
        sys.exit()


def create_buyers_uplift(click_client, sql_parameters: Dict):
    # LOGGER.info("Create buyers_uplift table")

    buyers_uplift_code = get_sql_code("06_create_buyers_uplift.sql", {})

    try:
        click_client.command("DROP TABLE IF EXISTS buyers_uplift;")
        click_client.command(buyers_uplift_code, sql_parameters)
    except Exception as e:
        LOGGER.error(e)
        LOGGER.info(buyers_uplift_code)
        sys.exit()


def create_final_kpi(
    click_client, buyers_tablename: str, hh_filter: bool, sql_parameters: Dict
):
    final_kpi_rwbasis_code = get_sql_code(
        template_filename="07_create_final_kpi_rwbasis.sql",
        params={"buyers_final": buyers_tablename, "hh_filter": hh_filter},
    )
    # LOGGER.info("Create final_kpi_rwbasis table")
    try:
        click_client.command("DROP TABLE IF EXISTS final_KPI_rwbasis;")
        click_client.command(final_kpi_rwbasis_code, sql_parameters)
    except Exception as e:
        LOGGER.error(e)
        LOGGER.info(final_kpi_rwbasis_code)
        sys.exit()


def main():
    click_client = initialize_click_connection()
    cpapi_client = initialize_cpapi_client()

    calc_assortment_optimizer(
        json_axis_id=json_axis_id,
        json_flt_hh_id=json_flt_hh_id,
        start_date=start_date,
        end_date=end_date,
        total_position_number=total_position_number,
        rest_position_number=rest_position_number,
        id_panel=id_panel,
        click_client=click_client,
        cpapi_client=cpapi_client,
    )


def calc_assortment_optimizer(
    json_axis_id: int,
    json_flt_hh_id: int,
    start_date: str,
    end_date: str,
    total_position_number: int,
    rest_position_number: int,
    id_panel: int,
    click_client,
    cpapi_client: CPAApi_Client,
):
    general_sql_parameters = {
        "start_date": start_date,
        "end_date": end_date,
        "id_panel": id_panel,
    }

    axis_description = cpapi_client.get_object_description(id=json_axis_id)
    if axis_description["object_type"] != "Axis":
        LOGGER.error(
            f"Object with id={json_axis_id} is not an Axis. Object type is {axis_description['object_type']}"
        )
        sys.exit(1)

    axis_type = axis_description["axis_type"]
    if axis_type not in ("axsm", "axsa"):
        LOGGER.error(
            f"Object with id={json_axis_id} is not an Article or Movement Axis. Object type is {axis_type}"
        )
        sys.exit()

    # temp table name for the axis content
    axis_table_name = "ao_axis_temp_table"

    LOGGER.info(f"Getting SQL code for an axis={json_axis_id}")
    try:
        # we use article axis only so don't need pass id_panel for non cluster
        axis_code = get_axis_code(
            temp_table_name=axis_table_name,
            cpapi_client=cpapi_client,
            json_id_axis=json_axis_id,
            engine_type=DBEngineEnum.clickhouse,
            id_panel=id_panel,
        )
    except AxisTranslationError as e:
        LOGGER.error(f"{e}")
        sys.exit()
    except Exception as e:
        LOGGER.error(f"{e}")
        sys.exit()

    # LOGGER.info(f'Create the temp table "{axis_table_name}" based on the axis sql code')
    try:
        click_client.command(axis_code)
    except Exception as e:
        LOGGER.error(f"{e}")
        LOGGER.info(axis_code)
        sys.exit()

    hh_flt_description = None
    if json_flt_hh_id is not None:
        hh_flt_description = cpapi_client.get_object_description(id=json_flt_hh_id)

        LOGGER.info(f"Getting SQL code for HH filter = {json_flt_hh_id}")
        try:
            translation_result = cpapi_client.get_filter_translation(
                id=json_flt_hh_id, engine_type=DBEngineEnum.clickhouse
            )
        except AxisTranslationError as e:
            LOGGER.error(f"{e}")
            sys.exit()
        except Exception as e:
            LOGGER.error(f"{e}")
            sys.exit()

        flth_code = translation_result["query"]
        create_hh_filter_code = get_sql_code(
            template_filename="00_create_hh_filter.sql",
            params={"hh_filter_query": flth_code},
        )

        try:
            click_client.command(
                cmd=create_hh_filter_code, parameters=general_sql_parameters
            )
        except Exception as e:
            LOGGER.error(f"{e}")
            LOGGER.info(create_hh_filter_code)
            sys.exit()

    # get list of an unique positions
    LOGGER.info(f"Get list of unique positions within the axis")
    axis_position_df = click_client.query_df(
        f"SELECT DISTINCT position_number FROM {axis_table_name} ORDER BY position_number"
    )
    axis_position_list = axis_position_df["position_number"].tolist()
    LOGGER.info(f"Positions = {axis_position_list}")

    axis_position_no_total_no_rest_list = axis_position_list.copy()
    if total_position_number in axis_position_list:
        axis_position_no_total_no_rest_list.remove(total_position_number)
    else:
        raise ValueError(
            f"Specified Total position={total_position_number} not found in the axis's positions: {axis_position_list}"
        )

    if rest_position_number in axis_position_no_total_no_rest_list:
        axis_position_no_total_no_rest_list.remove(rest_position_number)

    axis_position_no_total_list = axis_position_list.copy()
    axis_position_no_total_list.remove(total_position_number)

    buyers_total_table_name = "buyers_final_total"
    buyers_flth_table_name = "buyers_final_flth"

    ## --------------------
    ## calc standard penetration (as KPI reporter)

    create_pre_axis(
        click_client=click_client,
        axis_table_name=axis_table_name,
        axis_type=axis_type,
        position_number_filter=axis_position_list,
        id_panel=id_panel,
        start_date=start_date,
        end_date=end_date,
        position_number="position_number",
    )

    create_axis_by_id_trip(click_client=click_client)

    create_buyers_final(
        click_client=click_client,
        target_tablename=buyers_total_table_name,
        hh_filter=False,
        calc_exclusive_penetration=False,
    )

    create_rp_data(
        click_client=click_client,
        buyers_table_name=buyers_total_table_name,
        sql_parameters=general_sql_parameters,
    )

    create_buyers_uplift(
        click_client=click_client, sql_parameters=general_sql_parameters
    )

    if json_flt_hh_id is not None:
        create_buyers_final(
            click_client=click_client,
            target_tablename=buyers_flth_table_name,
            hh_filter=True if json_flt_hh_id is not None else False,
            calc_exclusive_penetration=False,
        )

    # ------------------------------------------------------------
    create_final_kpi(
        click_client=click_client,
        buyers_tablename=buyers_flth_table_name
        if json_flt_hh_id is not None
        else buyers_total_table_name,
        hh_filter=True if json_flt_hh_id is not None else False,
        sql_parameters=general_sql_parameters,
    )

    standard_kpi_df = click_client.query_df(query="SELECT * FROM final_KPI_rwbasis")

    if standard_kpi_df.empty:
        LOGGER.error(f"The axis provided an empty dataset using specified date range!")
        sys.exit()

    standard_pen_df = (
        standard_kpi_df.groupby("position_number")
        .agg({"population": "first", "buyers": "sum", "projectc": "first"})
        .reset_index()
    )

    # Затем вычисляем новые столбцы
    standard_pen_df["buyers_rp"] = (
        standard_pen_df["buyers"] * standard_pen_df["projectc"] / 1000
    )
    standard_pen_df["population"] = (
        standard_pen_df["population"] * standard_pen_df["projectc"] / 1000
    )
    standard_pen_df["penetration"] = (
        standard_pen_df["buyers_rp"] / standard_pen_df["population"]
    )

    total_penetration = standard_pen_df[
        standard_pen_df["position_number"] == total_position_number
    ].iloc[0, standard_pen_df.columns.get_loc("penetration")]
    LOGGER.info(f"Total penetration: {total_penetration:.2f}")

    if total_penetration == 0:
        raise ValueError("Penetration for Total position is 0. We can't continue")

    standard_pen_df["single relative penetration"] = (
        standard_pen_df["penetration"] / total_penetration
    )

    ## --------------------------------------------------

    template_columns = [
        "position_number",
        "buyers_rp",
        "population",
        "projectc",
        "penetration",
    ]
    top_df = pd.DataFrame(columns=template_columns)

    for idx, position_number1 in enumerate(axis_position_no_total_list):
        print(f"Iteration {idx}")

        combination_df = pd.DataFrame(columns=template_columns)

        position_for_loop = axis_position_no_total_no_rest_list
        if idx == len(axis_position_no_total_list) - 1:
            position_for_loop = axis_position_no_total_list

        for idx, position_number in enumerate(position_for_loop):
            # skip the check if we have already selected this position
            if position_number in top_df["position_number"].to_list():
                continue

            # ------------------------------------------------------------
            # here we select rows from 2 positions BUT mark them as the same position (here we use 1 but we may use any number)
            check_positions = top_df["position_number"].tolist()
            check_positions.append(position_number)

            create_pre_axis(
                click_client=click_client,
                axis_table_name=axis_table_name,
                axis_type=axis_type,
                position_number_filter=check_positions,
                id_panel=id_panel,
                start_date=start_date,
                end_date=end_date,
                position_number=1,
            )

            create_axis_by_id_trip(click_client=click_client)

            create_buyers_final(
                click_client=click_client,
                target_tablename=buyers_total_table_name,
                hh_filter=False,
                calc_exclusive_penetration=False,
            )

            create_rp_data(
                click_client=click_client,
                buyers_table_name=buyers_total_table_name,
                sql_parameters=general_sql_parameters,
            )

            create_buyers_uplift(
                click_client=click_client, sql_parameters=general_sql_parameters
            )

            if json_flt_hh_id is not None:
                create_buyers_final(
                    click_client=click_client,
                    target_tablename=buyers_flth_table_name,
                    hh_filter=True if json_flt_hh_id is not None else False,
                    calc_exclusive_penetration=False,
                )

            create_final_kpi(
                click_client=click_client,
                buyers_tablename=buyers_flth_table_name
                if json_flt_hh_id is not None
                else buyers_total_table_name,
                hh_filter=True if json_flt_hh_id is not None else False,
                sql_parameters=general_sql_parameters,
            )

            filtered_kpi_df = click_client.query_df(
                query="SELECT * FROM final_KPI_rwbasis"
            )

            filtered_kpi_df["position_number"] = position_number
            filtered_kpi_df = filtered_kpi_df.drop("position_number", axis=1)

            if not filtered_kpi_df.empty:
                total_buyers = filtered_kpi_df["buyers"].sum()

                first_row = filtered_kpi_df.iloc[0]
                population = first_row["population"]
                projectc = first_row["projectc"]

                # Вычисляем показатели
                buyers_rp = total_buyers * projectc / 1000
                population_adj = population * projectc / 1000
                penetration = buyers_rp / population_adj

                # Создаем итоговый DataFrame с одной строкой
                result = pd.DataFrame(
                    {
                        "position_number": position_number,
                        "buyers_rp": [buyers_rp],
                        "population": [population_adj],
                        "projectc": [projectc],
                        "penetration": [penetration],
                    }
                )

                combination_df = pd.concat(
                    [
                        combination_df.astype(result.dtypes),
                        result.astype(combination_df.dtypes),
                    ]
                )

        combination_df = combination_df.set_index("position_number")
        combination_df["position_number"] = combination_df.index

        max_df = combination_df[
            combination_df.index == combination_df["penetration"].idxmax()
        ]

        # calc exclusive  ----------------------------------------------
        max_position = int(max_df["position_number"].iloc[0])
        rest_but_max_position = axis_position_list.copy()
        rest_but_max_position.remove(max_position)
        rest_but_max_position.remove(total_position_number)

        create_pre_axis(
            click_client=click_client,
            axis_table_name=axis_table_name,
            axis_type=axis_type,
            position_number_filter=rest_but_max_position,
            id_panel=id_panel,
            start_date=start_date,
            end_date=end_date,
            position_number=1,
        )

        create_axis_by_id_trip(click_client=click_client)

        create_buyers_final(
            click_client=click_client,
            target_tablename=buyers_total_table_name,
            hh_filter=False,
            calc_exclusive_penetration=False,
        )

        create_rp_data(
            click_client=click_client,
            buyers_table_name=buyers_total_table_name,
            sql_parameters=general_sql_parameters,
        )

        create_buyers_uplift(
            click_client=click_client, sql_parameters=general_sql_parameters
        )

        if json_flt_hh_id is not None:
            create_buyers_final(
                click_client=click_client,
                target_tablename=buyers_flth_table_name,
                hh_filter=True if json_flt_hh_id is not None else False,
                calc_exclusive_penetration=False,
            )

        create_final_kpi(
            click_client=click_client,
            buyers_tablename=buyers_flth_table_name
            if json_flt_hh_id is not None
            else buyers_total_table_name,
            hh_filter=True if json_flt_hh_id is not None else False,
            sql_parameters=general_sql_parameters,
        )

        filtered_kpi_df = click_client.query_df(query="SELECT * FROM final_KPI_rwbasis")

        filtered_kpi_df["position_number"] = position_number
        filtered_kpi_df = filtered_kpi_df.drop("position_number", axis=1)

        total_buyers = filtered_kpi_df["buyers"].sum()

        first_row = filtered_kpi_df.iloc[0]
        population = first_row["population"]
        projectc = first_row["projectc"]

        # Вычисляем показатели
        buyers_rp = total_buyers * projectc / 1000
        population_adj = population * projectc / 1000
        penetration = buyers_rp / population_adj

        max_df = max_df.copy()
        max_df["exclusive penetration"] = total_penetration - penetration
        max_df["exclusive relative penetration"] = (
            total_penetration - penetration
        ) / total_penetration

        # --------------------------------------------------------------
        top_df = pd.concat([top_df, max_df])

    ## --------------------

    top_df = top_df.rename(columns={"penetration": "cumulative penetration"})

    top_df["cumulative relative penetration"] = (
        top_df["cumulative penetration"] / total_penetration
    )

    top_df["incremental penetration"] = top_df["cumulative penetration"].diff()

    inc_pen_col = top_df.columns.get_loc("incremental penetration")
    top_df.iloc[0, int(inc_pen_col)] = top_df.iloc[
        0, top_df.columns.get_loc("cumulative penetration")
    ]
    top_df["incremental relative penetration"] = (
        top_df["incremental penetration"] / total_penetration
    )

    ## add usual penetration
    top_df = top_df.merge(
        standard_pen_df[
            ["position_number", "penetration", "single relative penetration"]
        ],
        on="position_number",
        how="left",
    )
    top_df["cannibilized relative penetration"] = (
        top_df["single relative penetration"]
        - top_df["incremental relative penetration"]
    )

    # technical column for bulding a chart
    top_df["chart column"] = (
        top_df["cumulative relative penetration"]
        - top_df["incremental relative penetration"]
        - top_df["cannibilized relative penetration"]
    )

    ## --------------------
    top_df["duplicators"] = 0.0

    for i in range(1, len(top_df)):
        top_df.loc[i, "duplicators"] = (
            top_df.loc[i - 1, "single relative penetration"]
            + top_df.loc[i - 1, "duplicators"]
            - top_df.loc[i - 1, "cannibilized relative penetration"]
        )

    ## ------------------------------------------------------------------------

    axis_data = cpapi_client.get_axis_data(json_axis_id)

    # axis_data is expected to be a list of dicts with 'position_number' and 'value_group_name'
    axis_df = pd.DataFrame(axis_data)
    if "position_number" in axis_df.columns and "value_group_name" in axis_df.columns:
        top_df = top_df.merge(
            axis_df[["position_number", "value_group_name"]],
            on="position_number",
            how="left",
        )
    else:
        LOGGER.warning(
            "axis_data does not contain expected columns: position_number, value_group_name"
        )

    top_df = top_df.drop(["population", "projectc"], axis=1)

    output_column_order = [
        "position_number",
        "value_group_name",
        "buyers_rp",
        "penetration",
        "cumulative penetration",
        "cumulative relative penetration",
        "incremental penetration",
        "incremental relative penetration",
        "single relative penetration",
        "cannibilized relative penetration",
        "duplicators",
        "exclusive penetration",
        "exclusive relative penetration",
        "chart column",
    ]

    top_df = top_df[output_column_order]

    print(top_df)

    panel_name = get_panel_name(id_panel, cpapi_client)

    axis_name = f"{axis_description['client_group']}.{axis_description['axis_name']} ({json_axis_id})"
    filter_name = f"{hh_flt_description['client_group'] if hh_flt_description else ''}.{hh_flt_description['filter_name'] if hh_flt_description else ''} ({json_flt_hh_id})"

    general_info = [
        ["Panel name", f"{panel_name} ({id_panel})"],
        ["Period", f"{start_date} - {end_date}"],
        ["Json Axis", axis_name],
        ["Json HH filter", filter_name],
        ["Total position number", total_position_number],
        ["App version", get_version_from_toml()],
        ["TimeStamp:", datetime.now()],
    ]
    general_info_df = pd.DataFrame(general_info, columns=["Info", "Value"])

    axis_info_df = get_axis_info_df(axis_description)

    hh_flt_info_df = (
        get_filter_info_df(hh_flt_description) if hh_flt_description else None
    )

    with pd.ExcelWriter("result.xlsx", engine="xlsxwriter") as writer:
        top_df.to_excel(writer, index=False, sheet_name="Data")

        workbook = writer.book

        data_worksheet = writer.sheets["Data"]

        # Создаем форматы
        dot1_format = workbook.add_format({"num_format": "0.0"})
        percent_format = workbook.add_format({"num_format": "0.0%"})

        data_worksheet.set_column("C:C", None, dot1_format)
        data_worksheet.set_column("D:N", None, percent_format)

        general_info_df.to_excel(writer, index=False, sheet_name="Info", startrow=1)

        info_worksheet = writer.sheets["Info"]
        info_worksheet.write(0, 0, "Report info")

        right_align_format = workbook.add_format()
        right_align_format.set_align("right")

        info_worksheet.set_column("A:A", 30)
        info_worksheet.set_column("B:B", 50, right_align_format)

        info_worksheet.write(10, 0, "Axis info")
        axis_info_df.to_excel(writer, index=False, sheet_name="Info", startrow=11)

        info_worksheet.write(14, 0, "Filter info")
        if (
            hh_flt_description is not None
            and hh_flt_info_df is not None
            and not hh_flt_info_df.empty
        ):
            hh_flt_info_df.to_excel(writer, index=False, sheet_name="Info", startrow=15)


def get_panel_name(id_panel: int, cpapi_client) -> str:
    panel_name = str(id_panel)
    panel_list = cpapi_client.get_panel_list()

    for panel in panel_list:
        if int(panel["id_panel"]) == id_panel:
            panel_name = panel["panel_name"]

    return panel_name


def get_axis_info_df(axis_description: Dict) -> pd.DataFrame:
    axis_info_df = pd.DataFrame(axis_description, index=[0])
    axis_info_df = axis_info_df.drop(
        [
            "definition_json",
            "id_folder",
            "axis_features_used",
            "udf_category",
            "udf_feature",
            "udf_value",
            "udfgroup_category",
            "udfgroup_feature",
            "udfgroup_value",
            "pp_feature",
            "pp_type",
            "pp_value",
            "filter_type",
            "filter_name",
        ],
        axis=1,
    )

    return axis_info_df


def get_filter_info_df(flt_description: Dict) -> pd.DataFrame:
    flt_info_df = pd.DataFrame(flt_description, index=[0])
    flt_info_df = flt_info_df.drop(
        [
            "definition_json",
            "id_folder",
            "axis_features_used",
            "udf_category",
            "udf_feature",
            "udf_value",
            "udfgroup_category",
            "udfgroup_feature",
            "udfgroup_value",
            "pp_feature",
            "pp_type",
            "pp_value",
            "axis_type",
            "axis_name",
        ],
        axis=1,
    )

    return flt_info_df


if __name__ == "__main__":
    main()
