# MagicGateway

A high-performance, secure, and scalable FastAPI application designed to serve as a unified gateway for executing queries against ClickHouse and PostgreSQL databases, with advanced session management, query tracking, and specialized data conversion utilities.

## Features

### Authentication & Authorization
- **LDAP Authentication**: Exclusive authentication method with support for both UPN format (username@domain) for regular users and DN format for service accounts
- **JWT Token-based Access**: Secure token generation with configurable expiration times for both access and refresh tokens

### Database Interaction
- **ClickHouse Integration**:
  - Execute read-only queries with optional write permission
  - Execute administrative commands
  - Session-based connection management
  - Query cancellation support
  - Custom query IDs for tracking

- **PostgreSQL Integration**:
  - Execute read-only queries with optional write permission
  - Execute administrative commands
  - Session-based connection management
  - Query cancellation support

- **Connection Management**:
  - Efficient connection pooling for both databases
  - Configurable connection limits and timeouts
  - Automatic retry and reconnection logic
  - Service connections for administrative operations

### Data Conversion & Utilities
- **PostgreSQL to ClickHouse Conversion**:
  - Convert PostgreSQL views to ClickHouse SQL
  - Support for column name mapping
  - Table name mapping with axis type detection
  - Pretty formatting options
  - Split output mode (CTE and queries separately)

- **View Checker**:
  - Compare row counts between PostgreSQL and ClickHouse
  - Validate data consistency
  - Timeout configuration for long-running operations

### Script Execution
- **Secure Script Runner**:
  - Execute predefined Python scripts with parameter validation
  - Permission-based access control
  - Execution tracking and monitoring

### Observability & Monitoring
- **Request Tracking**:
  - Comprehensive request logging in PostgreSQL
  - Track all database queries associated with API requests
  - Query execution time monitoring
  - Session tracking

- **Logging**:
  - Structured logging with configurable verbosity
  - Log storage in memlog folder
  - Filtered logging to reduce noise from ClickHouse debug messages

- **Administration**:
  - Health check endpoints
  - Request status monitoring
  - Query cancellation capabilities

## Getting Started

### Prerequisites

- Python 3.10 or higher
- Poetry for dependency management
- ClickHouse database
- PostgreSQL database
- LDAP server (required for authentication)

### Installation

1. Clone the repository:
   ```
   git clone https://gitlab.icmr.ru/dev/magicgateway.git
   cd magicgateway
   ```

2. Install dependencies with Poetry:
   ```
   poetry install
   ```

3. Copy `.env.example` to `.env` and configure environment variables:
   ```
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Run the application:
   ```
   poetry run uvicorn src.magic_gateway.main:app --reload
   ```

### Docker Deployment

Alternatively, you can use Docker Compose:

```
docker-compose up -d
```

## API Documentation

Once the application is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Key Endpoints

### Authentication
- `POST /api/v1/auth/login` - Obtain JWT tokens
- `POST /api/v1/auth/refresh` - Refresh access token

### ClickHouse
- `POST /api/v1/clickhouse/query` - Execute ClickHouse queries
- `POST /api/v1/clickhouse/command` - Execute ClickHouse commands

### PostgreSQL
- `POST /api/v1/postgres/query` - Execute PostgreSQL queries
- `POST /api/v1/postgres/command` - Execute PostgreSQL commands

### Scripts
- `GET /api/v1/scripts/` - List available scripts
- `POST /api/v1/scripts/run/{script_name}` - Run a specific script
- `POST /api/v1/scripts/pg-to-clickhouse` - Convert PostgreSQL object to ClickHouse SQL
- `POST /api/v1/scripts/pg-to-ch-view-checker` - Check PostgreSQL to ClickHouse view conversion

### Admin
- `GET /api/v1/admin/health` - Check application health
- `GET /api/v1/admin/requests` - List active requests
- `DELETE /api/v1/admin/requests/{request_id}` - Cancel a request

## Development

### Project Structure

```
├── certs/               # SSL certificates
├── docs/                # Documentation
├── examples/            # Example usage scripts
├── memlog/              # Log storage
├── scripts/             # Utility scripts
├── src/                 # Source code
│   └── magic_gateway/
│       ├── api/         # API endpoints and middleware
│       ├── auth/        # Authentication and authorization
│       ├── core/        # Core application components
│       ├── db/          # Database handlers and connection managers
│       ├── scripts/     # Script execution system
│       ├── tracking/    # Request tracking
│       └── utils/       # Utility functions
└── tests/               # Test suite
```

### Running Tests

```
poetry run pytest
```

### Code Formatting

```
poetry run black .
poetry run isort .
```

### Type Checking

```
poetry run mypy .
```

## Configuration

The application is configured through environment variables. See `.env.example` for all available options.

Key configuration categories:
- Application settings
- Server settings
- Security settings
- LDAP authentication
- PostgreSQL connection
- ClickHouse connection
- Query settings
- Session management

## License

This project is licensed under the MIT License - see the LICENSE file for details.
