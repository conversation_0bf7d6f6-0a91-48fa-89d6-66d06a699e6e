import httpx
from enum import Enum
from typing import Dict, List

class DBEngineEnum(Enum):
    postgresql = 'PostgreSQL'
    clickhouse = 'ClickHouse'


class CPAApi_Client:

    def __init__(self, api_host:str, app_name:str):
        self.api_host = api_host
        self.app_name = app_name
        self._client = self._get_client()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._client.close()
        
    

    def _get_client(self) -> httpx.Client:

        headers = {
            'user-agent': self.app_name
        }

        client = httpx.Client(
            base_url=self.api_host,
            headers=headers,
            verify=False
        )

        return client

    def close(self):
        self._client.close()
    

    def get_axis_translation(self, id:int, engine_type:DBEngineEnum, id_panel:int) -> Dict:

        """Get SQL code for specified axis"""

        endpoint = f'/v1/json_object/axis/{id}/translation/{engine_type.value}?id_panel={id_panel}'        

        response = self._client.get(endpoint)

        if response.status_code ==  httpx.codes.OK:
            return response.json()
        else:
            raise ValueError(f"{response.status_code} {response.text}")

    def get_filter_translation(self, id:int, engine_type:DBEngineEnum, id_panel:int) -> Dict:

        """Get SQL code for specified filter"""

        endpoint = f'/v1/json_object/filter/{id}/translation/{engine_type.value}?id_panel={id_panel}'        

        response = self._client.get(endpoint)

        if response.status_code ==  httpx.codes.OK:
            return response.json()
        else:
            raise ValueError(f"{response.status_code} {response.text}")

    def get_axis_data(self, id:int) -> List:

        """Get SQL code for specified filter"""

        endpoint = f'/v1/json_object/axis/{id}/axisdata'        

        response = self._client.get(endpoint)

        if response.status_code ==  httpx.codes.OK:
            return response.json()
        else:
            raise ValueError(f"{response.status_code} {response.text}")


    def get_object_description(self, id:int) -> Dict:
        """Get description for specified object"""

        endpoint = f'/v1/json_object/object/{id}/description'        

        response = self._client.get(endpoint)

        if response.status_code ==  httpx.codes.OK:
            return response.json()
        else:
            raise ValueError(f"{response.status_code} {response.text}")
    
    def get_panel_list(self) -> List[Dict]:

        endpoint = f'/v1/catalog/metadata_panels?engine_type=PostgreSQL'        

        response = self._client.get(endpoint)

        if response.status_code ==  httpx.codes.OK:
            return response.json()
        else:
            raise ValueError(f"{response.status_code} {response.text}")