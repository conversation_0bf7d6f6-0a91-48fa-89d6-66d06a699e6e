"""Models for monitoring connection pools and other system metrics."""

from datetime import datetime
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field


class ConnectionPoolMetrics(BaseModel):
    """Model for connection pool metrics."""

    timestamp: datetime = Field(default_factory=lambda: datetime.now())
    pool_type: str  # 'postgres', 'clickhouse', 'logs'
    active_connections: int
    idle_connections: int
    min_size: int
    max_size: int
    total_connections_created: int = 0
    total_connections_closed: int = 0
    connection_timeouts: int = 0
    connection_errors: int = 0
    avg_acquisition_time_ms: Optional[float] = None
    max_acquisition_time_ms: Optional[float] = None
    avg_usage_time_ms: Optional[float] = None
    max_usage_time_ms: Optional[float] = None
    additional_metrics: Dict[str, Any] = Field(default_factory=dict)
    # Script-specific metrics
    script_usage_stats: Dict[str, Any] = Field(
        default_factory=dict
    )  # Script usage statistics


class ConnectionPoolStatus(BaseModel):
    """Model for connection pool status in health check responses."""

    pool_type: str
    active_connections: int
    idle_connections: int
    min_size: int
    max_size: int
    utilization_percent: float
    status: str  # 'healthy', 'warning', 'critical'
    last_updated: datetime
    acquisition_time_ms: Optional[float] = None
    usage_time_ms: Optional[float] = None
    timeouts: Optional[int] = None
    errors: Optional[int] = None
    # Script-specific status information
    script_usage_summary: Dict[str, Any] = Field(
        default_factory=dict
    )  # Summary of script usage
