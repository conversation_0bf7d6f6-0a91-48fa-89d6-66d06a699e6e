#!/usr/bin/env python
"""
Example script demonstrating the PostgreSQL to ClickHouse conversion API endpoint.
"""

import json
import sys
import os
import logging
import requests

# Add the parent directory to the path so we can import the api_client
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from examples.api_client import MagicGatewayClient

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def print_json(data):
    """Print JSON data in a readable format."""
    print(json.dumps(data, indent=2))


class PgToClickHouseExample:
    """Example class for PostgreSQL to ClickHouse conversion."""

    def __init__(self):
        """Initialize the example."""
        self.client = MagicGatewayClient()
        # Use default credentials from environment or hardcoded test credentials
        self.client.authenticate(username="rnvaga", password="!Newupgrade1")

    def convert_view_to_clickhouse(
        self,
        object_name,
        object_type=None,
        pretty_format=True,
        output_mode="normal",
        output_format="full",
        id_panel=None,
        timeout_seconds=None,
    ):
        """Convert a PostgreSQL view to ClickHouse SQL."""
        print(f"\n=== Converting {object_name} to ClickHouse SQL ===")

        conversion_url = f"{self.client.base_url}/api/v1/scripts/pg-to-clickhouse"
        conversion_data = {
            "object_name": object_name,
            "object_type": object_type,
            "pretty_format": pretty_format,
            "output_mode": output_mode,
            "output_format": output_format,
        }

        # Add id_panel if provided
        if id_panel:
            conversion_data["id_panel"] = id_panel
            print(f"Using panel ID: {id_panel} for table mapping")

        # Add timeout if provided
        if timeout_seconds:
            conversion_data["timeout_seconds"] = timeout_seconds
            print(f"Using custom timeout of {timeout_seconds} seconds")

        try:
            print(f"Sending request to convert {object_name}...")
            # Add timeout to prevent hanging indefinitely
            try:
                response = self.client._make_authenticated_request(
                    "POST", conversion_url, json=conversion_data, timeout=10
                )
                print("Response received.")
                result = response.json()
                print("Conversion successful!")
            except requests.exceptions.Timeout:
                print(
                    "Request timed out after 10 seconds. The server might be processing a complex view."
                )
                return None

            print("\n=== Original PostgreSQL DDL ===")
            print(result["original_ddl"])

            print("\n=== ClickHouse SQL ===")
            print(result["clickhouse_sql"])

            # Display CTE and Queries parts if available
            if "cte_part" in result and result["cte_part"]:
                print("\n=== CTE Part ===")
                print(result["cte_part"])

            if "queries_part" in result and result["queries_part"]:
                print("\n=== Queries Part ===")
                print(result["queries_part"])

            # Display output format information
            if output_format != "full":
                print(f"\n=== Output Format: {output_format} ===")

            return result
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to convert to ClickHouse: {e}")
            print(f"Error: {e}")
            return None


def main():
    """Run the example."""
    try:
        # Initialize the example
        print("Initializing PgToClickHouse example...")
        print(
            "Note: This example requires a running MagicGateway server with the PostgreSQL to ClickHouse conversion endpoint implemented."
        )
        print(
            "The example will timeout after 10 seconds if the server takes too long to respond."
        )
        print(
            "\nYou can modify the script to use different PostgreSQL objects or adjust the timeout."
        )

        example = PgToClickHouseExample()

        # Check if authentication was successful
        if not example.client.access_token:
            print("\nAuthentication failed. Please check your credentials.")
            print(
                "This example requires a running MagicGateway server and valid credentials."
            )
            print(
                "You can modify the script to use your own credentials or set up test users."
            )
            return

        print("\nAuthentication successful!")

        # Example 1: Convert a view with normal output mode and shorter timeout
        print(
            "\nRunning Example 1: Convert a view with normal output mode and 30s timeout"
        )
        example.convert_view_to_clickhouse(
            "cmt_international_cluster_1.axsm_pg_detergents_ecom_v1",
            "view",
            timeout_seconds=30,  # Use a shorter timeout to avoid long waits
        )

        # Example 2: Convert a view with split output mode
        print(
            "\nRunning Example 2: Convert a view with split output mode and 30s timeout"
        )
        example.convert_view_to_clickhouse(
            "cmt_international_cluster_1.axsm_pg_detergents_ecom_v1",
            "view",
            output_mode="split",
            timeout_seconds=30,  # Use a shorter timeout to avoid long waits
        )

        # Example 3: Convert a view with panel ID for table mapping
        print("\nRunning Example 3: Convert a view with panel ID '1' for table mapping")
        example.convert_view_to_clickhouse(
            "cmt_international_cluster_1.axsm_pg_detergents_ecom_v1",
            "view",
            id_panel="1",
            pretty_format=False,
            timeout_seconds=30,  # Use a shorter timeout to avoid long waits
        )

        # Example 4: Convert a view with CTE-only output format
        print("\nRunning Example 4: Convert a view with CTE-only output format")
        example.convert_view_to_clickhouse(
            "cmt_international_cluster_1.axsm_pg_detergents_ecom_v1",
            "view",
            output_format="cte_only",
            timeout_seconds=30,  # Use a shorter timeout to avoid long waits
        )

        # Example 5: Convert a view with queries-only output format
        print("\nRunning Example 5: Convert a view with queries-only output format")
        example.convert_view_to_clickhouse(
            "cmt_international_cluster_1.axsm_pg_detergents_ecom_v1",
            "view",
            output_format="queries_only",
            timeout_seconds=30,  # Use a shorter timeout to avoid long waits
        )

    except Exception as e:
        print(f"\nAn error occurred: {e}")
        print("\nThis example requires:")
        print("1. A running MagicGateway server")
        print("2. Valid authentication credentials")
        print("3. The PostgreSQL to ClickHouse conversion endpoint to be implemented")


if __name__ == "__main__":
    print("Starting PgToClickHouse example script...")
    main()
    print("Example script completed.")
