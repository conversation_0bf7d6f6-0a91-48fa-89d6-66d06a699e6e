"""
Export optimization module for MagicGateway.

This module provides optimized export functionality for job results,
consolidating connection management and streamlining the export workflow.
"""

from .models import (
    ExportContext,
    ExportOptions,
    ConnectionSelection,
    ExportFormat,
    ExcelLayout,
    TempFileStrategy,
    JobMetadata,
    SizeEstimate,
    OptimizationStrategy,
    ConversionOptions,
)
from .exceptions import (
    ExportError,
    ConnectionPoolExhaustedError,
    ConnectionTimeoutError,
    TableNotFoundError,
    FormatConversionError,
    InsufficientResourcesError,
    ExportValidationError,
    ExcelLimitExceededError,
    MetadataRetrievalError,
    StreamingDataError,
)
from .adapters import (
    ConnectionPoolAdapter,
)
from .handlers import (
    OptimizedExportHandler,
)

__all__ = [
    "ExportContext",
    "ExportOptions", 
    "ConnectionSelection",
    "ExportFormat",
    "ExcelLayout",
    "TempFileStrategy",
    "JobMetadata",
    "SizeEstimate",
    "OptimizationStrategy",
    "ConversionOptions",
    "ExportError",
    "ConnectionPoolExhaustedError",
    "ConnectionTimeoutError",
    "TableNotFoundError",
    "FormatConversionError",
    "InsufficientResourcesError",
    "ExportValidationError",
    "ExcelLimitExceededError",
    "MetadataRetrievalError",
    "StreamingDataError",
    "ConnectionPoolAdapter",
    "OptimizedExportHandler",
]