"""Script runner for the MagicGateway application."""

import asyncio
import importlib
import inspect
import os
import sys
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable, Set, Tuple

from pydantic import BaseModel, create_model, ValidationError

from magic_gateway.core.exceptions import ScriptExecutionException
from magic_gateway.core.logging_config import log
from magic_gateway.utils.temp_file_manager import temp_file_manager
from magic_gateway.monitoring.script_context import set_script_context

# Import custom parameter models
try:
    from magic_gateway.api.v1.models import (
        AssortmentOptimizerParams,
        AssortmentOptimizerResponse,
    )
except ImportError:
    AssortmentOptimizerParams = None
    AssortmentOptimizerResponse = None


@dataclass
class ScriptConnectionManagers:
    """Container for database connection managers available to scripts."""

    clickhouse_primary: Optional[Any] = None
    clickhouse_cluster: Optional[Any] = None
    postgres: Optional[Any] = None
    logs: Optional[Any] = None


class ScriptMetadata(BaseModel):
    """Metadata for a script."""

    name: str
    description: str
    author: Optional[str] = None
    version: Optional[str] = None
    requires_admin: bool = False


class ScriptResult(BaseModel):
    """Result of a script execution."""

    script_id: str
    script_name: str
    start_time: datetime
    end_time: datetime
    execution_time: float
    result: Any
    error: Optional[str] = None


class ScriptRunner:
    """Runner for predefined Python scripts."""

    def __init__(self):
        """Initialize the script runner."""
        self._scripts: Dict[str, Dict[str, Any]] = {}
        self._script_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "definitions"
        )
        self._loaded = False

    async def load_scripts(self) -> None:
        """Load all scripts from the definitions directory."""
        if self._loaded:
            return

        log.info("Loading scripts...")

        # Add script directory to path
        if self._script_dir not in sys.path:
            sys.path.append(self._script_dir)

        # Get all Python files in the directory
        script_files = [
            f[:-3]
            for f in os.listdir(self._script_dir)
            if f.endswith(".py") and not f.startswith("__")
        ]

        # Load each script
        for script_name in script_files:
            try:
                # Import the module
                module = importlib.import_module(script_name)

                # Check if module has required attributes
                if not hasattr(module, "run") or not callable(module.run):
                    log.warning(f"Script {script_name} does not have a run function")
                    continue

                if not hasattr(module, "METADATA") or not isinstance(
                    module.METADATA, dict
                ):
                    log.warning(f"Script {script_name} does not have METADATA")
                    continue

                # Create metadata
                try:
                    metadata = ScriptMetadata(
                        name=module.METADATA.get("name", script_name),
                        description=module.METADATA.get("description", ""),
                        author=module.METADATA.get("author"),
                        version=module.METADATA.get("version"),
                        requires_admin=module.METADATA.get("requires_admin", False),
                    )
                except ValidationError as e:
                    log.warning(f"Invalid metadata for script {script_name}: {e}")
                    continue

                # Get parameter model
                param_model = self._get_parameter_model(module.run, script_name)

                # Store script info
                self._scripts[script_name] = {
                    "module": module,
                    "metadata": metadata,
                    "param_model": param_model,
                }

                log.info(f"Loaded script: {metadata.name} ({script_name})")
            except Exception as e:
                log.error(f"Failed to load script {script_name}: {e}")

        self._loaded = True
        log.info(f"Loaded {len(self._scripts)} scripts")

        # Schedule automatic cleanup of old temporary files
        await self._schedule_cleanup()

    def _get_parameter_model(self, func: Callable, script_name: str) -> Optional[type]:
        """
        Create a Pydantic model for script parameters.

        Args:
            func: Script function
            script_name: Name of the script

        Returns:
            Pydantic model class for parameters
        """
        # Check for custom parameter models first
        if script_name == "assortment_optimizer" and AssortmentOptimizerParams:
            log.info(f"Using custom parameter model for script: {script_name}")
            return AssortmentOptimizerParams

        # Get function signature for auto-generated model
        sig = inspect.signature(func)

        # Create field definitions
        fields = {}
        for name, param in sig.parameters.items():
            # Skip self parameter
            if name == "self":
                continue

            # Get type annotation
            annotation = param.annotation
            if annotation is inspect.Parameter.empty:
                annotation = Any

            # Get default value
            default = ...  # Required field
            if param.default is not inspect.Parameter.empty:
                default = param.default

            # Add field
            fields[name] = (annotation, default)

        # Create model
        model_name = f"{script_name.capitalize()}Params"
        return create_model(model_name, **fields)

    async def get_available_scripts(self) -> List[Dict[str, Any]]:
        """
        Get all available scripts.

        Returns:
            List of script metadata
        """
        if not self._loaded:
            await self.load_scripts()

        return [
            {
                "name": script_name,
                "metadata": script_info["metadata"].model_dump(),
                "parameters": self._get_parameter_info(script_info["param_model"]),
            }
            for script_name, script_info in self._scripts.items()
        ]

    async def get_script_info(self, script_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific script.

        Args:
            script_name: Name of the script

        Returns:
            Script information or None if not found
        """
        if not self._loaded:
            await self.load_scripts()

        if script_name not in self._scripts:
            return None

        script_info = self._scripts[script_name]
        return {
            "name": script_name,
            "metadata": script_info["metadata"].model_dump(),
            "parameters": self._get_parameter_info(script_info["param_model"]),
        }

    def _get_parameter_info(self, model: type) -> List[Dict[str, Any]]:
        """
        Get parameter information from a Pydantic model.

        Args:
            model: Pydantic model class

        Returns:
            List of parameter information
        """
        if not model:
            return []

        # Handle Pydantic v2 model fields
        if hasattr(model, "model_fields"):
            # Pydantic v2
            return [
                {
                    "name": name,
                    "type": str(field.annotation),
                    "required": field.is_required(),
                    "default": field.default if not field.is_required() else None,
                    "description": field.description,
                }
                for name, field in model.model_fields.items()
            ]
        elif hasattr(model, "__fields__"):
            # Pydantic v1 (fallback)
            return [
                {
                    "name": name,
                    "type": str(field.annotation),
                    "required": field.required,
                    "default": field.default if not field.required else None,
                    "description": field.description,
                }
                for name, field in model.__fields__.items()
            ]
        else:
            return []

    async def run_script(
        self,
        script_name: str,
        params: Dict[str, Any],
        script_id: Optional[str] = None,
        timeout_seconds: Optional[int] = None,
        connection_managers: Optional[ScriptConnectionManagers] = None,
    ) -> ScriptResult:
        """
        Run a script.

        Args:
            script_name: Name of the script to run
            params: Parameters for the script
            script_id: Optional ID for the script execution
            timeout_seconds: Optional timeout in seconds for script execution
            connection_managers: Optional connection managers to inject into script context

        Returns:
            Script execution result
        """
        if not self._loaded:
            await self.load_scripts()

        # Check if script exists
        if script_name not in self._scripts:
            raise ScriptExecutionException(f"Script not found: {script_name}")

        script_info = self._scripts[script_name]

        # Generate script ID if not provided
        if not script_id:
            script_id = str(uuid.uuid4())

        # Set timeout from settings if not provided
        if timeout_seconds is None:
            from magic_gateway.core.config import settings

            timeout_seconds = getattr(settings, "MAX_QUERY_EXECUTION_TIME", 300)

        # Log script execution start
        log.info(
            f"Starting script execution: {script_name} (ID: {script_id}) with timeout: {timeout_seconds}s"
        )

        # Store original params for connection manager injection
        original_params = params.copy()

        # Validate parameters (excluding connection managers)
        if script_info["param_model"]:
            try:
                # Only validate user-provided parameters, not injected connection managers
                user_params = {
                    k: v
                    for k, v in params.items()
                    if k
                    not in [
                        "clickhouse_manager",
                        "clickhouse_cluster_manager",
                        "postgres_manager",
                        "logs_manager",
                    ]
                }
                validated_params = script_info["param_model"](**user_params)
                params = validated_params.model_dump()
                log.info(f"Parameters validated successfully for script: {script_name}")
            except ValidationError as e:
                log.error(f"Parameter validation failed for script {script_name}: {e}")
                validation_errors = {}

                # Extract detailed validation errors
                for error in e.errors():
                    field = ".".join(str(loc) for loc in error["loc"])
                    validation_errors[field] = error["msg"]

                raise ScriptExecutionException(
                    f"Invalid parameters: {e}",
                    details={"validation_errors": validation_errors},
                )

        # Inject connection managers into script parameters if provided
        # This happens AFTER validation to avoid validation errors
        if connection_managers:
            log.info(f"Injecting connection managers into script: {script_name}")

            # Add connection managers to the parameters that will be passed to the script
            if connection_managers.clickhouse_primary:
                params["clickhouse_manager"] = connection_managers.clickhouse_primary
                log.debug(
                    f"Injected primary ClickHouse manager for script: {script_name}"
                )

            if connection_managers.clickhouse_cluster:
                params["clickhouse_cluster_manager"] = (
                    connection_managers.clickhouse_cluster
                )
                log.debug(
                    f"Injected cluster ClickHouse manager for script: {script_name}"
                )

            if connection_managers.postgres:
                params["postgres_manager"] = connection_managers.postgres
                log.debug(f"Injected PostgreSQL manager for script: {script_name}")

            if connection_managers.logs:
                params["logs_manager"] = connection_managers.logs
                log.debug(f"Injected logs PostgreSQL manager for script: {script_name}")
        else:
            log.debug(f"No connection managers provided for script: {script_name}")

        # Run the script
        start_time = datetime.utcnow()
        error = None
        result = None

        try:
            # Run the script in a separate thread to avoid blocking, with timeout
            try:
                # Create a wrapper function that sets the script context
                def run_with_context():
                    with set_script_context(script_name):
                        return script_info["module"].run(**params)

                # Create a task for the script execution
                task = asyncio.create_task(asyncio.to_thread(run_with_context))

                # Wait for the task to complete with timeout
                result = await asyncio.wait_for(task, timeout=timeout_seconds)

            except asyncio.TimeoutError:
                # Handle timeout
                log.error(
                    f"Script execution timed out after {timeout_seconds}s: {script_name} (ID: {script_id})"
                )
                error = f"Script execution timed out after {timeout_seconds} seconds"
                # Try to cancel the task if it's still running
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        log.info(
                            f"Script task cancelled: {script_name} (ID: {script_id})"
                        )
                    except Exception as cancel_error:
                        log.warning(f"Error during task cancellation: {cancel_error}")

        except Exception as e:
            import traceback

            error = str(e)
            log.error(f"Script execution failed: {script_name} (ID: {script_id}): {e}")
            log.error(f"Traceback: {traceback.format_exc()}")

        end_time = datetime.utcnow()
        execution_time = (end_time - start_time).total_seconds()

        # Handle file generation for scripts that return file information
        result_file_id = None
        download_url = None

        if result and isinstance(result, dict) and not error:
            log.info(f"Script result: {result}")
            # Check if the script result contains file information
            if "result_file_id" in result:
                result_file_id = result["result_file_id"]
                download_url = result.get("download_url")
                log.info(
                    f"Extracted result_file_id: {result_file_id}, download_url: {download_url}"
                )

                # Verify the file exists in TempFileManager
                if result_file_id:
                    file_path = temp_file_manager.get_file_path(result_file_id)
                    if file_path:
                        log.info(
                            f"Script generated file: {result_file_id} at {file_path}"
                        )
                    else:
                        log.warning(
                            f"Script returned file_id {result_file_id} but file not found in TempFileManager"
                        )

                        # Fallback: try to find the file by expected filename pattern
                        expected_filename = (
                            f"assortment_optimizer_{result_file_id}.xlsx"
                        )
                        expected_path = (
                            temp_file_manager.base_directory / expected_filename
                        )

                        if expected_path.exists():
                            log.info(
                                f"Found file by expected filename: {expected_path}"
                            )
                            # Register the file in the mapping
                            try:
                                temp_file_manager._store_file_mapping(
                                    result_file_id, expected_filename
                                )
                                log.info(
                                    f"Successfully registered file mapping for {result_file_id}"
                                )

                                # Verify it works now
                                file_path = temp_file_manager.get_file_path(
                                    result_file_id
                                )
                                if file_path:
                                    log.info(
                                        f"File mapping recovery successful: {file_path}"
                                    )
                                else:
                                    log.error(
                                        f"File mapping recovery failed for {result_file_id}"
                                    )
                                    result_file_id = None
                                    download_url = None
                            except Exception as e:
                                log.error(f"Failed to register file mapping: {e}")
                                result_file_id = None
                                download_url = None
                        else:
                            log.error(
                                f"File not found even by expected filename: {expected_path}"
                            )
                            result_file_id = None
                            download_url = None
                else:
                    log.warning(
                        f"Script result contains result_file_id key but value is None or empty"
                    )
            else:
                log.warning(
                    f"Script result does not contain result_file_id key. Keys: {list(result.keys()) if result else 'None'}"
                )
        else:
            log.warning(
                f"Script result is not a valid dict or has error. Result: {result}, Error: {error}"
            )

        # Create appropriate result based on script type
        if script_name == "assortment_optimizer" and AssortmentOptimizerResponse:
            # Use enhanced response model for assortment optimizer
            log.info(
                f"Using AssortmentOptimizerResponse for script: {script_name}, file_id: {result_file_id}"
            )
            script_result = AssortmentOptimizerResponse(
                script_id=script_id,
                script_name=script_name,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                result=result,
                error=error,
                result_file_id=result_file_id,
                download_url=download_url,
            )
        else:
            # Use standard result for other scripts
            log.info(
                f"Using ScriptResult for script: {script_name}, AssortmentOptimizerResponse available: {AssortmentOptimizerResponse is not None}"
            )
            script_result = ScriptResult(
                script_id=script_id,
                script_name=script_name,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                result=result,
                error=error,
            )

        log.info(
            f"Script execution completed: {script_name} (ID: {script_id}), "
            f"execution time: {execution_time:.2f}s"
        )

        return script_result

    async def _schedule_cleanup(self) -> None:
        """Schedule automatic cleanup of old temporary files."""
        try:
            # Perform initial cleanup
            cleaned_count = temp_file_manager.cleanup_old_files(max_age_hours=24)
            if cleaned_count > 0:
                log.info(f"Initial cleanup removed {cleaned_count} old temporary files")
        except Exception as e:
            log.error(f"Failed to perform initial cleanup: {e}")

    async def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        Manually trigger cleanup of old temporary files.

        Args:
            max_age_hours: Maximum age in hours before files are deleted

        Returns:
            Number of files cleaned up
        """
        try:
            return temp_file_manager.cleanup_old_files(max_age_hours)
        except Exception as e:
            log.error(f"Failed to cleanup temporary files: {e}")
            return 0


# Create global instance of script runner
script_runner = ScriptRunner()
