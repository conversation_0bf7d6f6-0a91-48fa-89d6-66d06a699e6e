"""Integration tests for the API."""

import pytest
from httpx import Async<PERSON><PERSON>

from magic_gateway.core.config import settings


@pytest.mark.asyncio
async def test_root_endpoint(async_client: AsyncClient):
    """Test the root endpoint."""
    # Act
    response = await async_client.get("/")
    
    # Assert
    assert response.status_code == 200
    assert response.json()["name"] == settings.APP_NAME
    assert response.json()["version"] == settings.APP_VERSION


@pytest.mark.asyncio
async def test_health_check(async_client: AsyncClient):
    """Test the health check endpoint."""
    # Act
    response = await async_client.get("/api/v1/admin/health")
    
    # Assert
    assert response.status_code == 200
    assert "status" in response.json()
    assert "version" in response.json()
    assert "timestamp" in response.json()


@pytest.mark.asyncio
async def test_unauthorized_access(async_client: AsyncClient):
    """Test unauthorized access to protected endpoints."""
    # Arrange
    endpoints = [
        "/api/v1/clickhouse/query",
        "/api/v1/postgres/query",
        "/api/v1/scripts/",
        "/api/v1/admin/queries",
    ]
    
    # Act & Assert
    for endpoint in endpoints:
        response = await async_client.get(endpoint)
        assert response.status_code == 401, f"Endpoint {endpoint} should require authentication"


@pytest.mark.asyncio
async def test_authorized_access(async_client: AsyncClient, admin_token: str):
    """Test authorized access to protected endpoints."""
    # Arrange
    headers = {"Authorization": f"Bearer {admin_token}"}
    
    # Act
    response = await async_client.get("/api/v1/scripts/", headers=headers)
    
    # Assert
    assert response.status_code == 200
    assert isinstance(response.json(), list)
