graph TD
    %% MagicGateway Complete Architecture and Workflow
    
    %% === CLIENT LAYER ===
    subgraph "Client Layer"
        CLIENT[Client Applications]
        SYNC_CLIENT[MagicGatewayClient<br/>Synchronous]
        ASYNC_CLIENT[AsyncMagicGatewayClient<br/>Asynchronous]
        
        CLIENT --> SYNC_CLIENT
        CLIENT --> ASYNC_CLIENT
    end
    
    %% === API GATEWAY LAYER ===
    subgraph "API Gateway Layer"
        FASTAPI[FastAPI Application<br/>main.py]
        CORS[CORS Middleware]
        TOKEN_REFRESH[Token Refresh Middleware]
        REQUEST_TRACKING[Request Tracking Middleware]
        STATIC[Static Files Mount]
        
        CORS --> TOKEN_REFRESH
        TOKEN_REFRESH --> REQUEST_TRACKING
        REQUEST_TRACKING --> FASTAPI
        FASTAPI --> STATIC
    end
    
    %% === API ROUTING LAYER ===
    subgraph "API v1 Routing"
        API_ROUTER[API Router<br/>/api/v1]
        AUTH_ROUTER[Auth Router<br/>/auth]
        CH_ROUTER[ClickHouse Router<br/>/clickhouse]
        PG_ROUTER[PostgreSQL Router<br/>/postgres]
        SCRIPTS_ROUTER[Scripts Router<br/>/scripts]
        ADMIN_ROUTER[Admin Router<br/>/admin]
        
        API_ROUTER --> AUTH_ROUTER
        API_ROUTER --> CH_ROUTER
        API_ROUTER --> PG_ROUTER
        API_ROUTER --> SCRIPTS_ROUTER
        API_ROUTER --> ADMIN_ROUTER
    end
    
    %% === AUTHENTICATION LAYER ===
    subgraph "Authentication System"
        LDAP_AUTH[LDAP Authentication<br/>ldap_auth.py]
        JWT_HANDLER[JWT Token Handler<br/>jwt.py]
        AUTH_DEPS[Auth Dependencies<br/>dependencies.py]
        
        LDAP_AUTH --> JWT_HANDLER
        JWT_HANDLER --> AUTH_DEPS
    end
    
    %% === CORE SYSTEM LAYER ===
    subgraph "Core System"
        CONFIG[Configuration<br/>config.py]
        LIFESPAN[Application Lifespan<br/>lifespan.py]
        LOGGING[Logging Config<br/>logging_config.py]
        EXCEPTIONS[Custom Exceptions<br/>exceptions.py]
        
        CONFIG --> LIFESPAN
        LOGGING --> LIFESPAN
    end
    
    %% === DATABASE CONNECTION LAYER ===
    subgraph "Database Connection Management"
        CONN_MANAGER[Connection Manager<br/>connection_manager.py]
        PG_CONN_MGR[PostgreSQL Connection Manager]
        CH_CONN_MGR[ClickHouse Connection Manager]
        CH_REGISTRY[ClickHouse Registry<br/>Multi-cluster Support]
        LOGS_CONN_MGR[Logs Connection Manager]
        
        CONN_MANAGER --> PG_CONN_MGR
        CONN_MANAGER --> CH_CONN_MGR
        CONN_MANAGER --> LOGS_CONN_MGR
        CH_CONN_MGR --> CH_REGISTRY
    end
    
    %% === DATABASE HANDLERS LAYER ===
    subgraph "Database Handlers"
        PG_HANDLER[PostgreSQL Handler<br/>postgres_handler.py]
        CH_HANDLER[ClickHouse Handler<br/>clickhouse_handler.py]
        LOGS_HANDLER[Logs Handler<br/>logs_handler.py]
        DB_MODELS[Database Models<br/>models.py]
        
        PG_HANDLER --> DB_MODELS
        CH_HANDLER --> DB_MODELS
        LOGS_HANDLER --> DB_MODELS
    end
    
    %% === EXPORT SYSTEM ===
    subgraph "Export System"
        EXPORT_HANDLER[Optimized Export Handler<br/>optimized_export_handler.py]
        CONN_POOL_ADAPTER[Connection Pool Adapter<br/>connection_pool.py]
        FORMAT_PIPELINE[Format Conversion Pipeline<br/>format_conversion.py]
        RESOURCE_MANAGER[Export Resource Manager<br/>manager.py]
        EXPORT_MODELS[Export Models<br/>models.py]
        EXPORT_EXCEPTIONS[Export Exceptions<br/>exceptions.py]
        
        EXPORT_HANDLER --> CONN_POOL_ADAPTER
        EXPORT_HANDLER --> FORMAT_PIPELINE
        EXPORT_HANDLER --> RESOURCE_MANAGER
        FORMAT_PIPELINE --> EXPORT_MODELS
        EXPORT_HANDLER --> EXPORT_EXCEPTIONS
    end
    
    %% === UTILITIES LAYER ===
    subgraph "Utilities & Utils"
        TEMP_FILE_MGR[Temp File Manager<br/>temp_file_manager.py]
        STREAMING_EXCEL[Streaming Excel Writer<br/>streaming_excel_writer.py]
        PARQUET_PROCESSOR[Parquet Processor<br/>parquet_processor.py]
        ASYNC_EXPORT[Async Export Utils<br/>async_export.py]
        DB_UTILS[Database Utils<br/>db_utils.py]
        INFO_SHEET_GEN[Info Sheet Generator<br/>info_sheet_generator.py]
        
        TEMP_FILE_MGR --> STREAMING_EXCEL
        PARQUET_PROCESSOR --> FORMAT_PIPELINE
        ASYNC_EXPORT --> EXPORT_HANDLER
    end
    
    %% === SCRIPT EXECUTION SYSTEM ===
    subgraph "Script Execution System"
        SCRIPT_RUNNER[Script Runner<br/>runner.py]
        ASYNC_BRIDGE[Async Bridge<br/>async_bridge.py]
        PG_TO_CH_CHECKER[PG to CH View Checker<br/>pg_to_ch_view_checker.py]
        SCRIPT_DEFINITIONS[Script Definitions<br/>definitions/]
        
        SCRIPT_RUNNER --> ASYNC_BRIDGE
        SCRIPT_RUNNER --> PG_TO_CH_CHECKER
        SCRIPT_RUNNER --> SCRIPT_DEFINITIONS
    end
    
    %% === MONITORING & TRACKING ===
    subgraph "Monitoring & Tracking"
        MONITORING_SERVICE[Monitoring Service<br/>service.py]
        SCRIPT_CONTEXT[Script Context<br/>script_context.py]
        TRACKING_SERVICE[Request Tracking Service<br/>tracking/service.py]
        TRACKING_MODELS[Tracking Models<br/>tracking/models.py]
        
        MONITORING_SERVICE --> SCRIPT_CONTEXT
        TRACKING_SERVICE --> TRACKING_MODELS
    end
    
    %% === EXTERNAL DATABASES ===
    subgraph "External Databases"
        POSTGRES_DB[(PostgreSQL Database<br/>Primary Data)]
        CLICKHOUSE_DB[(ClickHouse Database<br/>Analytics)]
        CLICKHOUSE_CLUSTER[(ClickHouse Cluster<br/>Secondary)]
        LOGS_DB[(Logs Database<br/>Request Tracking)]
        LDAP_SERVER[(LDAP Server<br/>Authentication)]
    end
    
    %% === DOCKER & DEPLOYMENT ===
    subgraph "Deployment"
        DOCKER[Docker Container<br/>Dockerfile]
        COMPOSE[Docker Compose<br/>compose.yml]
        POETRY[Poetry Dependencies<br/>pyproject.toml]
        
        DOCKER --> COMPOSE
        POETRY --> DOCKER
    end
    
    %% === CLIENT CONNECTIONS ===
    SYNC_CLIENT -.-> FASTAPI
    ASYNC_CLIENT -.-> FASTAPI
    
    %% === MIDDLEWARE FLOW ===
    FASTAPI --> CORS
    
    %% === ROUTING CONNECTIONS ===
    FASTAPI --> API_ROUTER
    
    %% === AUTHENTICATION FLOW ===
    AUTH_ROUTER --> LDAP_AUTH
    AUTH_ROUTER --> JWT_HANDLER
    
    %% === DATABASE CONNECTIONS ===
    PG_ROUTER --> PG_HANDLER
    CH_ROUTER --> CH_HANDLER
    SCRIPTS_ROUTER --> SCRIPT_RUNNER
    ADMIN_ROUTER --> MONITORING_SERVICE
    
    %% === CONNECTION MANAGEMENT ===
    PG_HANDLER --> PG_CONN_MGR
    CH_HANDLER --> CH_CONN_MGR
    LOGS_HANDLER --> LOGS_CONN_MGR
    
    %% === DATABASE CONNECTIONS TO EXTERNAL ===
    PG_CONN_MGR --> POSTGRES_DB
    CH_CONN_MGR --> CLICKHOUSE_DB
    CH_REGISTRY --> CLICKHOUSE_CLUSTER
    LOGS_CONN_MGR --> LOGS_DB
    LDAP_AUTH --> LDAP_SERVER
    
    %% === EXPORT WORKFLOW ===
    CH_ROUTER --> EXPORT_HANDLER
    PG_ROUTER --> EXPORT_HANDLER
    EXPORT_HANDLER --> CH_HANDLER
    CONN_POOL_ADAPTER --> CH_REGISTRY
    
    %% === SCRIPT EXECUTION FLOW ===
    SCRIPT_RUNNER --> PG_CONN_MGR
    SCRIPT_RUNNER --> CH_CONN_MGR
    SCRIPT_RUNNER --> LOGS_CONN_MGR
    
    %% === MONITORING CONNECTIONS ===
    REQUEST_TRACKING --> TRACKING_SERVICE
    TRACKING_SERVICE --> LOGS_HANDLER
    MONITORING_SERVICE --> CONN_MANAGER
    
    %% === LIFECYCLE MANAGEMENT ===
    LIFESPAN --> CONN_MANAGER
    LIFESPAN --> TRACKING_SERVICE
    LIFESPAN --> MONITORING_SERVICE
    LIFESPAN --> TEMP_FILE_MGR
    
    %% === CONFIGURATION FLOW ===
    CONFIG --> CONN_MANAGER
    CONFIG --> LDAP_AUTH
    CONFIG --> JWT_HANDLER
    
    %% === UTILITY CONNECTIONS ===
    EXPORT_HANDLER --> TEMP_FILE_MGR
    FORMAT_PIPELINE --> STREAMING_EXCEL
    FORMAT_PIPELINE --> PARQUET_PROCESSOR
    
    %% === STYLING ===
    classDef clientLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef apiLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef authLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef coreLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dbLayer fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef exportLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef utilLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef scriptLayer fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef monitorLayer fill:#e8eaf6,stroke:#1a237e,stroke-width:2px
    classDef externalLayer fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef deployLayer fill:#f9fbe7,stroke:#827717,stroke-width:2px
    
    class CLIENT,SYNC_CLIENT,ASYNC_CLIENT clientLayer
    class FASTAPI,CORS,TOKEN_REFRESH,REQUEST_TRACKING,STATIC,API_ROUTER,AUTH_ROUTER,CH_ROUTER,PG_ROUTER,SCRIPTS_ROUTER,ADMIN_ROUTER apiLayer
    class LDAP_AUTH,JWT_HANDLER,AUTH_DEPS authLayer
    class CONFIG,LIFESPAN,LOGGING,EXCEPTIONS coreLayer
    class CONN_MANAGER,PG_CONN_MGR,CH_CONN_MGR,CH_REGISTRY,LOGS_CONN_MGR,PG_HANDLER,CH_HANDLER,LOGS_HANDLER,DB_MODELS dbLayer
    class EXPORT_HANDLER,CONN_POOL_ADAPTER,FORMAT_PIPELINE,RESOURCE_MANAGER,EXPORT_MODELS,EXPORT_EXCEPTIONS exportLayer
    class TEMP_FILE_MGR,STREAMING_EXCEL,PARQUET_PROCESSOR,ASYNC_EXPORT,DB_UTILS,INFO_SHEET_GEN utilLayer
    class SCRIPT_RUNNER,ASYNC_BRIDGE,PG_TO_CH_CHECKER,SCRIPT_DEFINITIONS scriptLayer
    class MONITORING_SERVICE,SCRIPT_CONTEXT,TRACKING_SERVICE,TRACKING_MODELS monitorLayer
    class POSTGRES_DB,CLICKHOUSE_DB,CLICKHOUSE_CLUSTER,LOGS_DB,LDAP_SERVER externalLayer
    class DOCKER,COMPOSE,POETRY deployLayer