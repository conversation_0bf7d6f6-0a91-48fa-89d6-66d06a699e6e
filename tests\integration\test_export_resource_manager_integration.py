"""
Integration tests for ExportResourceManager with TempFileManager.

Tests the integration between ExportResourceManager and the actual TempFileManager
to ensure consistent temporary file handling across all export formats.
"""

import asyncio
import tempfile
from pathlib import Path
import pytest

from magic_gateway.export.resources.manager import ExportResourceManager, export_resource_context
from magic_gateway.utils.temp_file_manager import TempFileManager


class TestExportResourceManagerIntegration:
    """Integration test cases for ExportResourceManager with TempFileManager."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def temp_file_manager(self, temp_dir):
        """Create a TempFileManager instance for testing."""
        return TempFileManager(base_directory=str(temp_dir))

    @pytest.fixture
    def resource_manager(self, temp_file_manager):
        """Create an ExportResourceManager with real TempFileManager."""
        return ExportResourceManager(temp_file_manager=temp_file_manager)

    def test_integration_with_real_temp_file_manager(self, resource_manager, temp_file_manager):
        """Test ExportResourceManager integration with real TempFileManager."""
        # Create a file using TempFileManager directly
        test_content = "test content for integration"
        file_id = temp_file_manager.save_result_file(
            content=test_content,
            extension="txt",
            prefix="integration_test_"
        )
        
        # Verify file was created
        file_path = temp_file_manager.get_file_path(file_id)
        assert file_path is not None
        assert file_path.exists()
        
        # Register the file with ExportResourceManager
        resource_id = resource_manager.register_temp_file(
            file_path=file_path,
            file_id=file_id
        )
        
        # Verify resource is tracked
        assert resource_manager.resource_count == 1
        assert resource_manager.get_temp_file_path(resource_id) == file_path
        
        # Verify file content
        with open(file_path, 'r') as f:
            content = f.read()
        assert content == test_content

    def test_create_file_via_resource_manager(self, resource_manager, temp_file_manager):
        """Test creating files through ExportResourceManager using TempFileManager."""
        test_content = "content created via resource manager"
        
        # Create file through resource manager
        resource_id = resource_manager.register_temp_file_from_manager(
            content=test_content,
            extension="csv",
            prefix="export_test_"
        )
        
        # Verify resource is tracked
        assert resource_manager.resource_count == 1
        
        # Get file path and verify it exists
        file_path = resource_manager.get_temp_file_path(resource_id)
        assert file_path is not None
        assert file_path.exists()
        
        # Verify file content
        with open(file_path, 'r') as f:
            content = f.read()
        assert content == test_content
        
        # Verify file is also accessible through TempFileManager
        resources = resource_manager.list_resources()
        file_id = resources[resource_id]["file_id"]
        tfm_file_path = temp_file_manager.get_file_path(file_id)
        assert tfm_file_path == file_path

    @pytest.mark.asyncio
    async def test_cleanup_integration(self, resource_manager, temp_file_manager):
        """Test cleanup integration between ExportResourceManager and TempFileManager."""
        # Create multiple files
        resource_ids = []
        file_paths = []
        
        for i in range(3):
            content = f"test content {i}"
            resource_id = resource_manager.register_temp_file_from_manager(
                content=content,
                extension="txt",
                prefix=f"cleanup_test_{i}_"
            )
            resource_ids.append(resource_id)
            file_paths.append(resource_manager.get_temp_file_path(resource_id))
        
        # Verify all files exist
        assert resource_manager.resource_count == 3
        for file_path in file_paths:
            assert file_path.exists()
        
        # Cleanup all resources
        await resource_manager.cleanup_all()
        
        # Verify cleanup
        assert resource_manager.resource_count == 0
        assert resource_manager.is_cleaned_up
        
        # Note: Files may still exist on disk as TempFileManager handles its own cleanup
        # but they should be marked as downloaded/processed in TempFileManager

    @pytest.mark.asyncio
    async def test_context_manager_integration(self, temp_file_manager):
        """Test async context manager integration with TempFileManager."""
        file_paths = []
        
        async with ExportResourceManager(temp_file_manager) as manager:
            # Create files within context
            for i in range(2):
                resource_id = manager.register_temp_file_from_manager(
                    content=f"context test {i}",
                    extension="json",
                    prefix=f"context_test_{i}_"
                )
                file_paths.append(manager.get_temp_file_path(resource_id))
            
            # Verify files exist during context
            assert manager.resource_count == 2
            for file_path in file_paths:
                assert file_path.exists()
        
        # After context exit, manager should be cleaned up
        assert manager.is_cleaned_up
        assert manager.resource_count == 0

    @pytest.mark.asyncio
    async def test_export_resource_context_integration(self, temp_file_manager):
        """Test export_resource_context factory with TempFileManager integration."""
        created_files = []
        
        async with export_resource_context(temp_file_manager) as manager:
            # Create files using the factory context
            for format_ext in ["csv", "xlsx", "json"]:
                resource_id = manager.register_temp_file_from_manager(
                    content=f"export data for {format_ext}",
                    extension=format_ext,
                    prefix=f"export_{format_ext}_"
                )
                file_path = manager.get_temp_file_path(resource_id)
                created_files.append(file_path)
                
                # Verify file content matches expected format
                with open(file_path, 'r') as f:
                    content = f.read()
                assert f"export data for {format_ext}" in content
            
            assert manager.resource_count == 3
        
        # Verify cleanup after context
        assert manager.is_cleaned_up

    def test_temp_file_manager_settings_integration(self, temp_dir):
        """Test that ExportResourceManager respects TempFileManager settings."""
        # Create TempFileManager with specific settings
        tfm = TempFileManager(base_directory=str(temp_dir))
        tfm.allowed_extensions = ["txt", "csv"]  # Restrict extensions
        tfm.max_file_size_mb = 1  # Small size limit
        
        resource_manager = ExportResourceManager(temp_file_manager=tfm)
        
        # Test allowed extension
        resource_id = resource_manager.register_temp_file_from_manager(
            content="small content",
            extension="txt"
        )
        assert resource_manager.resource_count == 1
        
        # Test that TempFileManager settings are respected
        file_path = resource_manager.get_temp_file_path(resource_id)
        assert file_path.suffix == ".txt"

    def test_multiple_resource_managers_same_temp_file_manager(self, temp_file_manager):
        """Test multiple ExportResourceManager instances sharing the same TempFileManager."""
        manager1 = ExportResourceManager(temp_file_manager)
        manager2 = ExportResourceManager(temp_file_manager)
        
        # Create files with both managers
        resource_id1 = manager1.register_temp_file_from_manager(
            content="manager 1 content",
            extension="txt",
            prefix="mgr1_"
        )
        
        resource_id2 = manager2.register_temp_file_from_manager(
            content="manager 2 content",
            extension="txt",
            prefix="mgr2_"
        )
        
        # Verify both managers track their own resources
        assert manager1.resource_count == 1
        assert manager2.resource_count == 1
        
        # Verify files are accessible through both managers and TempFileManager
        file_path1 = manager1.get_temp_file_path(resource_id1)
        file_path2 = manager2.get_temp_file_path(resource_id2)
        
        assert file_path1 != file_path2
        assert file_path1.exists()
        assert file_path2.exists()
        
        # Verify content
        with open(file_path1, 'r') as f:
            assert f.read() == "manager 1 content"
        with open(file_path2, 'r') as f:
            assert f.read() == "manager 2 content"

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, temp_file_manager):
        """Test error handling integration between components."""
        resource_manager = ExportResourceManager(temp_file_manager)
        
        # Test handling of TempFileManager errors
        with pytest.raises(ValueError, match="Content size"):
            # Try to create a file that's too large (assuming size limits)
            large_content = "x" * (200 * 1024 * 1024)  # 200MB content
            resource_manager.register_temp_file_from_manager(
                content=large_content,
                extension="txt"
            )
        
        # Verify resource manager state is consistent after error
        assert resource_manager.resource_count == 0
        assert not resource_manager.is_cleaned_up

    def test_file_info_integration(self, resource_manager, temp_file_manager):
        """Test file information retrieval integration."""
        # Create a file
        test_content = "file info test content"
        resource_id = resource_manager.register_temp_file_from_manager(
            content=test_content,
            extension="txt",
            prefix="info_test_"
        )
        
        # Get file info through resource manager
        file_path = resource_manager.get_temp_file_path(resource_id)
        assert file_path is not None
        
        # Get file info through TempFileManager
        resources = resource_manager.list_resources()
        file_id = resources[resource_id]["file_id"]
        tfm_info = temp_file_manager.get_file_info(file_id)
        
        # Verify consistency
        assert tfm_info is not None
        assert tfm_info["filename"] == file_path.name
        assert tfm_info["exists"] is True
        assert tfm_info["size_bytes"] == len(test_content.encode('utf-8'))

    @pytest.mark.asyncio
    async def test_concurrent_operations_integration(self, temp_file_manager):
        """Test concurrent operations between ExportResourceManager and TempFileManager."""
        async def create_and_cleanup_resources(manager_id: int):
            """Create resources and clean them up."""
            async with ExportResourceManager(temp_file_manager) as manager:
                resource_ids = []
                for i in range(3):
                    resource_id = manager.register_temp_file_from_manager(
                        content=f"concurrent test {manager_id}-{i}",
                        extension="txt",
                        prefix=f"concurrent_{manager_id}_{i}_"
                    )
                    resource_ids.append(resource_id)
                
                # Verify all resources are tracked
                assert manager.resource_count == 3
                
                # Verify all files exist
                for resource_id in resource_ids:
                    file_path = manager.get_temp_file_path(resource_id)
                    assert file_path.exists()
                
                return len(resource_ids)
        
        # Run multiple concurrent operations
        tasks = [create_and_cleanup_resources(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        # Verify all operations completed successfully
        assert all(result == 3 for result in results)